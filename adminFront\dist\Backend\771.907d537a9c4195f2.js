"use strict";(self.webpackChunkngx_admin_master_v16=self.webpackChunkngx_admin_master_v16||[]).push([[771],{6771:(cn,Q,c)=>{c.r(Q),c.d(Q,{HouseholdManagementModule:()=>an});var p=c(177),q=c(5595),r=c(6142),$=c(3743),B=c(6717),C=c(1427),g=c(467),E=c(6250),y=c(8141),L=c(274),K=c(4701),T=c(7586),A=function(s){return s[s.\u5c1a\u672a\u958b\u59cb=0]="\u5c1a\u672a\u958b\u59cb",s[s.\u5df2\u95b1\u8b80\u64cd\u4f5c\u8aaa\u660e=1]="\u5df2\u95b1\u8b80\u64cd\u4f5c\u8aaa\u660e",s[s.\u9078\u6a23\u5b8c\u6210=2]="\u9078\u6a23\u5b8c\u6210",s[s.\u7c3d\u7f72\u5b8c\u6210=3]="\u7c3d\u7f72\u5b8c\u6210",s}(A||{}),V=c(1329),O=function(s){return s[s.\u672a\u4ed8\u6b3e=0]="\u672a\u4ed8\u6b3e",s[s.\u5df2\u4ed8\u6b3e=1]="\u5df2\u4ed8\u6b3e",s[s.\u7121\u9808\u4ed8\u6b3e=2]="\u7121\u9808\u4ed8\u6b3e",s}(O||{}),X=function(s){return s[s.\u5df2\u7c3d\u56de=1]="\u5df2\u7c3d\u56de",s[s.\u672a\u7c3d\u56de=2]="\u672a\u7c3d\u56de",s}(X||{}),H=function(s){return s[s.\u5f85\u5831\u50f9=1]="\u5f85\u5831\u50f9",s[s.\u5df2\u5831\u50f9=2]="\u5df2\u5831\u50f9",s[s.\u5df2\u7c3d\u56de=3]="\u5df2\u7c3d\u56de",s}(H||{}),f=c(8066),b=c(907),_=function(s){return s[s.\u5ba2\u8b8a\u9700\u6c42=1]="\u5ba2\u8b8a\u9700\u6c42",s[s.\u81ea\u5b9a\u7fa9=2]="\u81ea\u5b9a\u7fa9",s[s.\u9078\u6a23=3]="\u9078\u6a23",s}(_||{}),e=c(4438),I=c(7207),U=c(3273),M=c(1029),P=c(3824),h=c(5654),Z=c(2437),v=c(5482),ee=c(2866),k=c(6354),N=c(4506),te=c(6681),ne=c(1626);let oe=(()=>{class s{constructor(t,o){this.apiQuotationService=t,this.http=o,this.apiUrl="/api/Quotation"}mapEnumToCQuotationType(t){switch(t){case N.P.$1:return _.\u5ba2\u8b8a\u9700\u6c42;case N.P.$3:return _.\u9078\u6a23;default:return _.\u81ea\u5b9a\u7fa9}}getQuotationList(t){return this.apiQuotationService.apiQuotationGetListPost$Json({body:t})}getQuotationData(t){return this.apiQuotationService.apiQuotationGetDataPost$Json({body:{CQuotationID:t}})}getQuotationByHouseId(t){return this.apiQuotationService.apiQuotationGetListByHouseIdPost$Json({body:{CHouseID:t}}).pipe((0,k.T)(n=>n&&{StatusCode:n.StatusCode,Message:n.Message,TotalItems:n.TotalItems,Entries:n.Entries}))}saveQuotationItem(t){return this.apiQuotationService.apiQuotationSaveDataPost$Json({body:t})}saveQuotation(t){const o=t.items.map(i=>({CItemName:i.cItemName,CUnit:i.cUnit||"",CUnitPrice:i.cUnitPrice,CCount:i.cCount,CStatus:i.cStatus||1,CQuotationItemType:i.CQuotationItemType&&i.CQuotationItemType>0?i.CQuotationItemType:_.\u81ea\u5b9a\u7fa9,CRemark:i.cRemark||""})),n={CHouseID:t.houseId,CQuotationVersionId:t.quotationId||0,Items:o,CShowOther:t.cShowOther||!1,COtherName:t.cOtherName||"",COtherPercent:t.cOtherPercent||0};return console.log("QuotationService saveRequest:",n),this.apiQuotationService.apiQuotationSaveDataPost$Json({body:n}).pipe((0,k.T)(i=>({success:0===i?.StatusCode,message:0===i?.StatusCode?"\u5831\u50f9\u55ae\u4fdd\u5b58\u6210\u529f":"\u5831\u50f9\u55ae\u4fdd\u5b58\u5931\u6557",data:t.items})))}getDefaultQuotationItems(){return this.apiQuotationService.apiQuotationGetListPost$Json({body:{PageIndex:0,PageSize:100}}).pipe((0,k.T)(o=>{const n=(o.Entries||[]).map(i=>({cHouseID:i.CHouseID||0,cItemName:i.CItemName||"",cUnit:i.CUnit||"",cUnitPrice:i.CUnitPrice||0,cCount:i.CCount||0,cStatus:i.CStatus||1,CQuotationItemType:this.mapEnumToCQuotationType(i.CQuotationItemType),cRemark:i.CRemark||""}));return{success:0===o.StatusCode,message:o.Message||"",data:n}}))}loadDefaultItems(t){return this.apiQuotationService.apiQuotationLoadDefaultItemsPost$Json({body:t}).pipe((0,k.T)(o=>{const n=(o.Entries||[]).map(i=>({cHouseID:i.CHouseID||0,cItemName:i.CItemName||"",cUnit:i.CUnit||"",cUnitPrice:i.CUnitPrice||0,cCount:i.CCount||0,cStatus:i.CStatus||1,CQuotationItemType:this.mapEnumToCQuotationType(i.CQuotationItemType),cRemark:i.CRemark||""}));return{success:0===o.StatusCode,message:o.Message||"",data:n}}))}loadRegularItems(t){return this.apiQuotationService.apiQuotationLoadRegularItemsPost$Json({body:t}).pipe((0,k.T)(o=>{const n=(o.Entries||[]).map(i=>({cHouseID:i.CHouseID||0,cItemName:i.CItemName||"",cUnit:i.CUnit||"",cUnitPrice:i.CUnitPrice||0,cCount:i.CCount||0,cStatus:i.CStatus||1,CQuotationItemType:this.mapEnumToCQuotationType(i.CQuotationItemType),cRemark:i.CRemark||""}));return{success:0===o.StatusCode,message:o.Message||"",data:n}}))}updateQuotationItem(t,o){return this.apiQuotationService.apiQuotationSaveDataPost$Json({body:{CHouseID:o.cHouseID,CQuotationVersionId:t,Items:[{CItemName:o.cItemName,CUnitPrice:o.cUnitPrice,CCount:o.cCount,CStatus:o.cStatus||1,CQuotationItemType:o.CQuotationItemType||_.\u81ea\u5b9a\u7fa9}]}}).pipe((0,k.T)(i=>({success:0===i.StatusCode,message:i.Message||"",data:[o]})))}exportQuotation(t){throw new Error("Export quotation functionality needs to be implemented separately")}lockQuotation(t){return this.apiQuotationService.apiQuotationLockQuotationPost$Json({body:t}).pipe((0,k.T)(o=>({success:0===o.StatusCode,message:o.Message||"",data:o.Entries||null})))}static{this.\u0275fac=function(o){return new(o||s)(e.KVO(te.q),e.KVO(ne.Qq))}}static{this.\u0275prov=e.jDH({token:s,factory:s.\u0275fac,providedIn:"root"})}}return s})();var d=c(9417),R=c(3391),j=c(7932),w=c(8584);const ie=["fileInput"];function se(s,l){if(1&s&&(e.j41(0,"nb-option",56),e.EFF(1),e.k0s()),2&s){const t=l.$implicit;e.Y8G("value",t),e.R7$(),e.SpI(" ",t.CBuildCaseName," ")}}function ae(s,l){if(1&s&&(e.j41(0,"nb-option",56),e.EFF(1),e.k0s()),2&s){const t=l.$implicit;e.Y8G("value",t),e.R7$(),e.SpI(" ",t.label," ")}}function le(s,l){if(1&s&&(e.j41(0,"nb-option",56),e.EFF(1),e.k0s()),2&s){const t=l.$implicit;e.Y8G("value",t),e.R7$(),e.SpI(" ",t.label," ")}}function ue(s,l){if(1&s&&(e.j41(0,"nb-option",56),e.EFF(1),e.k0s()),2&s){const t=l.$implicit;e.Y8G("value",t),e.R7$(),e.SpI(" ",t.label," ")}}function re(s,l){if(1&s&&(e.j41(0,"nb-option",56),e.EFF(1),e.k0s()),2&s){const t=l.$implicit;e.Y8G("value",t),e.R7$(),e.SpI(" ",t.label," ")}}function ce(s,l){if(1&s&&(e.j41(0,"nb-option",56),e.EFF(1),e.k0s()),2&s){const t=l.$implicit;e.Y8G("value",t),e.R7$(),e.SpI(" ",t.label," ")}}function de(s,l){if(1&s&&(e.j41(0,"nb-option",56),e.EFF(1),e.k0s()),2&s){const t=l.$implicit;e.Y8G("value",t),e.R7$(),e.SpI(" ",t.label," ")}}function pe(s,l){if(1&s&&(e.j41(0,"nb-option",56),e.EFF(1),e.k0s()),2&s){const t=l.$implicit;e.Y8G("value",t),e.R7$(),e.SpI(" ",t.label," ")}}function me(s,l){if(1&s){const t=e.RV6();e.j41(0,"button",42),e.bIt("click",function(){e.eBV(t);const n=e.XpG(),i=e.sdS(117);return e.Njj(n.openModel(i))}),e.nrm(1,"i",57),e.EFF(2,"\u6279\u6b21\u65b0\u589e\u6236\u5225\u8cc7\u6599 "),e.k0s()}}function he(s,l){if(1&s){const t=e.RV6();e.j41(0,"button",66),e.bIt("click",function(){e.eBV(t);const n=e.XpG().$implicit,i=e.XpG(),a=e.sdS(115);return e.Njj(i.openModelDetail(a,n))}),e.nrm(1,"i",67),e.EFF(2,"\u7de8\u8f2f "),e.k0s()}}function ge(s,l){if(1&s){const t=e.RV6();e.j41(0,"tr")(1,"td"),e.EFF(2),e.k0s(),e.j41(3,"td"),e.EFF(4),e.k0s(),e.j41(5,"td"),e.EFF(6),e.k0s(),e.j41(7,"td"),e.EFF(8),e.k0s(),e.j41(9,"td"),e.EFF(10),e.k0s(),e.j41(11,"td"),e.EFF(12),e.k0s(),e.j41(13,"td"),e.EFF(14),e.k0s(),e.j41(15,"td"),e.EFF(16),e.k0s(),e.j41(17,"td"),e.EFF(18),e.k0s(),e.j41(19,"td",58),e.DNE(20,he,3,0,"button",59),e.j41(21,"button",60),e.bIt("click",function(){const n=e.eBV(t).$implicit,i=e.XpG();return e.Njj(i.onNavidateBuildCaseIdHouseId("customer-change-picture",i.searchQuery.CBuildCaseSelected.cID,n.CID))}),e.nrm(22,"i",61),e.EFF(23,"\u6d3d\u8ac7\u7d00\u9304 "),e.k0s(),e.j41(24,"button",60),e.bIt("click",function(){const n=e.eBV(t).$implicit,i=e.XpG();return e.Njj(i.onNavidateBuildCaseIdHouseId("sample-selection-result",i.searchQuery.CBuildCaseSelected.cID,n.CID))}),e.nrm(25,"i",62),e.EFF(26,"\u5ba2\u8b8a\u78ba\u8a8d\u5716\u8aaa "),e.k0s(),e.j41(27,"button",60),e.bIt("click",function(){const n=e.eBV(t).$implicit,i=e.XpG();return e.Njj(i.onNavidateBuildCaseIdHouseId("finaldochouse_management",i.searchQuery.CBuildCaseSelected.cID,n.CID))}),e.nrm(28,"i",63),e.EFF(29,"\u7c3d\u7f72\u6587\u4ef6\u6b77\u7a0b "),e.k0s(),e.j41(30,"button",60),e.bIt("click",function(){const n=e.eBV(t).$implicit,i=e.XpG();return e.Njj(i.resetSecureKey(n))}),e.nrm(31,"i",64),e.EFF(32,"\u91cd\u7f6e\u5bc6\u78bc "),e.k0s(),e.j41(33,"button",60),e.bIt("click",function(){const n=e.eBV(t).$implicit,i=e.XpG(),a=e.sdS(119);return e.Njj(i.openQuotation(a,n))}),e.nrm(34,"i",65),e.EFF(35,"\u5831\u50f9\u55ae "),e.k0s()()()}if(2&s){const t=l.$implicit,o=e.XpG();e.R7$(2),e.JRh(t.CHouseHold),e.R7$(2),e.JRh(t.CFloor),e.R7$(2),e.Lme(" ",2===t.CHouseType?"\u92b7\u552e\u6236":""," ",1===t.CHouseType?"\u5730\u4e3b\u6236":""," "),e.R7$(2),e.JRh(!0===t.CIsChange?"\u5ba2\u8b8a":!1===t.CIsChange?"\u6a19\u6e96":""),e.R7$(2),e.JRh(t.CProgressName),e.R7$(2),e.E5c(" ",0===t.CPayStatus?"\u672a\u4ed8\u6b3e":""," ",1===t.CPayStatus?"\u5df2\u4ed8\u6b3e":""," ",2===t.CPayStatus?"\u7121\u9808\u4ed8\u6b3e":""," "),e.R7$(2),e.JRh(0===t.CSignStatus||null==t.CSignStatus?"\u672a\u7c3d\u56de":"\u5df2\u7c3d\u56de"),e.R7$(2),e.JRh(o.getQuotationStatusText(t.CQuotationStatus)),e.R7$(2),e.JRh(t.CIsEnable?"\u555f\u7528":"\u505c\u7528"),e.R7$(2),e.Y8G("ngIf",o.isUpdate)}}function _e(s,l){if(1&s&&(e.j41(0,"nb-option",56),e.EFF(1),e.k0s()),2&s){const t=l.$implicit;e.Y8G("value",t),e.R7$(),e.SpI(" ",t.CBuildCaseName," ")}}function Ce(s,l){if(1&s&&(e.j41(0,"nb-option",56),e.EFF(1),e.k0s()),2&s){const t=l.$implicit;e.Y8G("value",t),e.R7$(),e.SpI(" ",t.label," ")}}function fe(s,l){if(1&s&&(e.j41(0,"nb-option",56),e.EFF(1),e.k0s()),2&s){const t=l.$implicit;e.Y8G("value",t),e.R7$(),e.SpI(" ",t.label," ")}}function be(s,l){if(1&s){const t=e.RV6();e.j41(0,"div",74)(1,"label",103),e.EFF(2," \u4ed8\u6b3e\u72c0\u614b "),e.k0s(),e.j41(3,"nb-select",104),e.mxI("ngModelChange",function(n){e.eBV(t);const i=e.XpG(3);return e.DH7(i.detailSelected.CPayStatusSelected,n)||(i.detailSelected.CPayStatusSelected=n),e.Njj(n)}),e.DNE(4,fe,2,2,"nb-option",14),e.k0s()()}if(2&s){const t=e.XpG(3);e.R7$(3),e.R50("ngModel",t.detailSelected.CPayStatusSelected),e.R7$(),e.Y8G("ngForOf",t.options.payStatusOptions)}}function Fe(s,l){if(1&s&&(e.j41(0,"nb-option",56),e.EFF(1),e.k0s()),2&s){const t=l.$implicit;e.Y8G("value",t),e.R7$(),e.SpI(" ",t.label," ")}}function Se(s,l){if(1&s){const t=e.RV6();e.j41(0,"div",74)(1,"label",105),e.EFF(2," \u9032\u5ea6 "),e.k0s(),e.j41(3,"nb-select",106),e.mxI("ngModelChange",function(n){e.eBV(t);const i=e.XpG(3);return e.DH7(i.detailSelected.CProgressSelected,n)||(i.detailSelected.CProgressSelected=n),e.Njj(n)}),e.DNE(4,Fe,2,2,"nb-option",14),e.k0s()()}if(2&s){const t=e.XpG(3);e.R7$(3),e.R50("ngModel",t.detailSelected.CProgressSelected),e.R7$(),e.Y8G("ngForOf",t.options.progressOptions)}}function Ee(s,l){if(1&s){const t=e.RV6();e.j41(0,"nb-card-body",73)(1,"div",74)(2,"label",75),e.EFF(3," \u5efa\u6848\u540d\u7a31 "),e.k0s(),e.j41(4,"nb-select",76),e.mxI("ngModelChange",function(n){e.eBV(t);const i=e.XpG(2);return e.DH7(i.detailSelected.CBuildCaseSelected,n)||(i.detailSelected.CBuildCaseSelected=n),e.Njj(n)}),e.DNE(5,_e,2,2,"nb-option",14),e.k0s()(),e.j41(6,"div",74)(7,"label",77),e.EFF(8," \u6236\u578b\u540d\u7a31 "),e.k0s(),e.j41(9,"input",78),e.mxI("ngModelChange",function(n){e.eBV(t);const i=e.XpG(2);return e.DH7(i.houseDetail.CHousehold,n)||(i.houseDetail.CHousehold=n),e.Njj(n)}),e.k0s()(),e.j41(10,"div",74)(11,"label",79),e.EFF(12," \u6a13\u5c64 "),e.k0s(),e.j41(13,"input",80),e.mxI("ngModelChange",function(n){e.eBV(t);const i=e.XpG(2);return e.DH7(i.houseDetail.CFloor,n)||(i.houseDetail.CFloor=n),e.Njj(n)}),e.k0s()(),e.j41(14,"div",74)(15,"label",81),e.EFF(16," \u5ba2\u6236\u59d3\u540d "),e.k0s(),e.j41(17,"input",82),e.mxI("ngModelChange",function(n){e.eBV(t);const i=e.XpG(2);return e.DH7(i.houseDetail.CCustomerName,n)||(i.houseDetail.CCustomerName=n),e.Njj(n)}),e.k0s()(),e.j41(18,"div",74)(19,"label",83),e.EFF(20," \u8eab\u5206\u8b49\u5b57\u865f "),e.k0s(),e.j41(21,"input",84),e.mxI("ngModelChange",function(n){e.eBV(t);const i=e.XpG(2);return e.DH7(i.houseDetail.CNationalId,n)||(i.houseDetail.CNationalId=n),e.Njj(n)}),e.k0s()(),e.j41(22,"div",74)(23,"label",85),e.EFF(24," \u96fb\u5b50\u90f5\u4ef6 "),e.k0s(),e.j41(25,"input",86),e.mxI("ngModelChange",function(n){e.eBV(t);const i=e.XpG(2);return e.DH7(i.houseDetail.CMail,n)||(i.houseDetail.CMail=n),e.Njj(n)}),e.k0s()(),e.j41(26,"div",74)(27,"label",87),e.EFF(28," \u806f\u7d61\u96fb\u8a71 "),e.k0s(),e.j41(29,"input",88),e.mxI("ngModelChange",function(n){e.eBV(t);const i=e.XpG(2);return e.DH7(i.houseDetail.CPhone,n)||(i.houseDetail.CPhone=n),e.Njj(n)}),e.k0s()(),e.j41(30,"div",74)(31,"label",89),e.EFF(32," \u6236\u5225\u985e\u578b "),e.k0s(),e.j41(33,"nb-select",90),e.mxI("ngModelChange",function(n){e.eBV(t);const i=e.XpG(2);return e.DH7(i.detailSelected.CHouseTypeSelected,n)||(i.detailSelected.CHouseTypeSelected=n),e.Njj(n)}),e.DNE(34,Ce,2,2,"nb-option",14),e.k0s()(),e.DNE(35,be,5,2,"div",91)(36,Se,5,2,"div",91),e.j41(37,"div",74)(38,"label",92),e.EFF(39," \u662f\u5426\u5ba2\u8b8a "),e.k0s(),e.j41(40,"nb-checkbox",93),e.mxI("checkedChange",function(n){e.eBV(t);const i=e.XpG(2);return e.DH7(i.houseDetail.CIsChange,n)||(i.houseDetail.CIsChange=n),e.Njj(n)}),e.EFF(41,"\u662f "),e.k0s()(),e.j41(42,"div",74)(43,"label",94),e.EFF(44," \u662f\u5426\u555f\u7528 "),e.k0s(),e.j41(45,"nb-checkbox",93),e.mxI("checkedChange",function(n){e.eBV(t);const i=e.XpG(2);return e.DH7(i.houseDetail.CIsEnable,n)||(i.houseDetail.CIsEnable=n),e.Njj(n)}),e.EFF(46,"\u662f "),e.k0s()(),e.j41(47,"div",95)(48,"label",96),e.EFF(49," \u5ba2\u8b8a\u6642\u6bb5 "),e.k0s(),e.j41(50,"div",97)(51,"nb-form-field",98),e.nrm(52,"nb-icon",99),e.j41(53,"input",100),e.mxI("ngModelChange",function(n){e.eBV(t);const i=e.XpG(2);return e.DH7(i.houseDetail.changeStartDate,n)||(i.houseDetail.changeStartDate=n),e.Njj(n)}),e.k0s(),e.nrm(54,"nb-datepicker",101,5),e.k0s(),e.j41(56,"nb-form-field",98),e.nrm(57,"nb-icon",99),e.j41(58,"input",102),e.mxI("ngModelChange",function(n){e.eBV(t);const i=e.XpG(2);return e.DH7(i.houseDetail.changeEndDate,n)||(i.houseDetail.changeEndDate=n),e.Njj(n)}),e.k0s(),e.nrm(59,"nb-datepicker",101,6),e.k0s()()()()}if(2&s){const t=e.sdS(55),o=e.sdS(60),n=e.XpG(2);e.R7$(4),e.R50("ngModel",n.detailSelected.CBuildCaseSelected),e.R7$(),e.Y8G("ngForOf",n.userBuildCaseOptions),e.R7$(4),e.R50("ngModel",n.houseDetail.CHousehold),e.R7$(4),e.R50("ngModel",n.houseDetail.CFloor),e.R7$(4),e.R50("ngModel",n.houseDetail.CCustomerName),e.R7$(4),e.R50("ngModel",n.houseDetail.CNationalId),e.R7$(4),e.R50("ngModel",n.houseDetail.CMail),e.R7$(4),e.R50("ngModel",n.houseDetail.CPhone),e.R7$(4),e.R50("ngModel",n.detailSelected.CHouseTypeSelected),e.R7$(),e.Y8G("ngForOf",n.options.houseTypeOptions),e.R7$(),e.Y8G("ngIf",n.isChangePayStatus),e.R7$(),e.Y8G("ngIf",n.isChangeProgress),e.R7$(4),e.R50("checked",n.houseDetail.CIsChange),e.R7$(5),e.R50("checked",n.houseDetail.CIsEnable),e.R7$(8),e.Y8G("nbDatepicker",t),e.R50("ngModel",n.houseDetail.changeStartDate),e.R7$(5),e.Y8G("nbDatepicker",o),e.R50("ngModel",n.houseDetail.changeEndDate)}}function Ie(s,l){if(1&s){const t=e.RV6();e.j41(0,"button",107),e.bIt("click",function(){e.eBV(t);const n=e.XpG().dialogRef,i=e.XpG();return e.Njj(i.onSubmitDetail(n))}),e.nrm(1,"i",108),e.EFF(2,"\u9001\u51fa "),e.k0s()}}function Me(s,l){if(1&s){const t=e.RV6();e.j41(0,"nb-card",68),e.DNE(1,Ee,61,18,"nb-card-body",69),e.j41(2,"nb-card-footer",54)(3,"button",70),e.bIt("click",function(){const n=e.eBV(t).dialogRef,i=e.XpG();return e.Njj(i.onClose(n))}),e.nrm(4,"i",71),e.EFF(5,"\u53d6\u6d88 "),e.k0s(),e.DNE(6,Ie,3,0,"button",72),e.k0s()()}if(2&s){const t=e.XpG();e.R7$(),e.Y8G("ngIf",t.houseDetail),e.R7$(5),e.Y8G("ngIf",t.isCreate)}}function ke(s,l){if(1&s){const t=e.RV6();e.j41(0,"button",117),e.bIt("click",function(){e.eBV(t);const n=e.XpG().dialogRef,i=e.XpG();return e.Njj(i.addHouseHoldMain(n))}),e.nrm(1,"i",118),e.EFF(2,"\u5132\u5b58 "),e.k0s()}}function De(s,l){if(1&s){const t=e.RV6();e.j41(0,"nb-card",68)(1,"nb-card-header"),e.EFF(2," \u6236\u5225\u7ba1\u7406 \u300b\u6279\u6b21\u65b0\u589e\u6236\u5225\u8cc7\u6599 "),e.k0s(),e.j41(3,"nb-card-body",73)(4,"div",74)(5,"label",109),e.EFF(6," \u68df\u5225 "),e.k0s(),e.j41(7,"input",110),e.mxI("ngModelChange",function(n){e.eBV(t);const i=e.XpG();return e.DH7(i.houseHoldMain.CBuildingName,n)||(i.houseHoldMain.CBuildingName=n),e.Njj(n)}),e.k0s()(),e.j41(8,"div",74)(9,"label",111),e.EFF(10,"\u7576\u5c64\u6700\u591a\u6236\u6578 "),e.k0s(),e.j41(11,"input",112),e.mxI("ngModelChange",function(n){e.eBV(t);const i=e.XpG();return e.DH7(i.houseHoldMain.CHouseHoldCount,n)||(i.houseHoldMain.CHouseHoldCount=n),e.Njj(n)}),e.k0s()(),e.j41(12,"div",74)(13,"label",113),e.EFF(14,"\u672c\u68e0\u7e3d\u6a13\u5c64 "),e.k0s(),e.j41(15,"input",114),e.mxI("ngModelChange",function(n){e.eBV(t);const i=e.XpG();return e.DH7(i.houseHoldMain.CFloor,n)||(i.houseHoldMain.CFloor=n),e.Njj(n)}),e.k0s()()(),e.j41(16,"nb-card-footer",54)(17,"button",115),e.bIt("click",function(){const n=e.eBV(t).dialogRef,i=e.XpG();return e.Njj(i.onClose(n))}),e.nrm(18,"i",71),e.EFF(19,"\u95dc\u9589 "),e.k0s(),e.DNE(20,ke,3,0,"button",116),e.k0s()()}if(2&s){const t=e.XpG();e.R7$(7),e.R50("ngModel",t.houseHoldMain.CBuildingName),e.R7$(4),e.R50("ngModel",t.houseHoldMain.CHouseHoldCount),e.R7$(4),e.R50("ngModel",t.houseHoldMain.CFloor),e.R7$(5),e.Y8G("ngIf",t.isCreate)}}function ye(s,l){if(1&s){const t=e.RV6();e.j41(0,"div",157)(1,"button",46),e.bIt("click",function(){e.eBV(t);const n=e.XpG(2),i=e.sdS(121);return e.Njj(n.openTemplateImportDialog(i))}),e.nrm(2,"i",158),e.EFF(3,"\u5f9e\u6a21\u677f\u532f\u5165 "),e.k0s(),e.j41(4,"div")(5,"button",159),e.bIt("click",function(){e.eBV(t);const n=e.XpG(2);return e.Njj(n.loadDefaultItems())}),e.nrm(6,"i",160),e.EFF(7,"\u8f09\u5165\u5ba2\u8b8a\u9700\u6c42 "),e.k0s(),e.j41(8,"button",38),e.bIt("click",function(){e.eBV(t);const n=e.XpG(2);return e.Njj(n.loadRegularItems())}),e.nrm(9,"i",161),e.EFF(10,"\u8f09\u5165\u9078\u6a23\u8cc7\u6599 "),e.k0s()()()}}function He(s,l){1&s&&(e.j41(0,"div",162),e.nrm(1,"i",163),e.j41(2,"strong"),e.EFF(3,"\u5831\u50f9\u55ae\u5df2\u9396\u5b9a"),e.k0s(),e.EFF(4," - \u6b64\u5831\u50f9\u55ae\u5df2\u9396\u5b9a\uff0c\u7121\u6cd5\u9032\u884c\u4fee\u6539\u3002\u60a8\u53ef\u4ee5\u5217\u5370\u6b64\u5831\u50f9\u55ae\u6216\u7522\u751f\u65b0\u7684\u5831\u50f9\u55ae\u3002 "),e.k0s())}function ve(s,l){if(1&s){const t=e.RV6();e.j41(0,"button",172),e.bIt("click",function(){e.eBV(t);const n=e.XpG().index,i=e.XpG(2);return e.Njj(i.removeQuotationItem(n))}),e.nrm(1,"i",173),e.EFF(2,"\u522a\u9664 "),e.k0s()}}function je(s,l){1&s&&(e.j41(0,"span",174),e.nrm(1,"i",175),e.k0s())}function xe(s,l){if(1&s){const t=e.RV6();e.j41(0,"tr")(1,"td")(2,"input",164),e.mxI("ngModelChange",function(n){const i=e.eBV(t).$implicit;return e.DH7(i.cItemName,n)||(i.cItemName=n),e.Njj(n)}),e.k0s()(),e.j41(3,"td")(4,"input",165),e.mxI("ngModelChange",function(n){const i=e.eBV(t).$implicit;return e.DH7(i.cUnitPrice,n)||(i.cUnitPrice=n),e.Njj(n)}),e.bIt("ngModelChange",function(){e.eBV(t);const n=e.XpG(2);return e.Njj(n.calculateTotal())}),e.k0s()(),e.j41(5,"td")(6,"input",166),e.mxI("ngModelChange",function(n){const i=e.eBV(t).$implicit;return e.DH7(i.cUnit,n)||(i.cUnit=n),e.Njj(n)}),e.k0s()(),e.j41(7,"td")(8,"input",167),e.mxI("ngModelChange",function(n){const i=e.eBV(t).$implicit;return e.DH7(i.cCount,n)||(i.cCount=n),e.Njj(n)}),e.bIt("ngModelChange",function(){e.eBV(t);const n=e.XpG(2);return e.Njj(n.calculateTotal())}),e.k0s()(),e.j41(9,"td",168),e.EFF(10),e.k0s(),e.j41(11,"td")(12,"span",169),e.EFF(13),e.k0s()(),e.j41(14,"td"),e.DNE(15,ve,3,0,"button",170)(16,je,2,0,"span",171),e.k0s()()}if(2&s){const t=l.$implicit,o=e.XpG(2);e.R7$(2),e.AVh("bg-light",!o.isQuotationEditable||1===t.CQuotationItemType||3===t.CQuotationItemType),e.R50("ngModel",t.cItemName),e.Y8G("disabled",!o.isQuotationEditable||1===t.CQuotationItemType||3===t.CQuotationItemType),e.R7$(2),e.R50("ngModel",t.cUnitPrice),e.Y8G("disabled",!o.isQuotationEditable||3===t.CQuotationItemType),e.R7$(2),e.AVh("bg-light",!o.isQuotationEditable||1===t.CQuotationItemType||3===t.CQuotationItemType),e.R50("ngModel",t.cUnit),e.Y8G("disabled",!o.isQuotationEditable||1===t.CQuotationItemType||3===t.CQuotationItemType),e.R7$(2),e.R50("ngModel",t.cCount),e.Y8G("disabled",!o.isQuotationEditable||3===t.CQuotationItemType),e.R7$(2),e.SpI(" ",o.formatCurrency(t.cUnitPrice*t.cCount)," "),e.R7$(2),e.AVh("badge-primary",1===t.CQuotationItemType)("badge-info",3===t.CQuotationItemType)("badge-secondary",1!==t.CQuotationItemType&&3!==t.CQuotationItemType),e.R7$(),e.SpI(" ",o.getQuotationTypeText(t.CQuotationItemType)," "),e.R7$(2),e.Y8G("ngIf",o.isQuotationEditable),e.R7$(),e.Y8G("ngIf",!o.isQuotationEditable)}}function Be(s,l){1&s&&(e.j41(0,"tr")(1,"td",176),e.EFF(2," \u8acb\u9ede\u64ca\u300c\u65b0\u589e\u81ea\u5b9a\u7fa9\u9805\u76ee\u300d\u6216\u300c\u8f09\u5165\u5ba2\u8b8a\u9700\u6c42\u300d\u958b\u59cb\u5efa\u7acb\u5831\u50f9\u55ae "),e.k0s()())}function Te(s,l){if(1&s){const t=e.RV6();e.j41(0,"button",177),e.bIt("click",function(){e.eBV(t);const n=e.XpG(2);return e.Njj(n.createNewQuotation())}),e.nrm(1,"i",178),e.EFF(2," \u7522\u751f\u65b0\u5831\u50f9\u55ae "),e.k0s()}}function Pe(s,l){if(1&s){const t=e.RV6();e.j41(0,"button",179),e.bIt("click",function(){e.eBV(t);const n=e.XpG().dialogRef,i=e.XpG();return e.Njj(i.lockQuotation(n))}),e.nrm(1,"i",180),e.EFF(2,"\u9001\u51fa\u5831\u50f9\u55ae "),e.k0s()}if(2&s){const t=e.XpG(2);e.Y8G("disabled",0===t.quotationItems.length)}}function Re(s,l){if(1&s){const t=e.RV6();e.j41(0,"button",181),e.bIt("click",function(){e.eBV(t);const n=e.XpG().dialogRef,i=e.XpG();return e.Njj(i.saveQuotation(n))}),e.nrm(1,"i",118),e.EFF(2,"\u66ab\u5b58\u5831\u50f9\u55ae "),e.k0s()}if(2&s){const t=e.XpG(2);e.Y8G("disabled",0===t.quotationItems.length)}}function Ae(s,l){if(1&s){const t=e.RV6();e.j41(0,"nb-card",119)(1,"nb-card-header"),e.EFF(2),e.k0s(),e.j41(3,"nb-card-body"),e.DNE(4,ye,11,0,"div",120)(5,He,5,0,"div",121),e.j41(6,"div",122)(7,"table",123)(8,"thead",50)(9,"tr")(10,"th",124),e.EFF(11,"\u9805\u76ee\u540d\u7a31"),e.k0s(),e.j41(12,"th",125),e.EFF(13,"\u55ae\u50f9 (\u5143)"),e.k0s(),e.j41(14,"th",126),e.EFF(15,"\u55ae\u4f4d"),e.k0s(),e.j41(16,"th",126),e.EFF(17,"\u6578\u91cf"),e.k0s(),e.j41(18,"th",127),e.EFF(19,"\u5c0f\u8a08 (\u5143)"),e.k0s(),e.j41(20,"th",128),e.EFF(21,"\u985e\u578b"),e.k0s(),e.j41(22,"th",128),e.EFF(23,"\u64cd\u4f5c"),e.k0s()()(),e.j41(24,"tbody"),e.DNE(25,xe,17,22,"tr",53)(26,Be,3,0,"tr",129),e.k0s()()(),e.j41(27,"div",130)(28,"div",131)(29,"div",132)(30,"div",133)(31,"span",134),e.EFF(32,"\u5c0f\u8a08"),e.k0s(),e.j41(33,"span",135),e.EFF(34),e.k0s()(),e.j41(35,"div",136)(36,"div",137)(37,"div",138),e.nrm(38,"i",139),e.k0s(),e.j41(39,"div")(40,"span",140),e.EFF(41,"\u71df\u696d\u7a05"),e.k0s(),e.j41(42,"span",141),e.EFF(43,"5%"),e.k0s(),e.j41(44,"div",142),e.nrm(45,"i",143),e.EFF(46," \u56fa\u5b9a\u70ba\u5c0f\u8a08\u91d1\u984d\u76845% "),e.k0s()()(),e.j41(47,"div",144)(48,"div",145),e.EFF(49),e.k0s(),e.j41(50,"div",146),e.EFF(51,"\u542b\u7a05\u91d1\u984d"),e.k0s()()(),e.nrm(52,"hr",147),e.j41(53,"div",148)(54,"span",135),e.EFF(55,"\u7e3d\u91d1\u984d"),e.k0s(),e.j41(56,"span",149),e.EFF(57),e.k0s()()()()()(),e.j41(58,"nb-card-footer",150)(59,"div")(60,"button",151),e.bIt("click",function(){e.eBV(t);const n=e.XpG();return e.Njj(n.printQuotation())}),e.nrm(61,"i",152),e.EFF(62," \u5217\u5370\u5831\u50f9\u55ae "),e.k0s(),e.DNE(63,Te,3,0,"button",153),e.k0s(),e.j41(64,"div")(65,"button",154),e.bIt("click",function(){const n=e.eBV(t).dialogRef,i=e.XpG();return e.Njj(i.onClose(n))}),e.nrm(66,"i",71),e.EFF(67,"\u53d6\u6d88 "),e.k0s(),e.DNE(68,Pe,3,1,"button",155)(69,Re,3,1,"button",156),e.k0s()()()}if(2&s){const t=e.XpG();e.R7$(2),e.Lme(" \u5831\u50f9\u55ae - ",null==t.currentHouse?null:t.currentHouse.CHouseHold," (",null==t.currentHouse?null:t.currentHouse.CFloor,"\u6a13) "),e.R7$(2),e.Y8G("ngIf",t.isQuotationEditable),e.R7$(),e.Y8G("ngIf",!t.isQuotationEditable),e.R7$(20),e.Y8G("ngForOf",t.quotationItems),e.R7$(),e.Y8G("ngIf",0===t.quotationItems.length),e.R7$(8),e.JRh(t.formatCurrency(t.totalAmount)),e.R7$(15),e.JRh(t.formatCurrency(t.additionalFeeAmount)),e.R7$(8),e.JRh(t.formatCurrency(t.finalTotalAmount)),e.R7$(3),e.Y8G("disabled",0===t.quotationItems.length),e.R7$(3),e.Y8G("ngIf",!t.isQuotationEditable),e.R7$(5),e.Y8G("ngIf",t.isQuotationEditable),e.R7$(),e.Y8G("ngIf",t.isQuotationEditable)}}function Oe(s,l){if(1&s){const t=e.RV6();e.j41(0,"div",206)(1,"button",207),e.bIt("click",function(){e.eBV(t);const n=e.XpG(2);return e.Njj(n.clearTemplateSearch())}),e.nrm(2,"i",208),e.k0s()()}}function Ne(s,l){1&s&&e.nrm(0,"i",216)}function we(s,l){if(1&s){const t=e.RV6();e.j41(0,"div",209),e.bIt("click",function(){const n=e.eBV(t).$implicit,i=e.XpG(2);return e.Njj(i.selectTemplateForImport(n))}),e.j41(1,"div",210)(2,"div",211),e.nrm(3,"i",212),e.EFF(4),e.k0s(),e.j41(5,"div",213),e.EFF(6),e.k0s()(),e.j41(7,"div",214),e.DNE(8,Ne,1,0,"i",215),e.k0s()()}if(2&s){const t=l.$implicit,o=e.XpG(2);e.AVh("selected",(null==o.selectedTemplateForImport?null:o.selectedTemplateForImport.TemplateID)===t.TemplateID),e.R7$(4),e.SpI(" ",t.TemplateName," "),e.R7$(2),e.SpI(" ",t.Description||"\u7121\u63cf\u8ff0"," "),e.R7$(2),e.Y8G("ngIf",(null==o.selectedTemplateForImport?null:o.selectedTemplateForImport.TemplateID)===t.TemplateID)}}function Ge(s,l){if(1&s&&(e.j41(0,"div",217),e.nrm(1,"i",218),e.j41(2,"p",219),e.EFF(3),e.k0s()()),2&s){const t=e.XpG(2);e.R7$(3),e.SpI(" ",t.templateSearchKeyword?"\u627e\u4e0d\u5230\u7b26\u5408\u689d\u4ef6\u7684\u6a21\u677f":"\u8f09\u5165\u4e2d..."," ")}}function Qe(s,l){if(1&s){const t=e.RV6();e.j41(0,"div",220)(1,"div",148)(2,"div",221)(3,"small",174),e.EFF(4),e.k0s()(),e.j41(5,"div",222)(6,"button",223),e.bIt("click",function(){e.eBV(t);const n=e.XpG(2);return e.Njj(n.previousTemplatePage())}),e.nrm(7,"i",224),e.EFF(8," \u4e0a\u4e00\u9801 "),e.k0s(),e.j41(9,"span",225),e.EFF(10),e.k0s(),e.j41(11,"button",226),e.bIt("click",function(){e.eBV(t);const n=e.XpG(2);return e.Njj(n.nextTemplatePage())}),e.EFF(12," \u4e0b\u4e00\u9801 "),e.nrm(13,"i",227),e.k0s()()()()}if(2&s){const t=e.XpG(2);e.R7$(4),e.E5c(" \u986f\u793a ",t.getTemplateStartIndex()+1," - ",t.getTemplateEndIndex()," \u7b46\uff0c\u5171 ",t.templateTotalItems||t.templateList.length," \u7b46\u6a21\u677f "),e.R7$(2),e.Y8G("disabled",1===t.templateCurrentPage),e.R7$(4),e.Lme(" \u7b2c ",t.templateCurrentPage," / ",t.getTotalTemplatePages()," \u9801 "),e.R7$(),e.Y8G("disabled",t.templateCurrentPage===t.getTotalTemplatePages())}}function $e(s,l){if(1&s&&(e.j41(0,"span",241),e.nrm(1,"i",243),e.EFF(2),e.k0s()),2&s){const t=e.XpG().$implicit;e.R7$(2),e.SpI("",t.CLocation," ")}}function Le(s,l){if(1&s){const t=e.RV6();e.j41(0,"div",234)(1,"div",235)(2,"nb-checkbox",236),e.mxI("checkedChange",function(n){const i=e.eBV(t).$implicit;return e.DH7(i.selected,n)||(i.selected=n),e.Njj(n)}),e.bIt("checkedChange",function(){e.eBV(t);const n=e.XpG(3);return e.Njj(n.updateSelectedCount())}),e.k0s()(),e.j41(3,"div",237)(4,"div",238)(5,"strong"),e.EFF(6),e.k0s()(),e.j41(7,"div",239),e.DNE(8,$e,3,1,"span",240),e.j41(9,"span",241),e.nrm(10,"i",242),e.EFF(11),e.k0s()()()()}if(2&s){const t=l.$implicit;e.R7$(2),e.R50("checked",t.selected),e.R7$(4),e.JRh(t.CReleateName),e.R7$(2),e.Y8G("ngIf",t.CLocation),e.R7$(3),e.SpI("ID: ",t.CReleateId," ")}}function Ve(s,l){1&s&&(e.j41(0,"div",217),e.nrm(1,"i",244),e.j41(2,"p",219),e.EFF(3,"\u6b64\u6a21\u677f\u66ab\u7121\u9805\u76ee"),e.k0s()())}function Xe(s,l){if(1&s){const t=e.RV6();e.j41(0,"div",245)(1,"button",246),e.bIt("click",function(){e.eBV(t);const n=e.XpG(3);return e.Njj(n.selectAllTemplateItems())}),e.nrm(2,"i",247),e.EFF(3,"\u5168\u9078 "),e.k0s(),e.j41(4,"button",248),e.bIt("click",function(){e.eBV(t);const n=e.XpG(3);return e.Njj(n.deselectAllTemplateItems())}),e.nrm(5,"i",249),e.EFF(6,"\u53d6\u6d88\u5168\u9078 "),e.k0s(),e.j41(7,"span",250),e.EFF(8),e.k0s()()}if(2&s){const t=e.XpG(3);e.R7$(8),e.Lme(" \u5df2\u9078\u64c7 ",t.getSelectedTemplateItemsCount()," / ",t.templateDetailList.length," \u9805 ")}}function Ue(s,l){if(1&s&&(e.j41(0,"div",228)(1,"h6",187),e.nrm(2,"i",229),e.EFF(3,"\u6a21\u677f\u9805\u76ee "),e.j41(4,"span",230),e.EFF(5),e.k0s()(),e.j41(6,"div",231),e.DNE(7,Le,12,4,"div",232)(8,Ve,4,0,"div",198),e.k0s(),e.DNE(9,Xe,9,2,"div",233),e.k0s()),2&s){const t=e.XpG(2);e.R7$(5),e.SpI("",t.templateDetailList.length," \u9805"),e.R7$(2),e.Y8G("ngForOf",t.templateDetailList),e.R7$(),e.Y8G("ngIf",0===t.templateDetailList.length),e.R7$(),e.Y8G("ngIf",t.templateDetailList.length>0)}}function Ye(s,l){if(1&s&&(e.j41(0,"div",251)(1,"small",174),e.nrm(2,"i",252),e.EFF(3),e.k0s()()),2&s){const t=e.XpG(2);e.R7$(3),e.SpI(" \u5c07\u532f\u5165 ",t.getSelectedTemplateItemsCount()," \u500b\u9805\u76ee\u5230\u5831\u50f9\u55ae ")}}function Je(s,l){if(1&s){const t=e.RV6();e.j41(0,"nb-card",182)(1,"nb-card-header")(2,"div",148)(3,"h5",183),e.nrm(4,"i",184),e.EFF(5,"\u5f9e\u6a21\u677f\u532f\u5165\u9805\u76ee "),e.k0s(),e.j41(6,"span",185),e.EFF(7,"\u5ba2\u8b8a\u9700\u6c42\u6a21\u677f"),e.k0s()()(),e.j41(8,"nb-card-body")(9,"div",186)(10,"h6",187),e.nrm(11,"i",188),e.EFF(12,"\u9078\u64c7\u6a21\u677f "),e.k0s(),e.j41(13,"div",189)(14,"div",190)(15,"div",191)(16,"span",192),e.nrm(17,"i",193),e.k0s()(),e.j41(18,"input",194),e.mxI("ngModelChange",function(n){e.eBV(t);const i=e.XpG();return e.DH7(i.templateSearchKeyword,n)||(i.templateSearchKeyword=n),e.Njj(n)}),e.bIt("input",function(){e.eBV(t);const n=e.XpG();return e.Njj(n.filterTemplates())}),e.k0s(),e.DNE(19,Oe,3,0,"div",195),e.k0s()(),e.j41(20,"div",196),e.DNE(21,we,9,5,"div",197)(22,Ge,4,1,"div",198),e.k0s(),e.DNE(23,Qe,14,7,"div",199),e.k0s(),e.DNE(24,Ue,10,4,"div",200),e.k0s(),e.j41(25,"nb-card-footer",150),e.DNE(26,Ye,4,1,"div",201),e.j41(27,"div",202)(28,"button",203),e.bIt("click",function(){const n=e.eBV(t).dialogRef,i=e.XpG();return e.Njj(i.onClose(n))}),e.nrm(29,"i",71),e.EFF(30,"\u53d6\u6d88 "),e.k0s(),e.j41(31,"button",204),e.bIt("click",function(){const n=e.eBV(t).dialogRef,i=e.XpG();return e.Njj(i.importSelectedTemplateItems(n))}),e.nrm(32,"i",205),e.EFF(33),e.k0s()()()()}if(2&s){const t=e.XpG();e.R7$(18),e.R50("ngModel",t.templateSearchKeyword),e.R7$(),e.Y8G("ngIf",t.templateSearchKeyword),e.R7$(2),e.Y8G("ngForOf",t.templateList),e.R7$(),e.Y8G("ngIf",0===t.templateList.length),e.R7$(),e.Y8G("ngIf",t.templateTotalItems>0||t.templateList.length>0),e.R7$(),e.Y8G("ngIf",t.selectedTemplateForImport),e.R7$(2),e.Y8G("ngIf",t.selectedTemplateForImport),e.R7$(5),e.Y8G("disabled",!t.selectedTemplateForImport||0===t.getSelectedTemplateItemsCount()),e.R7$(2),e.SpI(" \u532f\u5165\u9078\u4e2d\u9805\u76ee (",t.getSelectedTemplateItemsCount(),") ")}}let ze=(()=>{class s extends E.${constructor(t,o,n,i,a,u,m,F,S,D,x,ln,un,rn){super(t),this._allow=t,this.enumHelper=o,this.dialogService=n,this.message=i,this.valid=a,this._houseService=u,this._houseHoldMainService=m,this._buildCaseService=F,this.pettern=S,this.router=D,this._eventService=x,this._ultilityService=ln,this.quotationService=un,this.templateService=rn,this.tempBuildCaseID=-1,this.pageFirst=1,this.pageSize=10,this.pageIndex=1,this.totalRecords=0,this.statusOptions=[{value:0,key:"allow",label:"\u5141\u8a31"},{value:1,key:"not allowed",label:"\u4e0d\u5141\u8a31"}],this.cIsEnableOptions=[{value:null,key:"all",label:"\u5168\u90e8"},{value:!0,key:"enable",label:"\u555f\u7528"},{value:!1,key:"deactivate",label:"\u505c\u7528"}],this.buildCaseOptions=[{label:"\u5168\u90e8",value:""}],this.houseHoldOptions=[{label:"\u5168\u90e8",value:""}],this.progressOptions=[{label:"\u5168\u90e8",value:-1}],this.houseTypeOptions=[{label:"\u5168\u90e8",value:-1}],this.payStatusOptions=[{label:"\u5168\u90e8",value:-1}],this.signStatusOptions=[{label:"\u5168\u90e8",value:-1}],this.quotationStatusOptions=[{label:"\u5168\u90e8",value:-1}],this.options={progressOptions:this.enumHelper.getEnumOptions(A),payStatusOptions:this.enumHelper.getEnumOptions(O),houseTypeOptions:this.enumHelper.getEnumOptions(V.c),quotationStatusOptions:this.enumHelper.getEnumOptions(H)},this.initDetail={CHouseID:0,CMail:"",CIsChange:!1,CPayStatus:0,CIsEnable:!1,CCustomerName:"",CNationalID:"",CProgress:"",CHouseType:0,CHouseHold:"",CPhone:""},this.quotationItems=[],this.totalAmount=0,this.additionalFeeName="\u71df\u696d\u7a05",this.additionalFeePercentage=5,this.additionalFeeAmount=0,this.finalTotalAmount=0,this.enableAdditionalFee=!0,this.currentHouse=null,this.currentQuotationId=0,this.isQuotationEditable=!0,this.templateList=[],this.templateDetailList=[],this.selectedTemplateForImport=null,this.templateSearchKeyword="",this.templateCurrentPage=1,this.templatePageSize=5,this.paginatedTemplateList=[],this.templateTotalItems=0,this.selectedFile=null,this.buildingSelectedOptions=[{value:"",label:"\u5168\u90e8"}],this._eventService.receive().pipe((0,y.M)(G=>{"GET_BUILDCASE"==G.action&&G.payload&&(this.tempBuildCaseID=G.payload)})).subscribe()}ngOnInit(){if(this.progressOptions=[...this.progressOptions,...this.enumHelper.getEnumOptions(A)],this.houseTypeOptions=[...this.houseTypeOptions,...this.enumHelper.getEnumOptions(V.c)],this.payStatusOptions=[...this.payStatusOptions,...this.enumHelper.getEnumOptions(O)],this.signStatusOptions=[...this.signStatusOptions,...this.enumHelper.getEnumOptions(X)],this.quotationStatusOptions=[...this.quotationStatusOptions,...this.enumHelper.getEnumOptions(H)],null!=f.s.GetSessionStorage(b.AQ.HOUSE_SEARCH)&&null!=f.s.GetSessionStorage(b.AQ.HOUSE_SEARCH)&&""!=f.s.GetSessionStorage(b.AQ.HOUSE_SEARCH)){let t=JSON.parse(f.s.GetSessionStorage(b.AQ.HOUSE_SEARCH));this.searchQuery={CBuildCaseSelected:null,CHouseHoldSelected:null!=t.CHouseHoldSelected&&null!=t.CHouseHoldSelected?this.houseHoldOptions.find(o=>o.value==t.CHouseHoldSelected.value):this.houseHoldOptions[0],CHouseTypeSelected:null!=t.CHouseTypeSelected&&null!=t.CHouseTypeSelected?this.houseTypeOptions.find(o=>o.value==t.CHouseTypeSelected.value):this.houseTypeOptions[0],CPayStatusSelected:null!=t.CPayStatusSelected&&null!=t.CPayStatusSelected?this.payStatusOptions.find(o=>o.value==t.CPayStatusSelected.value):this.payStatusOptions[0],CProgressSelected:null!=t.CProgressSelected&&null!=t.CProgressSelected?this.progressOptions.find(o=>o.value==t.CProgressSelected.value):this.progressOptions[0],CSignStatusSelected:null!=t.CSignStatusSelected&&null!=t.CSignStatusSelected?this.signStatusOptions.find(o=>o.value==t.CSignStatusSelected.value):this.signStatusOptions[0],CQuotationStatusSelected:null!=t.CQuotationStatusSelected&&null!=t.CQuotationStatusSelected?this.quotationStatusOptions.find(o=>o.value==t.CQuotationStatusSelected.value):this.quotationStatusOptions[0],CIsEnableSeleted:null!=t.CIsEnableSeleted&&null!=t.CIsEnableSeleted?this.cIsEnableOptions.find(o=>o.value==t.CIsEnableSeleted.value):this.cIsEnableOptions[0],CFrom:null!=t.CFrom&&null!=t.CFrom?t.CFrom:"",CTo:null!=t.CTo&&null!=t.CTo?t.CTo:""}}else this.searchQuery={CBuildCaseSelected:null,CHouseHoldSelected:this.houseHoldOptions[0],CHouseTypeSelected:this.houseTypeOptions[0],CPayStatusSelected:this.payStatusOptions[0],CProgressSelected:this.progressOptions[0],CSignStatusSelected:this.signStatusOptions[0],CQuotationStatusSelected:this.quotationStatusOptions[0],CIsEnableSeleted:this.cIsEnableOptions[0],CFrom:"",CTo:""};this.getListBuildCase()}onSearch(){f.s.AddSessionStorage(b.AQ.HOUSE_SEARCH,JSON.stringify({CBuildCaseSelected:this.searchQuery.CBuildCaseSelected,CFrom:this.searchQuery.CFrom,CTo:this.searchQuery.CTo,CHouseHoldSelected:this.searchQuery.CHouseHoldSelected,CHouseTypeSelected:this.searchQuery.CHouseTypeSelected,CIsEnableSeleted:this.searchQuery.CIsEnableSeleted,CPayStatusSelected:this.searchQuery.CPayStatusSelected,CProgressSelected:this.searchQuery.CProgressSelected,CSignStatusSelected:this.searchQuery.CSignStatusSelected,CQuotationStatusSelected:this.searchQuery.CQuotationStatusSelected})),this.getHouseList().subscribe()}pageChanged(t){this.pageIndex=t,this.getHouseList().subscribe()}exportHouse(){this.searchQuery.CBuildCaseSelected.cID&&this._houseService.apiHouseExportHousePost$Json({CBuildCaseID:this.searchQuery.CBuildCaseSelected.cID}).subscribe(t=>{t.Entries&&0==t.StatusCode?this._ultilityService.downloadExcelFile(t.Entries,"\u6236\u5225\u8cc7\u8a0a\u7bc4\u672c"):this.message.showErrorMSG(t.Message)})}triggerFileInput(){this.fileInput.nativeElement.click()}onFileSelected(t){const o=t.target;o.files&&o.files.length>0&&(this.selectedFile=o.files[0],this.importExcel())}importExcel(){this.selectedFile&&((new FormData).append("CFile",this.selectedFile),this._houseService.apiHouseImportHousePost$Json({body:{CBuildCaseID:this.searchQuery.CBuildCaseSelected.cID,CFile:this.selectedFile}}).subscribe(o=>{0===o.StatusCode?(this.message.showSucessMSG(o.Message),this.getHouseList().subscribe()):this.message.showErrorMSG(o.Message)}))}getListHouseHold(){this._houseService.apiHouseGetListHouseHoldPost$Json({body:{CBuildCaseID:this.searchQuery.CBuildCaseSelected.cID}}).subscribe(t=>{if(t.Entries&&0==t.StatusCode&&(this.houseHoldOptions=[{value:"",label:"\u5168\u90e8"},...t.Entries.map(o=>({value:o,label:o}))],null!=f.s.GetSessionStorage(b.AQ.HOUSE_SEARCH)&&null!=f.s.GetSessionStorage(b.AQ.HOUSE_SEARCH)&&""!=f.s.GetSessionStorage(b.AQ.HOUSE_SEARCH))){let o=JSON.parse(f.s.GetSessionStorage(b.AQ.HOUSE_SEARCH));if(null!=o.CHouseHoldSelected&&null!=o.CHouseHoldSelected){let n=this.houseHoldOptions.findIndex(i=>i.value==o.CHouseHoldSelected.value);this.searchQuery.CHouseHoldSelected=this.houseHoldOptions[n]}else this.searchQuery.CHouseHoldSelected=this.houseHoldOptions[0]}})}formatQuery(){return this.bodyRequest={CBuildCaseID:this.searchQuery.CBuildCaseSelected.cID,PageIndex:this.pageIndex,PageSize:this.pageSize},this.searchQuery.CFrom&&this.searchQuery.CTo&&(this.bodyRequest.CFloor={CFrom:this.searchQuery.CFrom,CTo:this.searchQuery.CTo}),this.searchQuery.CHouseHoldSelected&&(this.bodyRequest.CHouseHold=this.searchQuery.CHouseHoldSelected.value),this.searchQuery.CHouseTypeSelected.value&&(this.bodyRequest.CHouseType=this.searchQuery.CHouseTypeSelected.value),"boolean"==typeof this.searchQuery.CIsEnableSeleted.value&&(this.bodyRequest.CIsEnable=this.searchQuery.CIsEnableSeleted.value),this.searchQuery.CPayStatusSelected.value&&(this.bodyRequest.CPayStatus=this.searchQuery.CPayStatusSelected.value),this.searchQuery.CProgressSelected.value&&(this.bodyRequest.CProgress=this.searchQuery.CProgressSelected.value),this.searchQuery.CSignStatusSelected.value&&(this.bodyRequest.CSignStatus=this.searchQuery.CSignStatusSelected.value),this.searchQuery.CQuotationStatusSelected.value&&(this.bodyRequest.CQuotationStatus=this.searchQuery.CQuotationStatusSelected.value),this.bodyRequest}sortByFloorDescending(t){return t.sort((o,n)=>(n.CFloor??0)-(o.CFloor??0))}getHouseList(){return this._houseService.apiHouseGetHouseListPost$Json({body:this.formatQuery()}).pipe((0,y.M)(t=>{t.Entries&&0==t.StatusCode&&(this.houseList=t.Entries,this.totalRecords=t.TotalItems)}))}onSelectionChangeBuildCase(){this.getListHouseHold(),this.getHouseList().subscribe()}getListBuildCase(){this._buildCaseService.apiBuildCaseGetAllBuildCaseForSelectPost$Json({body:{CIsPagi:!1,CStatus:1}}).pipe((0,y.M)(t=>{if(t.Entries&&0==t.StatusCode)if(this.userBuildCaseOptions=t.Entries?.length?t.Entries.map(o=>({CBuildCaseName:o.CBuildCaseName,cID:o.cID})):[],null!=f.s.GetSessionStorage(b.AQ.HOUSE_SEARCH)&&null!=f.s.GetSessionStorage(b.AQ.HOUSE_SEARCH)&&""!=f.s.GetSessionStorage(b.AQ.HOUSE_SEARCH)){let o=JSON.parse(f.s.GetSessionStorage(b.AQ.HOUSE_SEARCH));if(null!=o.CBuildCaseSelected&&null!=o.CBuildCaseSelected){let n=this.userBuildCaseOptions.findIndex(i=>i.cID==o.CBuildCaseSelected.cID);this.searchQuery.CBuildCaseSelected=this.userBuildCaseOptions[n]}else this.searchQuery.CBuildCaseSelected=this.userBuildCaseOptions[0]}else this.searchQuery.CBuildCaseSelected=this.userBuildCaseOptions[0]}),(0,y.M)(()=>{this.getListHouseHold(),setTimeout(()=>{this.getHouseList().subscribe()},500)})).subscribe()}getHouseById(t,o){this.detailSelected={},this._houseService.apiHouseGetHouseByIdPost$Json({body:{CHouseID:t}}).subscribe(n=>{n.Entries&&0==n.StatusCode&&(this.houseDetail={...n.Entries,changeStartDate:n.Entries.CChangeStartDate?new Date(n.Entries.CChangeStartDate):void 0,changeEndDate:n.Entries.CChangeEndDate?new Date(n.Entries.CChangeEndDate):void 0},n.Entries.CBuildCaseId&&(this.detailSelected.CBuildCaseSelected=this.findItemInArray(this.userBuildCaseOptions,"cID",n.Entries.CBuildCaseId)),this.detailSelected.CPayStatusSelected=this.findItemInArray(this.options.payStatusOptions,"value",n.Entries.CPayStatus),this.detailSelected.CHouseTypeSelected=n.Entries.CHouseType?this.findItemInArray(this.options.houseTypeOptions,"value",n.Entries.CHouseType):this.options.houseTypeOptions[1],this.detailSelected.CProgressSelected=this.findItemInArray(this.options.progressOptions,"value",n.Entries.CProgress),n.Entries.CBuildCaseId&&this.houseHoldMain&&(this.houseHoldMain.CBuildCaseID=n.Entries.CBuildCaseId),this.dialogService.open(o))})}findItemInArray(t,o,n){return t.find(i=>i[o]===n)}openModelDetail(t,o){this.getHouseById(o.CID,t)}openModel(t){this.houseHoldMain={CBuildingName:"",CFloor:void 0,CHouseHoldCount:void 0},this.dialogService.open(t)}formatDate(t){return t?T(t).format("YYYY-MM-DDTHH:mm:ss"):""}onSubmitDetail(t){this.houseDetail.CChangeStartDate=this.houseDetail.changeStartDate?this.formatDate(this.houseDetail.changeStartDate):"",this.houseDetail.CChangeEndDate=this.houseDetail.changeEndDate?this.formatDate(this.houseDetail.changeEndDate):"",this.editHouseArgsParam={CCustomerName:this.houseDetail.CCustomerName,CHouseHold:this.houseDetail.CHousehold,CHouseID:this.houseDetail.CId,CHouseType:this.detailSelected.CHouseTypeSelected?this.detailSelected.CHouseTypeSelected.value:null,CIsChange:this.houseDetail.CIsChange,CIsEnable:this.houseDetail.CIsEnable,CMail:this.houseDetail.CMail,CNationalID:this.houseDetail.CNationalId,CPayStatus:this.detailSelected.CPayStatusSelected?this.detailSelected.CPayStatusSelected.value:null,CPhone:this.houseDetail.CPhone,CProgress:this.detailSelected.CProgressSelected?this.detailSelected.CProgressSelected.value:null,CChangeStartDate:this.houseDetail.CChangeStartDate,CChangeEndDate:this.houseDetail.CChangeEndDate},this.validation(),this.valid.errorMessages.length>0?this.message.showErrorMSGs(this.valid.errorMessages):this._houseService.apiHouseEditHousePost$Json({body:this.editHouseArgsParam}).pipe((0,y.M)(o=>{0===o.StatusCode?(this.message.showSucessMSG("\u57f7\u884c\u6210\u529f"),t.close()):(this.message.showErrorMSG(o.Message),t.close())}),(0,L.H)(()=>this.getHouseList())).subscribe()}onSubmit(t){this._houseService.apiHouseEditHousePost$Json({body:{CCustomerName:this.houseDetail.CCustomerName,CHouseHold:this.houseDetail.CHousehold,CHouseID:this.houseDetail.CId,CHouseType:this.houseDetail.CHouseType,CIsChange:this.houseDetail.CIsChange,CIsEnable:this.houseDetail.CIsEnable,CMail:this.houseDetail.CMail,CNationalID:this.houseDetail.CNationalId,CPayStatus:this.houseDetail.CPayStatus,CPhone:this.houseDetail.CPhone,CProgress:this.houseDetail.CProgress}}).subscribe(n=>{0===n.StatusCode&&(this.message.showSucessMSG("\u57f7\u884c\u6210\u529f"),t.close())})}onClose(t){t.close()}onNavidateId(t,o){this.router.navigate([`/pages/household-management/${t}`,o||this.searchQuery.CBuildCaseSelected.cID])}onNavidateBuildCaseIdHouseId(t,o,n){this.router.navigate([`/pages/household-management/${t}`,o,n])}resetSecureKey(t){confirm("\u60a8\u60f3\u91cd\u8a2d\u5bc6\u78bc\u55ce\uff1f")&&this._houseService.apiHouseResetHouseSecureKeyPost$Json({body:t.CID}).subscribe(o=>{0==o.StatusCode&&this.message.showSucessMSG("\u57f7\u884c\u6210\u529f")})}validation(){this.valid.clear(),this.valid.required("[\u5efa\u6848\u540d\u7a31]",this.houseDetail.CId),this.valid.required("[\u6236\u578b\u540d\u7a31]",this.editHouseArgsParam.CHouseHold),this.valid.isStringMaxLength("[\u68df\u5225]",this.editHouseArgsParam.CHouseHold,50),this.valid.required("[\u6a13\u5c64]",this.houseDetail.CFloor),this.valid.isStringMaxLength("[\u5ba2\u6236\u59d3\u540d]",this.editHouseArgsParam.CCustomerName,50),this.valid.pattern("[\u96fb\u5b50\u90f5\u4ef6]",this.editHouseArgsParam.CMail,this.pettern.MailPettern),this.valid.isPhoneNumber("[\u806f\u7d61\u96fb\u8a71]",this.editHouseArgsParam.CPhone),this.valid.required("[\u9032\u5ea6]",this.editHouseArgsParam.CProgress),this.valid.required("[\u6236\u5225\u985e\u578b]",this.detailSelected.CHouseTypeSelected.value),this.valid.required("[\u4ed8\u6b3e\u72c0\u614b]",this.detailSelected.CPayStatusSelected.value),this.houseDetail.CChangeStartDate&&this.valid.required("[\u5ba2\u8b8a\u7d50\u675f\u65e5\u671f]",this.houseDetail.CChangeEndDate),this.houseDetail.CChangeEndDate&&this.valid.required("[\u5ba2\u8b8a\u958b\u59cb\u65e5\u671f]",this.houseDetail.CChangeStartDate),this.valid.checkStartBeforeEnd("[\u958b\u653e\u65e5\u671f]",this.houseDetail.CChangeStartDate?this.houseDetail.CChangeStartDate:"",this.houseDetail.CChangeEndDate?this.houseDetail.CChangeEndDate:"")}validationHouseHoldMain(){this.valid.clear(),this.valid.required("[\u5efa\u6848]",this.houseHoldMain.CBuildCaseID),this.valid.required("[\u68df\u5225]",this.houseHoldMain.CBuildingName),this.valid.isStringMaxLength("[\u68df\u5225]",this.houseHoldMain.CBuildingName,10),this.valid.isNaturalNumberInRange("[\u7576\u5c64\u6700\u591a\u6236\u6578]",this.houseHoldMain.CFloor,1,100),this.valid.isNaturalNumberInRange("[\u672c\u68df\u7e3d\u6a13\u5c64]",this.houseHoldMain.CHouseHoldCount,1,100)}addHouseHoldMain(t){this.houseHoldMain.CBuildCaseID=this.searchQuery.CBuildCaseSelected.cID,this.validationHouseHoldMain(),this.valid.errorMessages.length>0?this.message.showErrorMSGs(this.valid.errorMessages):this._houseHoldMainService.apiHouseHoldMainAddHouseHoldMainPost$Json({body:this.houseHoldMain}).pipe((0,y.M)(o=>{0===o.StatusCode&&(this.message.showSucessMSG("\u57f7\u884c\u6210\u529f"),t.close())}),(0,L.H)(()=>this.getHouseList())).subscribe()}openQuotation(t,o){var n=this;return(0,g.A)(function*(){n.currentHouse=o,n.quotationItems=[],n.totalAmount=0,n.currentQuotationId=0,n.isQuotationEditable=!0,n.additionalFeeName="\u71df\u696d\u7a05",n.additionalFeePercentage=5,n.additionalFeeAmount=0,n.finalTotalAmount=0,n.enableAdditionalFee=!0;try{const i=yield n.quotationService.getQuotationByHouseId(o.CID).toPromise();i&&0===i.StatusCode&&i.Entries&&(n.currentQuotationId=i.Entries.CQuotationVersionId||0,n.isQuotationEditable=2!==i.Entries.CQuotationStatus,n.enableAdditionalFee=!0,n.additionalFeeName="\u71df\u696d\u7a05",n.additionalFeePercentage=5,i.Entries.Items&&Array.isArray(i.Entries.Items)&&(n.quotationItems=i.Entries.Items.map(a=>({cHouseID:i.Entries.CHouseID||o.CID,cQuotationID:i.Entries.CQuotationID,cItemName:a.CItemName||"",cUnit:a.CUnit||"",cUnitPrice:a.CUnitPrice||0,cCount:a.CCount||1,cStatus:a.CStatus||1,CQuotationItemType:a.CQuotationItemType&&a.CQuotationItemType>0?a.CQuotationItemType:_.\u81ea\u5b9a\u7fa9,cRemark:a.CRemark||"",cQuotationStatus:a.CQuotationStatus})),n.calculateTotal()))}catch(i){console.error("\u8f09\u5165\u5831\u50f9\u8cc7\u6599\u5931\u6557:",i)}n.dialogService.open(t,{context:o,closeOnBackdropClick:!1})})()}createNewQuotation(){this.currentQuotationId=0,this.quotationItems=[],this.isQuotationEditable=!0,this.totalAmount=0,this.finalTotalAmount=0,this.additionalFeeAmount=0,this.enableAdditionalFee=!0,this.message.showSucessMSG("\u5df2\u7522\u751f\u65b0\u5831\u50f9\u55ae\uff0c\u53ef\u958b\u59cb\u7de8\u8f2f")}addQuotationItem(){this.quotationItems.push({cHouseID:this.currentHouse?.CID||0,cItemName:"",cUnit:"",cUnitPrice:0,cCount:1,cStatus:1,CQuotationItemType:_.\u81ea\u5b9a\u7fa9,cRemark:""})}loadDefaultItems(){var t=this;return(0,g.A)(function*(){try{if(!t.currentHouse?.CID)return void t.message.showErrorMSG("\u8acb\u5148\u9078\u64c7\u6236\u5225");const o={CBuildCaseID:t.searchQuery?.CBuildCaseSelected?.cID||0,CHouseID:t.currentHouse.CID},n=yield t.quotationService.loadDefaultItems(o).toPromise();if(n?.success&&n.data){const i=n.data.map(a=>({cHouseID:t.currentHouse?.CID,cItemName:a.cItemName,cUnit:a.cUnit||"",cUnitPrice:a.cUnitPrice,cCount:a.cCount,cStatus:a.cStatus,CQuotationItemType:_.\u5ba2\u8b8a\u9700\u6c42,cRemark:a.cRemark}));t.quotationItems.push(...i),t.calculateTotal(),t.message.showSucessMSG("\u8f09\u5165\u5ba2\u8b8a\u9700\u6c42\u6210\u529f")}else t.message.showErrorMSG(n?.message||"\u8f09\u5165\u5ba2\u8b8a\u9700\u6c42\u5931\u6557")}catch(o){console.error("\u8f09\u5165\u5ba2\u8b8a\u9700\u6c42\u932f\u8aa4:",o),t.message.showErrorMSG("\u8f09\u5165\u5ba2\u8b8a\u9700\u6c42\u5931\u6557")}})()}loadRegularItems(){var t=this;return(0,g.A)(function*(){try{if(!t.currentHouse?.CID)return void t.message.showErrorMSG("\u8acb\u5148\u9078\u64c7\u6236\u5225");const o={CBuildCaseID:t.searchQuery?.CBuildCaseSelected?.cID||0,CHouseID:t.currentHouse.CID},n=yield t.quotationService.loadRegularItems(o).toPromise();if(n?.success&&n.data){const i=n.data.map(a=>({cHouseID:t.currentHouse?.CID,cItemName:a.cItemName,cUnit:a.cUnit||"",cUnitPrice:a.cUnitPrice,cCount:a.cCount,cStatus:a.cStatus,CQuotationItemType:_.\u9078\u6a23,cRemark:a.cRemark||""}));t.quotationItems.push(...i),t.calculateTotal(),t.message.showSucessMSG("\u8f09\u5165\u9078\u6a23\u8cc7\u6599\u6210\u529f")}else t.message.showErrorMSG(n?.message||"\u8f09\u5165\u9078\u6a23\u8cc7\u6599\u5931\u6557")}catch(o){console.error("\u8f09\u5165\u9078\u6a23\u8cc7\u6599\u932f\u8aa4:",o),t.message.showErrorMSG("\u8f09\u5165\u9078\u6a23\u8cc7\u6599\u5931\u6557")}})()}removeQuotationItem(t){this.quotationItems.splice(t,1),this.calculateTotal()}calculateTotal(){this.totalAmount=this.quotationItems.reduce((t,o)=>t+o.cUnitPrice*o.cCount,0),this.calculateFinalTotal()}calculateFinalTotal(){this.additionalFeeAmount=Math.round(.05*this.totalAmount),this.finalTotalAmount=this.totalAmount+this.additionalFeeAmount}formatCurrency(t){return new Intl.NumberFormat("zh-TW",{style:"currency",currency:"TWD",minimumFractionDigits:0}).format(t)}saveQuotation(t){var o=this;return(0,g.A)(function*(){if(0!==o.quotationItems.length)if(o.quotationItems.filter(i=>!i.cItemName.trim()).length>0)o.message.showErrorMSG("\u8acb\u78ba\u8a8d\u6240\u6709\u9805\u76ee\u540d\u7a31\u90fd\u5df2\u6b63\u78ba\u586b\u5beb");else try{const i={houseId:o.currentHouse.CID,items:o.quotationItems,quotationId:o.currentQuotationId,cShowOther:o.enableAdditionalFee,cOtherName:o.additionalFeeName,cOtherPercent:o.additionalFeePercentage},a=yield o.quotationService.saveQuotation(i).toPromise();a?.success?(o.message.showSucessMSG("\u5831\u50f9\u55ae\u5132\u5b58\u6210\u529f"),t.close()):o.message.showErrorMSG(a?.message||"\u5132\u5b58\u5931\u6557")}catch{o.message.showErrorMSG("\u5831\u50f9\u55ae\u5132\u5b58\u5931\u6557")}else o.message.showErrorMSG("\u8acb\u5148\u65b0\u589e\u5831\u50f9\u9805\u76ee")})()}exportQuotation(){var t=this;return(0,g.A)(function*(){try{const o=yield t.quotationService.exportQuotation(t.currentHouse.CID).toPromise();if(o){const n=window.URL.createObjectURL(o),i=document.createElement("a");i.href=n,i.download=`\u5831\u50f9\u55ae_${t.currentHouse.CHouseHold}_${t.currentHouse.CFloor}\u6a13.pdf`,i.click(),window.URL.revokeObjectURL(n)}else t.message.showErrorMSG("\u532f\u51fa\u5831\u50f9\u55ae\u5931\u6557\uff1a\u672a\u6536\u5230\u6a94\u6848\u8cc7\u6599")}catch{t.message.showErrorMSG("\u532f\u51fa\u5831\u50f9\u55ae\u5931\u6557")}})()}printQuotation(){if(0!==this.quotationItems.length)try{const t=this.generatePrintContent(),o=window.open("","_blank","width=800,height=600,scrollbars=yes,resizable=yes");o?(o.document.open(),o.document.write(t),o.document.close(),o.onload=function(){setTimeout(()=>{o.print()},500)}):this.message.showErrorMSG("\u7121\u6cd5\u958b\u555f\u5217\u5370\u8996\u7a97\uff0c\u8acb\u6aa2\u67e5\u700f\u89bd\u5668\u662f\u5426\u963b\u64cb\u5f48\u51fa\u8996\u7a97")}catch(t){console.error("\u5217\u5370\u5831\u50f9\u55ae\u932f\u8aa4:",t),this.message.showErrorMSG("\u5217\u5370\u5831\u50f9\u55ae\u6642\u767c\u751f\u932f\u8aa4")}else this.message.showErrorMSG("\u6c92\u6709\u53ef\u5217\u5370\u7684\u5831\u50f9\u9805\u76ee")}generatePrintContent(){const o=(new Date).toLocaleDateString("zh-TW"),n=this.searchQuery.CBuildCaseSelected?.CBuildCaseName||"";let i="";this.quotationItems.forEach((m,F)=>{const S=m.cUnitPrice*m.cCount,D=this.getQuotationTypeText(m.CQuotationItemType),x=m.cUnit||"";i+=`\n          <tr>\n            <td class="text-center">${F+1}</td>\n            <td>${m.cItemName}</td>\n            <td class="text-right">${this.formatCurrency(m.cUnitPrice)}</td>\n            <td class="text-center">${x}</td>\n            <td class="text-center">${m.cCount}</td>\n            <td class="text-right">${this.formatCurrency(S)}</td>\n            <td class="text-center">${D}</td>\n          </tr>\n        `});const a=this.enableAdditionalFee?`\n        <div class="additional-fee">\n          ${this.additionalFeeName} (${this.additionalFeePercentage}%)\uff1a${this.formatCurrency(this.additionalFeeAmount)}\n        </div>\n      `:"";return'<!DOCTYPE html>\n<html>\n\n<head>\n  <meta charset="utf-8">\n  <title>\u5831\u50f9\u55ae\u5217\u5370\u6a21\u677f</title>\n  <style>\n    body {\n      font-family: \'Microsoft JhengHei\', \'\u5fae\u8edf\u6b63\u9ed1\u9ad4\', Arial, sans-serif;\n      margin: 20px;\n      font-size: 14px;\n      line-height: 1.6;\n    }\n\n    .header {\n      text-align: center;\n      margin-bottom: 30px;\n    }\n\n    .header h1 {\n      margin: 0;\n      font-size: 24px;\n      color: #333;\n      font-weight: bold;\n    }\n\n    .info-section {\n      margin-bottom: 20px;\n      border-bottom: 1px solid #ddd;\n      padding-bottom: 15px;\n    }\n\n    .info-row {\n      display: flex;\n      margin-bottom: 8px;\n    }\n\n    .info-label {\n      font-weight: bold;\n      width: 100px;\n      flex-shrink: 0;\n    }\n\n    .info-value {\n      flex: 1;\n    }\n\n    table {\n      width: 100%;\n      border-collapse: collapse;\n      margin-bottom: 20px;\n    }\n\n    th {\n      background-color: #27ae60;\n      color: white;\n      border: 1px solid #ddd;\n      padding: 10px 8px;\n      text-align: center;\n      font-weight: bold;\n    }\n\n    td {\n      border: 1px solid #ddd;\n      padding: 8px;\n    }\n\n    .text-center {\n      text-align: center;\n    }\n\n    .text-right {\n      text-align: right;\n    }\n\n    .total-section {\n      text-align: right;\n      margin-top: 20px;\n      padding-top: 15px;\n      border-top: 2px solid #27ae60;\n    }\n\n    .subtotal {\n      font-size: 14px;\n      margin-bottom: 5px;\n      color: #666;\n    }\n\n    .additional-fee {\n      font-size: 14px;\n      margin-bottom: 10px;\n      color: #666;\n    }\n\n    .total-amount {\n      font-size: 18px;\n      font-weight: bold;\n      color: #27ae60;\n      border-top: 1px solid #ddd;\n      padding-top: 10px;\n    }\n\n    .footer {\n      margin-top: 40px;\n      text-align: center;\n      font-size: 12px;\n      color: #666;\n    }\n\n    .signature-section {\n      margin-top: 40px;\n      page-break-inside: avoid;\n    }\n\n    .signature-box {\n      width: 300px;\n      margin: 0 auto;\n      text-align: center;\n    }\n\n    .signature-label {\n      font-weight: bold;\n      margin-bottom: 40px;\n      font-size: 16px;\n    }\n\n    .signature-line {\n      border-bottom: 2px solid #000;\n      height: 60px;\n      margin-bottom: 10px;\n      position: relative;\n    }\n\n    .signature-date {\n      font-size: 14px;\n      margin-top: 15px;\n    }\n\n    .signature-notes {\n      margin-top: 30px;\n      padding: 15px;\n      background-color: #f9f9f9;\n      border-left: 4px solid #27ae60;\n    }\n\n    .signature-notes p {\n      margin: 0 0 10px 0;\n      font-weight: bold;\n    }\n\n    .signature-notes ul {\n      margin: 0;\n      padding-left: 20px;\n    }\n\n    .signature-notes li {\n      margin-bottom: 5px;\n      line-height: 1.4;\n    }\n\n    @media print {\n      body {\n        margin: 0;\n      }\n\n      .header {\n        page-break-inside: avoid;\n      }\n\n      .signature-section {\n        page-break-inside: avoid;\n      }\n    }\n  </style>\n</head>\n\n<body>\n  <div class="header">\n    <h1>\u5831\u50f9\u55ae</h1>\n  </div>\n\n  <div class="info-section">\n    <div class="info-row">\n      <span class="info-label">\u5efa\u6848\u540d\u7a31\uff1a</span>\n      <span class="info-value">{{buildCaseName}}</span>\n    </div>\n    <div class="info-row">\n      <span class="info-label">\u6236\u5225\uff1a</span>\n      <span class="info-value">{{houseHold}}</span>\n    </div>\n    <div class="info-row">\n      <span class="info-label">\u6a13\u5c64\uff1a</span>\n      <span class="info-value">{{floor}}\u6a13</span>\n    </div>\n    <div class="info-row">\n      <span class="info-label">\u5217\u5370\u65e5\u671f\uff1a</span>\n      <span class="info-value">{{printDate}}</span>\n    </div>\n  </div>\n\n  <table>\n    <thead>\n      <tr>\n        <th width="8%">\u5e8f\u865f</th>\n        <th width="30%">\u9805\u76ee\u540d\u7a31</th>\n        <th width="12%">\u55ae\u50f9 (\u5143)</th>\n        <th width="8%">\u55ae\u4f4d</th>\n        <th width="8%">\u6578\u91cf</th>\n        <th width="15%">\u5c0f\u8a08 (\u5143)</th>\n        <th width="12%">\u985e\u578b</th>\n      </tr>\n    </thead>\n    <tbody>\n      {{itemsHtml}}\n    </tbody>\n  </table>\n\n  <div class="total-section">\n    <div class="subtotal">\n      \u5c0f\u8a08\uff1a{{subtotalAmount}}\n    </div>\n    {{additionalFeeHtml}}\n    <div class="total-amount">\n      \u7e3d\u91d1\u984d\uff1a{{totalAmount}}\n    </div>\n  </div>\n\n  <div class="signature-section">\n    <div class="signature-box">\n      <div class="signature-label">\u5ba2\u6236\u7c3d\u540d\uff1a</div>\n      <div class="signature-line"></div>\n      <div class="signature-date">\u65e5\u671f\uff1a_____\u5e74_____\u6708_____\u65e5</div>\n    </div>\n\n    <div class="signature-notes">\n      <p><strong>\u6ce8\u610f\u4e8b\u9805\uff1a</strong></p>\n      <ul>\n        <li>\u6b64\u5831\u50f9\u55ae\u6709\u6548\u671f\u9650\u70ba30\u5929\uff0c\u903e\u671f\u9700\u91cd\u65b0\u5831\u50f9</li>\n        <li>\u5831\u50f9\u5167\u5bb9\u82e5\u6709\u7570\u52d5\uff0c\u8acb\u91cd\u65b0\u78ba\u8a8d</li>\n        <li>\u7c3d\u540d\u78ba\u8a8d\u5f8c\u5373\u8996\u70ba\u540c\u610f\u6b64\u5831\u50f9\u5167\u5bb9</li>\n      </ul>\n    </div>\n  </div>\n\n  <div class="footer">\n    \u6b64\u5831\u50f9\u55ae\u7531\u7cfb\u7d71\u81ea\u52d5\u7522\u751f\uff0c\u5217\u5370\u6642\u9593\uff1a{{printDateTime}}\n  </div>\n</body>\n\n</html>'.replace(/{{buildCaseName}}/g,n).replace(/{{houseHold}}/g,this.currentHouse?.CHouseHold||"").replace(/{{floor}}/g,this.currentHouse?.CFloor||"").replace(/{{customerName}}/g,this.currentHouse?.CCustomerName||"").replace(/{{printDate}}/g,o).replace(/{{itemsHtml}}/g,i).replace(/{{subtotalAmount}}/g,this.formatCurrency(this.totalAmount)).replace(/{{additionalFeeHtml}}/g,a).replace(/{{totalAmount}}/g,this.formatCurrency(this.finalTotalAmount)).replace(/{{printDateTime}}/g,(new Date).toLocaleString("zh-TW"))}lockQuotation(t){var o=this;return(0,g.A)(function*(){if(0!==o.quotationItems.length)if(o.currentQuotationId)try{const n=yield o.quotationService.lockQuotation(o.currentQuotationId).toPromise();n.success?(o.message.showSucessMSG("\u5831\u50f9\u55ae\u5df2\u6210\u529f\u9396\u5b9a"),console.log("\u5831\u50f9\u55ae\u9396\u5b9a\u6210\u529f:",{quotationId:o.currentQuotationId,message:n.message})):(o.message.showErrorMSG(n.message||"\u5831\u50f9\u55ae\u9396\u5b9a\u5931\u6557"),console.error("\u5831\u50f9\u55ae\u9396\u5b9a\u5931\u6557:",n.message)),t.close()}catch(n){o.message.showErrorMSG("\u5831\u50f9\u55ae\u9396\u5b9a\u5931\u6557"),console.error("\u9396\u5b9a\u5831\u50f9\u55ae\u932f\u8aa4:",n)}else o.message.showErrorMSG("\u7121\u6548\u7684\u5831\u50f9\u55aeID");else o.message.showErrorMSG("\u8acb\u5148\u65b0\u589e\u5831\u50f9\u9805\u76ee")})()}getQuotationTypeText(t){switch(t){case _.\u5ba2\u8b8a\u9700\u6c42:return"\u5ba2\u8b8a\u9700\u6c42";case _.\u81ea\u5b9a\u7fa9:return"\u5ba2\u8b8a\u9805\u76ee";case _.\u9078\u6a23:return"\u9078\u6a23";default:return"\u672a\u77e5"}}getQuotationStatusText(t){switch(t){case H.\u5f85\u5831\u50f9:return"\u5f85\u5831\u50f9";case H.\u5df2\u5831\u50f9:return"\u5df2\u5831\u50f9";case H.\u5df2\u7c3d\u56de:return"\u5df2\u7c3d\u56de";default:return"\u672a\u77e5"}}openTemplateImportDialog(t){var o=this;return(0,g.A)(function*(){o.templateList=[],o.templateDetailList=[],o.selectedTemplateForImport=null,o.templateSearchKeyword="",o.templateCurrentPage=1,o.paginatedTemplateList=[],o.templateTotalItems=0,yield o.loadTemplateList(),o.dialogService.open(t,{closeOnBackdropClick:!1})})()}loadTemplateList(){var t=this;return(0,g.A)(function*(){try{const o={CTemplateType:1,PageIndex:t.templateCurrentPage,PageSize:t.templatePageSize,CTemplateName:t.templateSearchKeyword||null},n=yield t.templateService.apiTemplateGetTemplateListPost$Json({body:o}).toPromise();0===n?.StatusCode&&n.Entries?(t.templateList=n.Entries.map(i=>({TemplateID:i.CTemplateId,TemplateName:i.CTemplateName||"",Description:`\u5efa\u7acb\u6642\u9593: ${i.CCreateDt?new Date(i.CCreateDt).toLocaleDateString():"\u672a\u77e5"}`})),t.templateTotalItems=n.TotalItems||0,t.updatePaginatedTemplateList()):(t.templateList=[],t.templateTotalItems=0,t.message.showErrorMSG("\u8f09\u5165\u6a21\u677f\u5217\u8868\u5931\u6557"))}catch(o){console.error("\u8f09\u5165\u6a21\u677f\u5217\u8868\u932f\u8aa4:",o),t.message.showErrorMSG("\u8f09\u5165\u6a21\u677f\u5217\u8868\u5931\u6557")}})()}filterTemplates(){var t=this;return(0,g.A)(function*(){t.templateCurrentPage=1,yield t.loadTemplateList()})()}clearTemplateSearch(){this.templateSearchKeyword="",this.filterTemplates()}selectTemplateForImport(t){var o=this;return(0,g.A)(function*(){o.selectedTemplateForImport=t,yield o.loadTemplateDetails(t.TemplateID)})()}loadTemplateDetails(t){var o=this;return(0,g.A)(function*(){try{const n={templateId:t},i=yield o.templateService.apiTemplateGetTemplateDetailByIdPost$Json({body:n}).toPromise();0===i?.StatusCode&&i.Entries?o.templateDetailList=i.Entries.map(a=>({CTemplateDetailId:a.CTemplateDetailId||0,CTemplateId:a.CTemplateId||t,CReleateId:a.CReleateId||0,CPart:a.CPart||"",CLocation:a.CLocation||"",selected:!1})):(o.templateDetailList=[],o.message.showErrorMSG("\u8f09\u5165\u6a21\u677f\u8a73\u60c5\u5931\u6557"))}catch(n){console.error("\u8f09\u5165\u6a21\u677f\u8a73\u60c5\u932f\u8aa4:",n),o.message.showErrorMSG("\u8f09\u5165\u6a21\u677f\u8a73\u60c5\u5931\u6557")}})()}updateSelectedCount(){}selectAllTemplateItems(){this.templateDetailList.forEach(t=>{t.selected=!0})}deselectAllTemplateItems(){this.templateDetailList.forEach(t=>{t.selected=!1})}getSelectedTemplateItemsCount(){return this.templateDetailList.filter(t=>t.selected).length}importSelectedTemplateItems(t){var o=this;return(0,g.A)(function*(){const n=o.templateDetailList.filter(i=>i.selected);if(0!==n.length)try{const i=n.map(a=>({cHouseID:o.currentHouse?.CID||0,cItemName:a.CPart,cUnit:"",cUnitPrice:0,cCount:1,cStatus:1,CQuotationItemType:_.\u81ea\u5b9a\u7fa9,cRemark:a.CLocation?`\u7fa4\u7d44: ${a.CLocation}`:""}));o.quotationItems.push(...i),o.calculateTotal(),o.message.showSucessMSG(`\u6210\u529f\u532f\u5165 ${n.length} \u500b\u9805\u76ee`),t.close()}catch(i){console.error("\u532f\u5165\u6a21\u677f\u9805\u76ee\u932f\u8aa4:",i),o.message.showErrorMSG("\u532f\u5165\u6a21\u677f\u9805\u76ee\u5931\u6557")}else o.message.showErrorMSG("\u8acb\u9078\u64c7\u8981\u532f\u5165\u7684\u9805\u76ee")})()}updatePaginatedTemplateList(){this.paginatedTemplateList=[...this.templateList]}getTotalTemplatePages(){const o=Math.ceil((this.templateTotalItems||this.templateList.length)/this.templatePageSize);return Math.max(1,o)}getTemplateStartIndex(){return(this.templateCurrentPage-1)*this.templatePageSize}getTemplateEndIndex(){return Math.min(this.templateCurrentPage*this.templatePageSize,this.templateTotalItems||this.templateList.length)}previousTemplatePage(){var t=this;return(0,g.A)(function*(){t.templateCurrentPage>1&&(t.templateCurrentPage--,yield t.loadTemplateList())})()}nextTemplatePage(){var t=this;return(0,g.A)(function*(){t.templateCurrentPage<t.getTotalTemplatePages()&&(t.templateCurrentPage++,yield t.loadTemplateList())})()}static{this.\u0275fac=function(o){return new(o||s)(e.rXU(I.O),e.rXU(U.e),e.rXU(r.S0W),e.rXU(M.b),e.rXU(P.E),e.rXU(h.SD),e.rXU(h.IO),e.rXU(h.aH),e.rXU(Z.n),e.rXU(C.Ix),e.rXU(v.U),e.rXU(ee.Q),e.rXU(oe),e.rXU(h.IK))}}static{this.\u0275cmp=e.VBU({type:s,selectors:[["ngx-household-management"]],viewQuery:function(o,n){if(1&o&&e.GBs(ie,5),2&o){let i;e.mGM(i=e.lsd())&&(n.fileInput=i.first)}},standalone:!0,features:[e.Vt3,e.aNF],decls:122,vars:23,consts:[["fileInput",""],["dialogUpdateHousehold",""],["dialogHouseholdMain",""],["dialogQuotation",""],["dialogTemplateImport",""],["StartDate",""],["EndDate",""],["accent","success"],[1,"font-bold","text-[#818181]"],[1,"d-flex","flex-wrap"],[1,"col-md-6"],[1,"form-group","d-flex","align-items-center","w-full"],["for","buildingName",1,"label","col-3"],["placeholder","\u5efa\u6848",1,"col-9",3,"ngModelChange","selectedChange","ngModel"],[3,"value",4,"ngFor","ngForOf"],["for","cHouseType",1,"label","col-3"],[1,"col-9",3,"ngModelChange","ngModel"],[1,"form-group","d-flex","align-items-center"],["for","cFloorFrom",1,"label","col-3"],[1,"ml-3"],["type","text","id","CFrom","nbInput","",1,"w-full","col-4",3,"ngModelChange","ngModel"],["for","cFloorTo",1,"label","col-1"],[1,"mr-3"],["type","text","id","CTo","nbInput","",1,"w-full",3,"ngModelChange","ngModel"],["for","cHousehold",1,"label","col-3"],["placeholder","\u6236\u578b",1,"col-9",3,"ngModelChange","ngModel"],["for","cPayStatus",1,"label","col-3"],["placeholder","\u7e73\u6b3e\u72c0\u614b",1,"col-9",3,"ngModelChange","ngModel"],["for","cProgress",1,"label","col-3"],["placeholder","\u9032\u5ea6",1,"col-9",3,"ngModelChange","ngModel"],["for","cStatus",1,"label","col-3"],["placeholder","\u72c0\u614b",1,"col-9",3,"ngModelChange","ngModel"],["for","cSignStatus",1,"label","col-3"],["placeholder","\u7c3d\u56de\u72c0\u614b",1,"col-9",3,"ngModelChange","ngModel"],["for","cQuotationStatus",1,"label","col-3"],["placeholder","\u5831\u50f9\u55ae\u72c0\u614b",1,"col-9",3,"ngModelChange","ngModel"],[1,"col-md-12"],[1,"d-flex","justify-content-end","w-full","mt-2","mb-3"],[1,"btn","btn-secondary","btn-sm",3,"click"],[1,"fas","fa-search","me-1"],[1,"d-flex","justify-content-end","w-full","mt-3"],["class","btn btn-info mx-1 btn-sm",3,"click",4,"ngIf"],[1,"btn","btn-info","mx-1","btn-sm",3,"click"],[1,"fas","fa-building","me-1"],[1,"fas","fa-file-export","me-1"],["type","file","accept",".xlsx, .xls",2,"display","none",3,"change"],[1,"btn","btn-info","btn-sm",3,"click"],[1,"fas","fa-file-import","me-1"],[1,"table-responsive","mt-4"],[1,"table",2,"min-width","1000px"],[1,"table-header"],["scope","col",1,"col-1"],["scope","col",1,"col-4"],[4,"ngFor","ngForOf"],[1,"d-flex","justify-content-center"],["aria-label","Pagination",3,"pageChange","page","pageSize","collectionSize"],[3,"value"],[1,"fas","fa-users-plus","me-1"],[1,"w-32","px-0"],["class","btn btn-outline-success btn-sm text-left m-[2px]",3,"click",4,"ngIf"],[1,"btn","btn-outline-success","btn-sm","m-[2px]",3,"click"],[1,"fas","fa-comments","me-1"],[1,"fas","fa-file-image","me-1"],[1,"fas","fa-file-signature","me-1"],[1,"fas","fa-key","me-1"],[1,"fas","fa-file-invoice-dollar","me-1"],[1,"btn","btn-outline-success","btn-sm","text-left","m-[2px]",3,"click"],[1,"fas","fa-edit","me-1"],[2,"width","500px","max-height","95vh"],["class","px-4",4,"ngIf"],[1,"btn","btn-outline-secondary","m-2","px-8",3,"click"],[1,"fas","fa-times","me-1"],["class","btn btn-primary m-2 bg-[#169BD5] px-8",3,"click",4,"ngIf"],[1,"px-4"],[1,"form-group"],["for","cBuildCaseId","baseLabel","",1,"required-field","mr-4",2,"min-width","75px"],["placeholder","\u5efa\u6848\u540d\u7a31","disabled","true",1,"w-full",3,"ngModelChange","ngModel"],["for","cHousehold","baseLabel","",1,"required-field","mr-4",2,"min-width","75px"],["type","text","nbInput","","placeholder","\u6236\u578b\u540d\u7a31",1,"w-full",3,"ngModelChange","ngModel"],["for","cFloor","baseLabel","",1,"required-field","mr-4",2,"min-width","75px"],["type","number","nbInput","","placeholder","\u6a13\u5c64","min","1","max","100","disabled","true",1,"w-full",3,"ngModelChange","ngModel"],["for","cCustomerName","baseLabel","",1,"mr-4",2,"min-width","75px"],["type","text","nbInput","","placeholder","\u5ba2\u6236\u59d3\u540d",1,"w-full",3,"ngModelChange","ngModel"],["for","cNationalId","baseLabel","",1,"mr-4",2,"min-width","75px"],["type","text","nbInput","","placeholder","\u8eab\u5206\u8b49\u5b57\u865f","maxlength","20",1,"w-full",3,"ngModelChange","ngModel"],["for","cMail","baseLabel","",1,"mr-4",2,"min-width","75px"],["type","text","nbInput","","placeholder","\u96fb\u5b50\u90f5\u4ef6",1,"w-full",3,"ngModelChange","ngModel"],["for","cPhone","baseLabel","",1,"mr-4",2,"min-width","75px"],["type","text","nbInput","","placeholder","\u806f\u7d61\u96fb\u8a71",1,"w-full",3,"ngModelChange","ngModel"],["for","cHouseType","baseLabel","",1,"required-field","mr-4",2,"min-width","75px"],["placeholder","\u6236\u5225\u985e\u578b",1,"w-full",3,"ngModelChange","ngModel"],["class","form-group",4,"ngIf"],["for","cIsChange","baseLabel","",1,"mr-4",2,"min-width","75px"],["status","basic",3,"checkedChange","checked"],["for","cIsEnable","baseLabel","",1,"mr-4",2,"min-width","75px"],[1,"form-group","flex","flex-row"],["for","cIsEnable","baseLabel","",1,"mr-4","content-center",2,"min-width","75px"],[1,"max-w-xs","flex","flex-row"],[1,"w-1/2"],["nbPrefix","","icon","calendar-outline"],["nbInput","","type","text","id","StartDate","placeholder","yyyy-mm-dd",1,"w-[42%]","mr-2",3,"ngModelChange","nbDatepicker","ngModel"],["format","yyyy-MM-dd"],["nbInput","","type","text","id","EndDate","placeholder","yyyy-mm-dd",1,"w-[42%]","ml-2",3,"ngModelChange","nbDatepicker","ngModel"],["for","cPayStatus","baseLabel","",1,"required-field","mr-4",2,"min-width","75px"],["placeholder","\u4ed8\u6b3e\u72c0\u614b",1,"w-full",3,"ngModelChange","ngModel"],["for","cProgress","baseLabel","",1,"required-field","mr-4",2,"min-width","75px"],["placeholder","\u9032\u5ea6",1,"w-full",3,"ngModelChange","ngModel"],[1,"btn","btn-primary","m-2","bg-[#169BD5]","px-8",3,"click"],[1,"fas","fa-check","me-1"],["for","cBuildingName","baseLabel","",1,"required-field","mr-4",2,"min-width","75px"],["type","text","nbInput","","placeholder","\u68df\u5225",1,"w-full",3,"ngModelChange","ngModel"],["for","CHouseHoldCount","baseLabel","",1,"required-field","mr-4",2,"min-width","75px"],["type","number","nbInput","","placeholder","\u7576\u5c64\u6700\u591a\u6236\u6578",1,"w-full",3,"ngModelChange","ngModel"],["for","CFloor","baseLabel","",1,"required-field","mr-4",2,"min-width","75px"],["type","number","nbInput","","placeholder","\u672c\u68e0\u7e3d\u6a13\u5c64",1,"w-full",3,"ngModelChange","ngModel"],[1,"btn","btn-primary","mr-4",3,"click"],["class","btn btn-primary",3,"click",4,"ngIf"],[1,"btn","btn-primary",3,"click"],[1,"fas","fa-save","me-1"],[2,"width","1200px","max-height","95vh"],["class","mb-4 d-flex justify-content-between",4,"ngIf"],["class","mb-4 alert alert-warning",4,"ngIf"],[1,"table-responsive"],[1,"table"],["width","25%"],["width","15%"],["width","8%"],["width","18%"],["width","10%"],[4,"ngIf"],[1,"mt-4"],[1,"card","border-0","shadow-sm"],[1,"card-body","p-4"],[1,"d-flex","justify-content-between","align-items-center","mb-3"],[1,"h6","mb-0","text-muted"],[1,"h5","mb-0","text-dark","fw-bold"],[1,"tax-section","d-flex","justify-content-between","align-items-center","mb-3","p-3","bg-light","rounded"],[1,"d-flex","align-items-center"],[1,"tax-icon-wrapper","me-3"],[1,"fas","fa-receipt","text-info"],[1,"fw-medium","text-dark"],[1,"tax-percentage","ms-1","badge","bg-info","text-white"],[1,"small","text-muted","mt-1"],[1,"fas","fa-info-circle","me-1"],[1,"text-end"],[1,"tax-amount","h6","mb-0","text-info","fw-bold"],[1,"small","text-muted"],[1,"my-3"],[1,"d-flex","justify-content-between","align-items-center"],[1,"h4","mb-0","text-primary","fw-bold"],[1,"d-flex","justify-content-between"],["title","\u5217\u5370\u5831\u50f9\u55ae",1,"btn","btn-outline-info","btn-sm","me-2",3,"click","disabled"],[1,"fas","fa-print","me-1"],["class","btn btn-outline-success btn-sm me-2","title","\u7522\u751f\u65b0\u5831\u50f9\u55ae",3,"click",4,"ngIf"],[1,"btn","btn-outline-secondary","m-2",3,"click"],["class","btn btn-warning m-2",3,"disabled","click",4,"ngIf"],["class","btn btn-primary m-2",3,"disabled","click",4,"ngIf"],[1,"mb-4","d-flex","justify-content-between"],[1,"fas","fa-download","me-1"],[1,"btn","btn-secondary","btn-sm","me-2",3,"click"],[1,"fas","fa-list-alt","me-1"],[1,"fas","fa-palette","me-1"],[1,"mb-4","alert","alert-warning"],[1,"fas","fa-lock","me-2"],["type","text","nbInput","",1,"w-full",3,"ngModelChange","ngModel","disabled"],["type","number","nbInput","","min","0","step","0.01",1,"w-full",3,"ngModelChange","ngModel","disabled"],["type","text","nbInput","","placeholder","\u55ae\u4f4d",1,"w-full",3,"ngModelChange","ngModel","disabled"],["type","number","nbInput","","step","0.01",1,"w-full",3,"ngModelChange","ngModel","disabled"],[1,"text-right"],[1,"badge"],["class","btn btn-danger btn-sm",3,"click",4,"ngIf"],["class","text-muted",4,"ngIf"],[1,"btn","btn-danger","btn-sm",3,"click"],[1,"fas","fa-trash","me-1"],[1,"text-muted"],[1,"fas","fa-lock"],["colspan","7",1,"text-muted","py-4"],["title","\u7522\u751f\u65b0\u5831\u50f9\u55ae",1,"btn","btn-outline-success","btn-sm","me-2",3,"click"],[1,"fas","fa-plus","me-1"],[1,"btn","btn-warning","m-2",3,"click","disabled"],[1,"fas","fa-paper-plane","me-1"],[1,"btn","btn-primary","m-2",3,"click","disabled"],[2,"width","800px","max-height","95vh"],[1,"mb-0"],[1,"fas","fa-download","mr-2","text-primary"],[1,"badge","badge-info"],[1,"template-selection-section","mb-4"],[1,"section-title","mb-3"],[1,"fas","fa-list","mr-2"],[1,"search-container","mb-3"],[1,"input-group"],[1,"input-group-prepend"],[1,"input-group-text"],[1,"fas","fa-search"],["type","text","placeholder","\u641c\u5c0b\u6a21\u677f\u540d\u7a31...",1,"form-control",3,"ngModelChange","input","ngModel"],["class","input-group-append",4,"ngIf"],[1,"template-list"],["class","template-item",3,"selected","click",4,"ngFor","ngForOf"],["class","empty-state text-center py-4",4,"ngIf"],["class","pagination-container mt-3",4,"ngIf"],["class","template-details-section",4,"ngIf"],["class","import-info",4,"ngIf"],[1,"actions"],[1,"btn","btn-outline-secondary","mr-2",3,"click"],[1,"btn","btn-primary",3,"click","disabled"],[1,"fas","fa-download","mr-1"],[1,"input-group-append"],["type","button",1,"btn","btn-outline-secondary",3,"click"],[1,"fas","fa-times"],[1,"template-item",3,"click"],[1,"template-info"],[1,"template-name"],[1,"fas","fa-file-alt","mr-2"],[1,"template-description","text-muted","small"],[1,"template-actions"],["class","fas fa-check-circle text-success",4,"ngIf"],[1,"fas","fa-check-circle","text-success"],[1,"empty-state","text-center","py-4"],[1,"fas","fa-search","fa-2x","text-muted","mb-2"],[1,"text-muted","mb-0"],[1,"pagination-container","mt-3"],[1,"pagination-info"],[1,"pagination-controls"],[1,"btn","btn-outline-secondary","btn-sm","mr-2",3,"click","disabled"],[1,"fas","fa-chevron-left"],[1,"pagination-current","mx-2"],[1,"btn","btn-outline-secondary","btn-sm","ml-2",3,"click","disabled"],[1,"fas","fa-chevron-right"],[1,"template-details-section"],[1,"fas","fa-list-ul","mr-2"],[1,"badge","badge-secondary","ml-2"],[1,"details-list",2,"max-height","250px","overflow-y","auto"],["class","detail-item",4,"ngFor","ngForOf"],["class","bulk-actions mt-3",4,"ngIf"],[1,"detail-item"],[1,"detail-checkbox"],[3,"checkedChange","checked"],[1,"detail-content"],[1,"detail-name"],[1,"detail-meta"],["class","meta-item",4,"ngIf"],[1,"meta-item"],[1,"fas","fa-hashtag","mr-1"],[1,"fas","fa-layer-group","mr-1"],[1,"fas","fa-inbox","fa-2x","text-muted","mb-2"],[1,"bulk-actions","mt-3"],[1,"btn","btn-outline-primary","btn-sm","mr-2",3,"click"],[1,"fas","fa-check-square","mr-1"],[1,"btn","btn-outline-secondary","btn-sm",3,"click"],[1,"fas","fa-square","mr-1"],[1,"ml-3","text-muted"],[1,"import-info"],[1,"fas","fa-info-circle","mr-1"]],template:function(o,n){if(1&o){const i=e.RV6();e.j41(0,"nb-card",7)(1,"nb-card-header"),e.nrm(2,"ngx-breadcrumb"),e.k0s(),e.j41(3,"nb-card-body")(4,"h1",8),e.EFF(5,"\u60a8\u53ef\u8207\u6b64\u7ba1\u7406\u5404\u6236\u5225\u5167\u4e4b\u76f8\u95dc\u8cc7\u8a0a\uff0c\u5305\u542b\u57fa\u672c\u8cc7\u6599\u3001\u7e73\u6b3e\u72c0\u6cc1\u3001\u4e0a\u50b3\u5ba2\u8b8a\u5716\u9762\u3001\u6aa2\u8996\u5ba2\u8b8a\u7d50\u679c\u7b49\u7b49\u3002 "),e.k0s(),e.j41(6,"div",9)(7,"div",10)(8,"div",11)(9,"label",12),e.EFF(10,"\u5efa\u6848"),e.k0s(),e.j41(11,"nb-select",13),e.mxI("ngModelChange",function(u){return e.eBV(i),e.DH7(n.searchQuery.CBuildCaseSelected,u)||(n.searchQuery.CBuildCaseSelected=u),e.Njj(u)}),e.bIt("selectedChange",function(){return e.eBV(i),e.Njj(n.onSelectionChangeBuildCase())}),e.DNE(12,se,2,2,"nb-option",14),e.k0s()()(),e.j41(13,"div",10)(14,"div",11)(15,"label",15),e.EFF(16,"\u985e\u578b"),e.k0s(),e.j41(17,"nb-select",16),e.mxI("ngModelChange",function(u){return e.eBV(i),e.DH7(n.searchQuery.CHouseTypeSelected,u)||(n.searchQuery.CHouseTypeSelected=u),e.Njj(u)}),e.DNE(18,ae,2,2,"nb-option",14),e.k0s()()(),e.j41(19,"div",10)(20,"div",17)(21,"label",18),e.EFF(22,"\u6a13 "),e.k0s(),e.j41(23,"nb-form-field",19)(24,"input",20),e.mxI("ngModelChange",function(u){return e.eBV(i),e.DH7(n.searchQuery.CFrom,u)||(n.searchQuery.CFrom=u),e.Njj(u)}),e.k0s()(),e.j41(25,"label",21),e.EFF(26,"~ "),e.k0s(),e.j41(27,"nb-form-field",22)(28,"input",23),e.mxI("ngModelChange",function(u){return e.eBV(i),e.DH7(n.searchQuery.CTo,u)||(n.searchQuery.CTo=u),e.Njj(u)}),e.k0s()()()(),e.nrm(29,"div",10),e.j41(30,"div",10)(31,"div",11)(32,"label",24),e.EFF(33," \u6236\u578b "),e.k0s(),e.j41(34,"nb-select",25),e.mxI("ngModelChange",function(u){return e.eBV(i),e.DH7(n.searchQuery.CHouseHoldSelected,u)||(n.searchQuery.CHouseHoldSelected=u),e.Njj(u)}),e.DNE(35,le,2,2,"nb-option",14),e.k0s()()(),e.j41(36,"div",10)(37,"div",11)(38,"label",26),e.EFF(39," \u7e73\u6b3e\u72c0\u614b "),e.k0s(),e.j41(40,"nb-select",27),e.mxI("ngModelChange",function(u){return e.eBV(i),e.DH7(n.searchQuery.CPayStatusSelected,u)||(n.searchQuery.CPayStatusSelected=u),e.Njj(u)}),e.DNE(41,ue,2,2,"nb-option",14),e.k0s()()(),e.j41(42,"div",10)(43,"div",11)(44,"label",28),e.EFF(45," \u9032\u5ea6 "),e.k0s(),e.j41(46,"nb-select",29),e.mxI("ngModelChange",function(u){return e.eBV(i),e.DH7(n.searchQuery.CProgressSelected,u)||(n.searchQuery.CProgressSelected=u),e.Njj(u)}),e.DNE(47,re,2,2,"nb-option",14),e.k0s()()(),e.j41(48,"div",10)(49,"div",11)(50,"label",30),e.EFF(51," \u72c0\u614b "),e.k0s(),e.j41(52,"nb-select",31),e.mxI("ngModelChange",function(u){return e.eBV(i),e.DH7(n.searchQuery.CIsEnableSeleted,u)||(n.searchQuery.CIsEnableSeleted=u),e.Njj(u)}),e.DNE(53,ce,2,2,"nb-option",14),e.k0s()()(),e.j41(54,"div",10)(55,"div",11)(56,"label",32),e.EFF(57," \u7c3d\u56de\u72c0\u614b "),e.k0s(),e.j41(58,"nb-select",33),e.mxI("ngModelChange",function(u){return e.eBV(i),e.DH7(n.searchQuery.CSignStatusSelected,u)||(n.searchQuery.CSignStatusSelected=u),e.Njj(u)}),e.DNE(59,de,2,2,"nb-option",14),e.k0s()()(),e.j41(60,"div",10)(61,"div",11)(62,"label",34),e.EFF(63," \u5831\u50f9\u55ae\u72c0\u614b "),e.k0s(),e.j41(64,"nb-select",35),e.mxI("ngModelChange",function(u){return e.eBV(i),e.DH7(n.searchQuery.CQuotationStatusSelected,u)||(n.searchQuery.CQuotationStatusSelected=u),e.Njj(u)}),e.DNE(65,pe,2,2,"nb-option",14),e.k0s()()(),e.nrm(66,"div",10),e.j41(67,"div",36)(68,"div",37)(69,"button",38),e.bIt("click",function(){return e.eBV(i),e.Njj(n.onSearch())}),e.nrm(70,"i",39),e.EFF(71,"\u67e5\u8a62 "),e.k0s()()(),e.j41(72,"div",36)(73,"div",40),e.DNE(74,me,3,0,"button",41),e.j41(75,"button",42),e.bIt("click",function(){return e.eBV(i),e.Njj(n.onNavidateId("modify-floor-plan"))}),e.nrm(76,"i",43),e.EFF(77,"\u4fee\u6539\u6a13\u5c64\u6236\u578b "),e.k0s(),e.j41(78,"button",42),e.bIt("click",function(){return e.eBV(i),e.Njj(n.exportHouse())}),e.nrm(79,"i",44),e.EFF(80,"\u532f\u51fa\u6236\u5225\u660e\u7d30\u6a94 "),e.k0s(),e.j41(81,"input",45,0),e.bIt("change",function(u){return e.eBV(i),e.Njj(n.onFileSelected(u))}),e.k0s(),e.j41(83,"button",46),e.bIt("click",function(){return e.eBV(i),e.Njj(n.triggerFileInput())}),e.nrm(84,"i",47),e.EFF(85,"\u532f\u5165\u66f4\u65b0\u6236\u5225\u660e\u7d30\u6a94 "),e.k0s()()()(),e.j41(86,"div",48)(87,"table",49)(88,"thead",50)(89,"tr")(90,"th",51),e.EFF(91,"\u6236\u578b"),e.k0s(),e.j41(92,"th",51),e.EFF(93,"\u6a13\u5c64"),e.k0s(),e.j41(94,"th",51),e.EFF(95,"\u6236\u5225"),e.k0s(),e.j41(96,"th",51),e.EFF(97,"\u985e\u578b"),e.k0s(),e.j41(98,"th",51),e.EFF(99,"\u9032\u5ea6"),e.k0s(),e.j41(100,"th",51),e.EFF(101,"\u7e73\u6b3e\u72c0\u614b"),e.k0s(),e.j41(102,"th",51),e.EFF(103,"\u7c3d\u56de\u72c0\u614b"),e.k0s(),e.j41(104,"th",51),e.EFF(105,"\u5831\u50f9\u55ae\u72c0\u614b"),e.k0s(),e.j41(106,"th",51),e.EFF(107,"\u72c0\u614b"),e.k0s(),e.j41(108,"th",52),e.EFF(109,"\u64cd\u4f5c"),e.k0s()()(),e.j41(110,"tbody"),e.DNE(111,ge,36,13,"tr",53),e.k0s()()()(),e.j41(112,"nb-card-footer",54)(113,"ngb-pagination",55),e.mxI("pageChange",function(u){return e.eBV(i),e.DH7(n.pageIndex,u)||(n.pageIndex=u),e.Njj(u)}),e.bIt("pageChange",function(u){return e.eBV(i),e.Njj(n.pageChanged(u))}),e.k0s()()(),e.DNE(114,Me,7,2,"ng-template",null,1,e.C5r)(116,De,21,4,"ng-template",null,2,e.C5r)(118,Ae,70,13,"ng-template",null,3,e.C5r)(120,Je,34,9,"ng-template",null,4,e.C5r)}2&o&&(e.R7$(11),e.R50("ngModel",n.searchQuery.CBuildCaseSelected),e.R7$(),e.Y8G("ngForOf",n.userBuildCaseOptions),e.R7$(5),e.R50("ngModel",n.searchQuery.CHouseTypeSelected),e.R7$(),e.Y8G("ngForOf",n.houseTypeOptions),e.R7$(6),e.R50("ngModel",n.searchQuery.CFrom),e.R7$(4),e.R50("ngModel",n.searchQuery.CTo),e.R7$(6),e.R50("ngModel",n.searchQuery.CHouseHoldSelected),e.R7$(),e.Y8G("ngForOf",n.houseHoldOptions),e.R7$(5),e.R50("ngModel",n.searchQuery.CPayStatusSelected),e.R7$(),e.Y8G("ngForOf",n.payStatusOptions),e.R7$(5),e.R50("ngModel",n.searchQuery.CProgressSelected),e.R7$(),e.Y8G("ngForOf",n.progressOptions),e.R7$(5),e.R50("ngModel",n.searchQuery.CIsEnableSeleted),e.R7$(),e.Y8G("ngForOf",n.cIsEnableOptions),e.R7$(5),e.R50("ngModel",n.searchQuery.CSignStatusSelected),e.R7$(),e.Y8G("ngForOf",n.signStatusOptions),e.R7$(5),e.R50("ngModel",n.searchQuery.CQuotationStatusSelected),e.R7$(),e.Y8G("ngForOf",n.quotationStatusOptions),e.R7$(9),e.Y8G("ngIf",n.isCreate),e.R7$(37),e.Y8G("ngForOf",n.houseList),e.R7$(2),e.R50("page",n.pageIndex),e.Y8G("pageSize",n.pageSize)("collectionSize",n.totalRecords))},dependencies:[p.MD,p.Sq,p.bT,$.G,d.me,d.Q0,d.BC,d.tU,d.VZ,d.zX,d.vS,r.SrT,r.KH5,r.VDm,r.zJv,r.DMy,r.i6h,r.S81,r.ZJ2,r.u_9,r.IEe,r.tHu,r.OA9,r.k2z,R.s5,j.D,w.F,r.TgK,K.y],styles:['.card[_ngcontent-%COMP%]{transition:all .3s ease}.card[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 8px 25px #0000001a!important}.tax-section[_ngcontent-%COMP%]{background-color:#f8f9fa!important;border:1px solid #e5e7eb;border-radius:8px;transition:all .2s ease;position:relative}.tax-section[_ngcontent-%COMP%]:hover{background-color:#f3f4f6!important;transform:translateY(-1px);box-shadow:0 4px 12px #0000001a}.tax-section[_ngcontent-%COMP%]:before{content:"";position:absolute;top:0;left:0;width:4px;height:100%;background-color:#0891b2;border-radius:4px 0 0 4px;transition:width .2s ease}.tax-section[_ngcontent-%COMP%]:hover:before{width:6px}.tax-icon-wrapper[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;background-color:#0891b2;display:flex;align-items:center;justify-content:center;box-shadow:0 2px 8px #0891b24d;transition:all .2s ease}.tax-icon-wrapper[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.1rem;color:#fff!important}.tax-icon-wrapper[_ngcontent-%COMP%]:hover{transform:scale(1.05);background-color:#0e7490;box-shadow:0 4px 12px #0891b266}.tax-percentage[_ngcontent-%COMP%]{font-size:.75rem;padding:.25rem .5rem;border-radius:12px;animation:_ngcontent-%COMP%_pulse 2s infinite}@keyframes _ngcontent-%COMP%_pulse{0%{transform:scale(1)}50%{transform:scale(1.05)}to{transform:scale(1)}}.tax-amount[_ngcontent-%COMP%]{transition:all .2s ease;font-weight:600}.tax-amount[_ngcontent-%COMP%]:hover{transform:scale(1.02);color:#0e7490!important}.text-primary[_ngcontent-%COMP%]{color:#2563eb!important}.text-info[_ngcontent-%COMP%]{color:#0891b2!important}hr[_ngcontent-%COMP%]{border-top:1px solid #e5e7eb;opacity:1;margin:1rem 0}.h5[_ngcontent-%COMP%], .h6[_ngcontent-%COMP%]{transition:all .2s ease;font-weight:600}.text-primary.fw-bold[_ngcontent-%COMP%]{color:#2563eb!important;font-weight:700!important;transition:all .2s ease}.text-primary.fw-bold[_ngcontent-%COMP%]:hover{transform:scale(1.01);color:#1d4ed8!important}.fa-info-circle[_ngcontent-%COMP%]{opacity:.7;transition:opacity .2s ease}.fa-info-circle[_ngcontent-%COMP%]:hover{opacity:1}@media (max-width: 768px){.card-body[_ngcontent-%COMP%]{padding:1.5rem!important}.h4[_ngcontent-%COMP%], .h5[_ngcontent-%COMP%], .h6[_ngcontent-%COMP%]{font-size:1rem!important}.tax-icon-wrapper[_ngcontent-%COMP%]{width:35px;height:35px}.tax-icon-wrapper[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1rem}.tax-section[_ngcontent-%COMP%]{padding:1rem!important}.tax-section[_ngcontent-%COMP%]:before{width:3px}.tax-section[_ngcontent-%COMP%]:hover:before{width:4px}}.template-selection-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{color:#495057;font-weight:600;border-bottom:2px solid #e9ecef;padding-bottom:.5rem}.template-selection-section[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .input-group-text[_ngcontent-%COMP%]{background-color:#f8f9fa;border-color:#ced4da;color:#6c757d}.template-selection-section[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]{border-color:#d1d5db;transition:all .2s ease;border-radius:6px}.template-selection-section[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus{border-color:#2563eb;box-shadow:0 0 0 3px #2563eb1a;outline:none}.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]{border:1px solid #e9ecef;border-radius:.375rem;background-color:#fff;min-height:400px;max-height:500px;overflow-y:auto}.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]{padding:.75rem 1rem;border-bottom:1px solid #f1f3f4;cursor:pointer;transition:all .3s ease;display:flex;justify-content:space-between;align-items:center}.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]:last-child{border-bottom:none}.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]:hover{background-color:#f8f9fa;transform:translate(4px)}.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item.selected[_ngcontent-%COMP%]{background-color:#eff6ff;border-left:4px solid #2563eb;transform:translate(4px)}.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item.selected[_ngcontent-%COMP%]   .template-name[_ngcontent-%COMP%]{color:#2563eb;font-weight:600}.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]{flex:1}.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .template-name[_ngcontent-%COMP%]{font-weight:500;margin-bottom:.25rem;transition:color .3s ease}.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .template-name[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#6c757d}.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .template-description[_ngcontent-%COMP%]{font-size:.875rem;line-height:1.4}.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-actions[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.25rem;opacity:0;transition:opacity .3s ease}.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item.selected[_ngcontent-%COMP%]   .template-actions[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{opacity:1}.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]{color:#6c757d}.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{opacity:.5}.template-details-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{color:#495057;font-weight:600;border-bottom:2px solid #e9ecef;padding-bottom:.5rem}.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]{border:1px solid #e9ecef;border-radius:.375rem;background-color:#fff}.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]{padding:.75rem 1rem;border-bottom:1px solid #f1f3f4;display:flex;align-items:center;transition:background-color .3s ease}.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]:last-child{border-bottom:none}.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]:hover{background-color:#f8f9fa}.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-checkbox[_ngcontent-%COMP%]{margin-right:1rem;flex-shrink:0}.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]{flex:1}.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-name[_ngcontent-%COMP%]{margin-bottom:.25rem;color:#212529}.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-meta[_ngcontent-%COMP%]{display:flex;gap:1rem;flex-wrap:wrap}.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]{font-size:.75rem;color:#6c757d;display:flex;align-items:center}.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-right:.25rem;opacity:.7}.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]{color:#6c757d}.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{opacity:.5}.template-details-section[_ngcontent-%COMP%]   .bulk-actions[_ngcontent-%COMP%]{padding:.75rem;background-color:#f8f9fa;border-radius:.375rem;border:1px solid #e9ecef}.template-details-section[_ngcontent-%COMP%]   .bulk-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{transition:all .3s ease}.template-details-section[_ngcontent-%COMP%]   .bulk-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover{transform:translateY(-1px)}.template-details-section[_ngcontent-%COMP%]   .pagination-container[_ngcontent-%COMP%]   .pagination-info[_ngcontent-%COMP%]{color:#6c757d;font-size:.875rem}.template-details-section[_ngcontent-%COMP%]   .pagination-container[_ngcontent-%COMP%]   .pagination-controls[_ngcontent-%COMP%]{display:flex;align-items:center}.template-details-section[_ngcontent-%COMP%]   .pagination-container[_ngcontent-%COMP%]   .pagination-controls[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{transition:all .3s ease;border-radius:.375rem}.template-details-section[_ngcontent-%COMP%]   .pagination-container[_ngcontent-%COMP%]   .pagination-controls[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover:not(:disabled){transform:translateY(-1px);box-shadow:0 2px 4px #0000001a}.template-details-section[_ngcontent-%COMP%]   .pagination-container[_ngcontent-%COMP%]   .pagination-controls[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:disabled{opacity:.5;cursor:not-allowed}.template-details-section[_ngcontent-%COMP%]   .pagination-container[_ngcontent-%COMP%]   .pagination-controls[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.75rem}.template-details-section[_ngcontent-%COMP%]   .pagination-container[_ngcontent-%COMP%]   .pagination-controls[_ngcontent-%COMP%]   .pagination-current[_ngcontent-%COMP%]{font-weight:500;color:#495057;font-size:.875rem;white-space:nowrap}.btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{transition:all .2s ease;font-size:.875rem}.btn[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%]{transform:scale(1.1)}.btn.btn-info[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#ffffffe6}.btn.btn-info[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]:hover{color:#fff}.btn.btn-outline-success[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#28a745;margin-right:.25rem}.btn.btn-secondary[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .btn.btn-danger[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .btn.btn-warning[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .btn.btn-primary[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#ffffffe6}.btn.btn-outline-secondary[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#6c757d}.table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{margin:1px;font-size:.75rem;padding:.25rem .5rem}.table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.75rem;margin-right:.25rem}.d-flex[_ngcontent-%COMP%]   .btn.btn-info[_ngcontent-%COMP%]{box-shadow:0 2px 4px #17a2b833;transition:all .3s ease}.d-flex[_ngcontent-%COMP%]   .btn.btn-info[_ngcontent-%COMP%]:hover{transform:translateY(-1px);box-shadow:0 4px 8px #17a2b84d}.btn-secondary[_ngcontent-%COMP%]{background-color:#6c757d;border-color:#6c757d;box-shadow:0 2px 4px #6c757d33;transition:all .3s ease}.btn-secondary[_ngcontent-%COMP%]:hover{background-color:#5a6268;border-color:#545b62;transform:translateY(-1px);box-shadow:0 4px 8px #6c757d4d}nb-card-footer[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{padding:.5rem 1.5rem;font-weight:500;border-radius:.375rem;transition:all .3s ease}nb-card-footer[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.875rem;margin-right:.5rem}nb-card-footer[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover{transform:translateY(-1px);box-shadow:0 4px 8px #00000026}']})}}return s})();var qe=c(5892);let Y=(()=>{class s{transform(t){switch(t){case 1:return"\u5f8c\u53f0";case 2:return"\u524d\u53f0";default:return""}}static{this.\u0275fac=function(o){return new(o||s)}}static{this.\u0275pipe=e.EJ8({name:"specialChangeSource",type:s,pure:!0,standalone:!0})}}return s})();const We=["fileInput"],Ke=()=>[];function Ze(s,l){if(1&s){const t=e.RV6();e.j41(0,"button",17),e.bIt("click",function(){e.eBV(t);const n=e.XpG(),i=e.sdS(35);return e.Njj(n.addNew(i))}),e.EFF(1," \u4e0a\u50b3\u5716\u9762"),e.k0s()}}function et(s,l){if(1&s){const t=e.RV6();e.j41(0,"button",20),e.bIt("click",function(){e.eBV(t);const n=e.XpG().$implicit,i=e.XpG(),a=e.sdS(35);return e.Njj(i.onEdit(a,n))}),e.EFF(1,"\u6aa2\u8996"),e.k0s()}}function tt(s,l){if(1&s&&(e.j41(0,"tr",11)(1,"td"),e.EFF(2),e.nI1(3,"specialChangeSource"),e.k0s(),e.j41(4,"td"),e.EFF(5),e.k0s(),e.j41(6,"td"),e.EFF(7),e.k0s(),e.j41(8,"td"),e.EFF(9),e.k0s(),e.j41(10,"td"),e.EFF(11),e.k0s(),e.j41(12,"td",18),e.DNE(13,et,2,0,"button",19),e.k0s()()),2&s){const t=l.$implicit,o=e.XpG();e.R7$(2),e.JRh(e.bMT(3,6,t.CSource)),e.R7$(3),e.JRh(o.formatDate(t.CChangeDate)),e.R7$(2),e.JRh(t.CDrawingName),e.R7$(2),e.JRh(o.formatDate(t.CCreateDT)),e.R7$(2),e.JRh(null==t.CIsApprove?"\u5f85\u5be9\u6838":t.CIsApprove?"\u901a\u904e":"\u99c1\u56de"),e.R7$(2),e.Y8G("ngIf",o.isUpdate)}}function nt(s,l){if(1&s){const t=e.RV6();e.j41(0,"button",35),e.bIt("click",function(){e.eBV(t);const n=e.XpG().dialogRef,i=e.XpG();return e.Njj(i.onSaveSpecialChange(n))}),e.EFF(1,"\u9001\u51fa\u5be9\u6838"),e.k0s()}}function ot(s,l){if(1&s){const t=e.RV6();e.j41(0,"nb-card",21)(1,"nb-card-header"),e.EFF(2),e.k0s(),e.j41(3,"nb-card-body",22)(4,"div",23)(5,"label",24,1),e.EFF(7," \u8a0e\u8ad6\u65e5\u671f "),e.k0s(),e.j41(8,"p-calendar",25),e.mxI("ngModelChange",function(n){e.eBV(t);const i=e.XpG();return e.DH7(i.formSpecialChange.CChangeDate,n)||(i.formSpecialChange.CChangeDate=n),e.Njj(n)}),e.k0s()(),e.j41(9,"div",23)(10,"label",26),e.EFF(11," \u5716\u9762\u540d\u7a31 "),e.k0s(),e.j41(12,"input",27),e.mxI("ngModelChange",function(n){e.eBV(t);const i=e.XpG();return e.DH7(i.formSpecialChange.CDrawingName,n)||(i.formSpecialChange.CDrawingName=n),e.Njj(n)}),e.k0s()(),e.j41(13,"div",23)(14,"label",28),e.EFF(15," \u9078\u6a23\u7d50\u679c "),e.k0s(),e.j41(16,"app-file-upload",29),e.bIt("multiFileSelected",function(n){e.eBV(t);const i=e.XpG();return e.Njj(i.onMultiFileSelected(n))})("nameAutoFilled",function(n){e.eBV(t);const i=e.XpG();return e.Njj(i.onNameAutoFilled(n))}),e.k0s()(),e.j41(17,"div",30)(18,"label",31),e.EFF(19,"\u5be9\u6838\u8aaa\u660e"),e.k0s(),e.j41(20,"textarea",32),e.mxI("ngModelChange",function(n){e.eBV(t);const i=e.XpG();return e.DH7(i.formSpecialChange.CApproveRemark,n)||(i.formSpecialChange.CApproveRemark=n),e.Njj(n)}),e.k0s()(),e.j41(21,"div",14)(22,"button",33),e.bIt("click",function(){const n=e.eBV(t).dialogRef,i=e.XpG();return e.Njj(i.onClose(n))}),e.EFF(23,"\u53d6\u6d88"),e.k0s(),e.DNE(24,nt,2,0,"button",34),e.k0s()()()}if(2&s){const t=e.XpG();e.R7$(2),e.Lme(" \u6236\u5225\u7ba1\u7406 > \u6d3d\u8ac7\u7d00\u9304\u4e0a\u50b3 > ",t.house.CHousehold," \xa0 ",t.house.CFloor,"F "),e.R7$(6),e.Y8G("appendTo","CChangeDate")("iconDisplay","input")("showIcon",!0),e.R50("ngModel",t.formSpecialChange.CChangeDate),e.Y8G("disabled",t.isEdit)("showButtonBar",!0),e.R7$(4),e.R50("ngModel",t.formSpecialChange.CDrawingName),e.Y8G("disabled",t.isEdit),e.R7$(4),e.Y8G("config",t.fileUploadConfig)("fileList",t.imageUrlList)("existingFiles",t.isEdit&&t.SpecialChange&&t.SpecialChange.CFileRes?t.SpecialChange.CFileRes:e.lJ4(16,Ke)),e.R7$(4),e.Y8G("disabled",t.isEdit),e.R50("ngModel",t.formSpecialChange.CApproveRemark),e.R7$(4),e.Y8G("ngIf",!t.isEdit)}}let it=(()=>{class s extends E.${constructor(t,o,n,i,a,u,m,F,S){super(t),this._allow=t,this.dialogService=o,this.valid=n,this._specialChangeService=i,this._houseService=a,this.route=u,this.message=m,this.location=F,this._eventService=S,this.imageUrlList=[],this.isEdit=!1,this.fileUploadConfig={acceptedTypes:["image/jpeg","image/jpg","application/pdf","application/acad","application/x-autocad","image/vnd.dwg","image/x-dwg"],acceptedFileRegex:/pdf|jpg|jpeg|png|dwg|dxf/i,acceptAttribute:"image/jpeg, image/jpg, application/pdf, .dwg, .dxf",label:"",helpText:"\u652f\u63f4\u683c\u5f0f\uff1a\u5716\u7247 (JPG, JPEG)\u3001PDF\u3001CAD (DWG, DXF)",required:!1,disabled:!1,autoFillName:!0,buttonText:"\u9078\u64c7\u6a94\u6848",buttonIcon:"fa fa-upload",maxFileSize:10,multiple:!0,showPreview:!0},this.statusOptions=[{value:0,key:"allow",label:"\u5141\u8a31"},{value:1,key:"not allowed",label:"\u4e0d\u5141\u8a31"}],this.pageFirst=1,this.pageSize=10,this.pageIndex=1,this.totalRecords=0,this.listPictures=[]}ngOnInit(){this.route.paramMap.subscribe(t=>{if(t){const o=t.get("id1");this.buildCaseId=o?+o:0;const i=t.get("id2");this.houseId=i?+i:0,this.getListSpecialChange(),this.getHouseById()}}),this.updateFileUploadConfig()}updateFileUploadConfig(){this.fileUploadConfig={...this.fileUploadConfig,disabled:this.isEdit,hideSelectButton:this.isEdit}}openPdfInNewTab(t){t&&t.CFileRes.CFile&&window.open(t.CFileRes.CFile,"_blank")}getListSpecialChange(){this._specialChangeService.apiSpecialChangeGetListSpecialChangePost$Json({body:{CHouseId:this.houseId,PageIndex:this.pageIndex,PageSize:this.pageSize}}).subscribe(t=>{t.TotalItems&&t.Entries&&0==t.StatusCode&&(this.listSpecialChange=t.Entries??[],this.totalRecords=t.TotalItems)})}getHouseById(){this._houseService.apiHouseGetHouseByIdPost$Json({body:{CHouseID:this.houseId}}).subscribe(t=>{t.TotalItems&&t.Entries&&0==t.StatusCode&&(this.house=t.Entries,this.houseTitle=`${this.house.CHousehold} ${this.house.CFloor}F`)})}getSpecialChangeById(t,o){this._specialChangeService.apiSpecialChangeGetSpecialChangeByIdPost$Json({body:o}).subscribe(n=>{n.TotalItems&&n.Entries&&0==n.StatusCode&&(this.SpecialChange=n.Entries,this.formSpecialChange={CApproveRemark:this.SpecialChange.CApproveRemark,CBuildCaseID:this.buildCaseId,CDrawingName:this.SpecialChange.CDrawingName,CHouseID:this.houseId,SpecialChangeFiles:null},this.SpecialChange.CChangeDate&&(this.formSpecialChange.CChangeDate=new Date(this.SpecialChange.CChangeDate)),this.dialogService.open(t))})}onSaveSpecialChange(t){this.validation(),this.valid.errorMessages.length>0?this.message.showErrorMSGs(this.valid.errorMessages):this._specialChangeService.apiSpecialChangeSaveSpecialChangePost$Json({body:this.formatParam()}).subscribe(o=>{0===o.StatusCode&&(this.message.showSucessMSG("\u57f7\u884c\u6210\u529f"),this.getListSpecialChange(),t.close())})}pageChanged(t){this.pageIndex=t,this.getListSpecialChange()}addNew(t){this.imageUrlList=[],this.isEdit=!1,this.updateFileUploadConfig(),this.formSpecialChange={CApproveRemark:"",CBuildCaseID:this.buildCaseId,CChangeDate:"",CDrawingName:"",CHouseID:this.houseId,SpecialChangeFiles:null},this.dialogService.open(t)}onEdit(t,o){this.imageUrlList=[],this.isEdit=!0,this.updateFileUploadConfig(),this.getSpecialChangeById(t,o.CSpecialChangeID)}onMultiFileSelected(t){this.imageUrlList=t}onNameAutoFilled(t){this.formSpecialChange.CDrawingName||(this.formSpecialChange.CDrawingName=t)}validation(){this.valid.clear(),this.valid.required("[\u8a0e\u8ad6\u65e5\u671f]",this.formSpecialChange.CChangeDate),this.valid.required("[\u5716\u9762\u540d\u7a31]",this.formSpecialChange.CDrawingName),this.valid.required("[\u5be9\u6838\u8aaa\u660e]",this.formSpecialChange.CApproveRemark)}formatDate(t){return t?T(t).format("YYYY-MM-DD"):""}deleteDataFields(t){for(const o of t)delete o.data;return t}formatParam(){const t={...this.formSpecialChange,SpecialChangeFiles:this.imageUrlList};return this.deleteDataFields(t.SpecialChangeFiles),this.formSpecialChange.CChangeDate&&(t.CChangeDate=this.formatDate(this.formSpecialChange.CChangeDate)),t}onClose(t){t.close()}removeBase64Prefix(t){const o=t.indexOf(",");return-1!==o?t.substring(o+1):t}removeImage(t){this.listPictures=this.listPictures.filter(o=>o.id!=t)}uploadImage(t){}renameFile(t,o){var n=this.listPictures[o].CFile.slice(0,this.listPictures[o].CFile.size,this.listPictures[o].CFile.type),i=new File([n],t.target.value+"."+this.listPictures[o].extension,{type:this.listPictures[o].CFile.type});this.listPictures[o].CFile=i}goBack(){this._eventService.push({action:"GET_BUILDCASE",payload:this.buildCaseId}),this.location.back()}openNewTab(t){t&&window.open(t,"_blank")}static{this.\u0275fac=function(o){return new(o||s)(e.rXU(I.O),e.rXU(r.S0W),e.rXU(P.E),e.rXU(h.X),e.rXU(h.SD),e.rXU(C.nX),e.rXU(M.b),e.rXU(p.aZ),e.rXU(v.U))}}static{this.\u0275cmp=e.VBU({type:s,selectors:[["ngx-customer-change-picture"]],viewQuery:function(o,n){if(1&o&&e.GBs(We,5),2&o){let i;e.mGM(i=e.lsd())&&(n.fileInput=i.first)}},features:[e.Vt3],decls:36,vars:6,consts:[["dialogUploadDrawing",""],["CChangeDate",""],["accent","success"],[1,"font-bold","text-[#818181]"],[1,"d-flex","flex-wrap"],[1,"col-md-12"],[1,"d-flex","justify-content-end","w-full"],["class","btn btn-info",3,"click",4,"ngIf"],[1,"table-responsive","mt-4"],[1,"table",2,"min-width","1000px"],[1,"table-header"],[1,"text-center"],["scope","col",1,"col-1"],["class","text-center",4,"ngFor","ngForOf"],[1,"d-flex","justify-content-center"],["aria-label","Pagination",3,"pageChange","page","pageSize","collectionSize"],[1,"btn","btn-secondary","btn-sm",3,"click"],[1,"btn","btn-info",3,"click"],[1,"w-32"],["class","btn btn-outline-primary btn-sm m-1",3,"click",4,"ngIf"],[1,"btn","btn-outline-primary","btn-sm","m-1",3,"click"],[2,"min-width","600px","max-height","95vh"],[1,"px-4"],[1,"form-group"],["for","CChangeDate","baseLabel","",1,"required-field","mr-4",2,"min-width","75px"],["placeholder","\u5e74/\u6708/\u65e5","inputId","icondisplay","dateFormat","yy/mm/dd",1,"!w-[400px]",3,"ngModelChange","appendTo","iconDisplay","showIcon","ngModel","disabled","showButtonBar"],["for","cDrawingName","baseLabel","",1,"required-field","mr-4",2,"min-width","75px"],["type","text","nbInput","","placeholder","\u5716\u9762\u540d\u7a31",1,"w-full",3,"ngModelChange","ngModel","disabled"],["for","cIsEnable","baseLabel","",1,"mr-4",2,"min-width","75px"],["labelMinWidth","0px",3,"multiFileSelected","nameAutoFilled","config","fileList","existingFiles"],[1,"form-group","d-flex","align-items-center"],["for","cApproveRemark","baseLabel","",1,"required-field","align-self-start","mr-4",2,"min-width","75px"],["name","remark","id","cApproveRemark","rows","5","nbInput","",1,"w-full",2,"resize","none",3,"ngModelChange","disabled","ngModel"],[1,"btn","btn-outline-secondary","m-2",3,"click"],["class","btn btn-success m-2",3,"click",4,"ngIf"],[1,"btn","btn-success","m-2",3,"click"]],template:function(o,n){if(1&o){const i=e.RV6();e.j41(0,"nb-card",2)(1,"nb-card-header"),e.EFF(2),e.k0s(),e.j41(3,"nb-card-body")(4,"h1",3),e.EFF(5,"\u60a8\u53ef\u8207\u6b64\u4e0a\u50b3\u8207\u8a72\u6236\u5225\u5ba2\u6236\u8a0e\u8ad6\u7684\u5ba2\u6236\u5716\u9762\uff0c\u5be9\u6838\u901a\u904e\u5f8c\u5ba2\u6236\u5c31\u53ef\u4ee5\u5728\u524d\u53f0\u6aa2\u8996\u8a72\u5716\u9762\u3002"),e.k0s(),e.j41(6,"div",4)(7,"div",5)(8,"div",6),e.DNE(9,Ze,2,0,"button",7),e.k0s()()(),e.j41(10,"div",8)(11,"table",9)(12,"thead",10)(13,"tr",11)(14,"th",12),e.EFF(15,"\u4f86\u6e90"),e.k0s(),e.j41(16,"th",12),e.EFF(17,"\u8a0e\u8ad6\u65e5\u671f"),e.k0s(),e.j41(18,"th",12),e.EFF(19,"\u5716\u9762\u540d\u7a31"),e.k0s(),e.j41(20,"th",12),e.EFF(21,"\u4e0a\u50b3\u65e5\u671f"),e.k0s(),e.j41(22,"th",12),e.EFF(23,"\u5be9\u6838\u72c0\u614b"),e.k0s(),e.j41(24,"th",12),e.EFF(25,"\u64cd\u4f5c"),e.k0s()()(),e.j41(26,"tbody"),e.DNE(27,tt,14,8,"tr",13),e.k0s()()()(),e.j41(28,"nb-card-footer",14)(29,"ngb-pagination",15),e.mxI("pageChange",function(u){return e.eBV(i),e.DH7(n.pageIndex,u)||(n.pageIndex=u),e.Njj(u)}),e.bIt("pageChange",function(u){return e.eBV(i),e.Njj(n.pageChanged(u))}),e.k0s()(),e.j41(30,"nb-card-footer")(31,"div",14)(32,"button",16),e.bIt("click",function(){return e.eBV(i),e.Njj(n.goBack())}),e.EFF(33," \u8fd4\u56de\u4e0a\u4e00\u9801 "),e.k0s()()()(),e.DNE(34,ot,25,17,"ng-template",null,0,e.C5r)}2&o&&(e.R7$(2),e.SpI(" \u6236\u5225\u7ba1\u7406 > \u6d3d\u8ac7\u7d00\u9304\u4e0a\u50b3 > ",n.houseTitle," "),e.R7$(7),e.Y8G("ngIf",n.isCreate),e.R7$(18),e.Y8G("ngForOf",n.listSpecialChange),e.R7$(2),e.R50("page",n.pageIndex),e.Y8G("pageSize",n.pageSize)("collectionSize",n.totalRecords))},dependencies:[p.Sq,p.bT,d.me,d.BC,d.vS,r.SrT,r.KH5,r.VDm,r.zJv,r.i6h,R.s5,w.F,qe.P,B.Vv,Y],styles:["#icondisplay{width:318px}  [id^=pn_id_]{z-index:10}.file-type-container[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]{transition:all .2s ease-in-out}.file-type-container[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]:hover{transform:translateY(-2px)}.file-type-container[_ngcontent-%COMP%]   .file-type-label[_ngcontent-%COMP%]{-webkit-backdrop-filter:blur(4px);backdrop-filter:blur(4px);background-color:#000000b3}.file-type-container[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]{transition:all .2s ease-in-out}.file-type-container[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]:hover{transform:scale(1.1)}.pdf-icon[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse 2s infinite}@keyframes _ngcontent-%COMP%_pulse{0%,to{opacity:1}50%{opacity:.7}}.cad-icon[_ngcontent-%COMP%]{transition:transform .3s ease}.cad-icon[_ngcontent-%COMP%]:hover{transform:rotate(15deg)}"]})}}return s})();var st=c(9908),at=c(5446);function lt(s,l){if(1&s){const t=e.RV6();e.j41(0,"tr",11)(1,"td"),e.EFF(2),e.k0s(),e.j41(3,"td"),e.EFF(4),e.k0s(),e.j41(5,"td"),e.EFF(6),e.nI1(7,"date"),e.k0s(),e.j41(8,"td"),e.EFF(9),e.nI1(10,"dateFormat"),e.k0s(),e.j41(11,"td"),e.EFF(12),e.nI1(13,"getDocumentStatus"),e.k0s(),e.j41(14,"td",17)(15,"button",18),e.bIt("click",function(){const n=e.eBV(t).$implicit,i=e.XpG();return e.Njj(i.openPdfInNewTab(n))}),e.EFF(16,"\u6aa2\u8996"),e.k0s()()()}if(2&s){const t=l.$implicit;e.R7$(2),e.JRh(!0===t.CIsChange?"\u5ba2\u8b8a":!1===t.CIsChange?"\u9078\u6a23":""),e.R7$(2),e.JRh(t.CDocumentName),e.R7$(2),e.JRh(e.i5U(7,5,t.CCreateDt,"yyyy/MM/dd HH:mm:ss")),e.R7$(3),e.JRh(t.CSignDate?e.bMT(10,8,t.CSignDate):""),e.R7$(3),e.JRh(e.bMT(13,10,t.CDocumentStatus))}}function ut(s,l){if(1&s){const t=e.RV6();e.j41(0,"tr")(1,"td")(2,"nb-checkbox",37),e.mxI("checkedChange",function(n){const i=e.eBV(t).$implicit;return e.DH7(i.isChecked,n)||(i.isChecked=n),e.Njj(n)}),e.k0s()(),e.j41(3,"td"),e.EFF(4),e.nI1(5,"specialChangeSource"),e.k0s(),e.j41(6,"td"),e.EFF(7),e.k0s(),e.j41(8,"td"),e.EFF(9),e.k0s(),e.j41(10,"td"),e.EFF(11),e.k0s(),e.j41(12,"td"),e.EFF(13),e.k0s()()}if(2&s){const t=l.$implicit,o=e.XpG(2);e.R7$(2),e.R50("checked",t.isChecked),e.R7$(2),e.JRh(e.bMT(5,6,t.CSource)),e.R7$(3),e.JRh(o.formatDate(t.CChangeDate)),e.R7$(2),e.JRh(t.CDrawingName),e.R7$(2),e.JRh(o.formatDate(t.CCreateDT)),e.R7$(2),e.JRh(o.formatDate(t.CApproveDate))}}function rt(s,l){if(1&s){const t=e.RV6();e.j41(0,"nb-card",19)(1,"nb-card-header"),e.EFF(2),e.k0s(),e.j41(3,"nb-card-body",20)(4,"h4"),e.EFF(5," \u8acb\u78ba\u8a8d\u8981\u5c07\u54ea\u4e9b\u5716\u9762\u6574\u5408\u70ba\u4e00\u4efd\u6587\u4ef6\u4f9b\u5ba2\u6236\u7c3d\u540d\u78ba\u8a8d\u3002 "),e.k0s(),e.j41(6,"div",21)(7,"label",22),e.EFF(8," \u6587\u4ef6\u540d\u7a31 "),e.k0s(),e.j41(9,"input",23),e.mxI("ngModelChange",function(n){e.eBV(t);const i=e.XpG();return e.DH7(i.finalDoc.CDocumentName,n)||(i.finalDoc.CDocumentName=n),e.Njj(n)}),e.k0s()(),e.j41(10,"div",21)(11,"label",24),e.EFF(12," \u9078\u6a23\u7d50\u679c "),e.k0s(),e.j41(13,"nb-checkbox",25),e.mxI("checkedChange",function(n){e.eBV(t);const i=e.XpG();return e.DH7(i.isChecked,n)||(i.isChecked=n),e.Njj(n)}),e.EFF(14,"\u9078\u6a23\u7d50\u679c "),e.k0s()(),e.j41(15,"div",21)(16,"label",26),e.EFF(17," \u5ba2\u8b8a\u5716 "),e.k0s(),e.j41(18,"h4"),e.EFF(19,"\u50c5\u80fd\u52fe\u9078\u5df2\u901a\u904e\u5be9\u6838\u4e4b\u5716\u9762\u3002"),e.k0s()(),e.j41(20,"table",27)(21,"thead",10)(22,"tr"),e.nrm(23,"td"),e.j41(24,"th"),e.EFF(25,"\u4f86\u6e90"),e.k0s(),e.j41(26,"th"),e.EFF(27,"\u8a0e\u8ad6\u65e5\u671f"),e.k0s(),e.j41(28,"th"),e.EFF(29,"\u5716\u9762\u540d\u7a31"),e.k0s(),e.j41(30,"th"),e.EFF(31,"\u4e0a\u50b3\u65e5\u671f"),e.k0s(),e.j41(32,"th"),e.EFF(33,"\u901a\u904e\u65e5\u671f"),e.k0s()()(),e.j41(34,"tbody"),e.DNE(35,ut,14,8,"tr",28),e.k0s()(),e.j41(36,"div",29)(37,"label",30),e.EFF(38,"\u9001\u5be9\u8cc7\u8a0a "),e.j41(39,"p",31),e.EFF(40,"\u5167\u90e8\u5be9\u6838\u4eba\u54e1\u67e5\u770b"),e.k0s()(),e.j41(41,"textarea",32),e.mxI("ngModelChange",function(n){e.eBV(t);const i=e.XpG();return e.DH7(i.finalDoc.CApproveRemark,n)||(i.finalDoc.CApproveRemark=n),e.Njj(n)}),e.EFF(42,"        "),e.k0s()(),e.j41(43,"div",29)(44,"label",33),e.EFF(45,"\u6458\u8981\u8a3b\u8a18 "),e.j41(46,"p",31),e.EFF(47,"\u5ba2\u6236\u65bc\u6587\u4ef6\u4e2d\u67e5\u770b"),e.k0s()(),e.j41(48,"textarea",34),e.mxI("ngModelChange",function(n){e.eBV(t);const i=e.XpG();return e.DH7(i.finalDoc.CNote,n)||(i.finalDoc.CNote=n),e.Njj(n)}),e.EFF(49,"        "),e.k0s()()(),e.j41(50,"nb-card-footer",14)(51,"button",35),e.bIt("click",function(){const n=e.eBV(t).dialogRef,i=e.XpG();return e.Njj(i.onClose(n))}),e.EFF(52,"\u53d6\u6d88"),e.k0s(),e.j41(53,"button",36),e.bIt("click",function(){const n=e.eBV(t).dialogRef,i=e.XpG();return e.Njj(i.onCreateFinalDoc(n))}),e.EFF(54,"\u78ba\u8a8d\u9001\u51fa\u5be9\u6838"),e.k0s()()()}if(2&s){const t=e.XpG();e.R7$(2),e.Lme(" \u6236\u5225\u7ba1\u7406 > \u5ba2\u8b8a\u78ba\u8a8d\u5716\u8aaa > ",t.houseByID.CHousehold," ",t.houseByID.CFloor,"F "),e.R7$(7),e.R50("ngModel",t.finalDoc.CDocumentName),e.R7$(4),e.R50("checked",t.isChecked),e.R7$(22),e.Y8G("ngForOf",t.listSpecialChangeAvailable),e.R7$(6),e.R50("ngModel",t.finalDoc.CApproveRemark),e.R7$(7),e.R50("ngModel",t.finalDoc.CNote)}}let ct=(()=>{class s extends E.${constructor(t,o,n,i,a,u,m,F,S,D){super(t),this._allow=t,this.dialogService=o,this.valid=n,this._finalDocumentService=i,this.message=a,this.route=u,this.location=m,this._eventService=F,this._houseService=S,this.fileService=D,this.documentStatusOptions=["\u5f85\u5be9\u6838","\u5df2\u99c1\u56de","\u5f85\u5ba2\u6236\u7c3d\u56de","\u5ba2\u6236\u5df2\u7c3d\u56de"],this.isChecked=!0}ngOnInit(){this.route.paramMap.subscribe(t=>{t&&(this.buildCaseId=+(t.get("id1")??0),this.houseID=+(t.get("id2")??0),this.houseID&&this.getHouseById(),this.getListFinalDoc())})}getHouseById(){this._houseService.apiHouseGetHouseByIdPost$Json({body:{CHouseID:this.houseID}}).subscribe(t=>{t.Entries&&0==t.StatusCode&&(this.houseByID=t.Entries)})}openPdfInNewTab(t){t&&t.CFile&&this.fileService.getFile(t.CFile,t.CDocumentName||"document.pdf").subscribe({next:o=>{const n=URL.createObjectURL(o);window.open(n,"_blank"),setTimeout(()=>URL.revokeObjectURL(n),1e4)},error:o=>{console.error("\u53d6\u5f97\u6a94\u6848\u5931\u6557:",o),this.message.showErrorMSG("\u7121\u6cd5\u958b\u555f\u6a94\u6848\uff0c\u8acb\u7a0d\u5f8c\u518d\u8a66")}})}getListFinalDoc(){this._finalDocumentService.apiFinalDocumentGetListFinalDocPost$Json({body:{CHouseID:this.houseID,PageIndex:this.pageIndex,PageSize:this.pageSize}}).subscribe(t=>{t.TotalItems&&t.Entries&&0==t.StatusCode&&(this.listFinalDoc=t.Entries??[],this.totalRecords=t.TotalItems)})}getListSpecialChangeAvailable(){this._finalDocumentService.apiFinalDocumentGetListSpecialChangeAvailablePost$Json({body:{CHouseID:this.houseID}}).subscribe(t=>{this.listSpecialChangeAvailable=[],t.TotalItems&&t.Entries&&0==t.StatusCode&&(this.listSpecialChangeAvailable=t.Entries??[],t.Entries.length&&(this.listSpecialChangeAvailable=t.Entries.map(o=>({...o,isChecked:!1}))))})}validation(){this.valid.clear(),this.valid.required("[\u6587\u4ef6\u540d\u7a31]",this.finalDoc.CDocumentName),this.valid.required("[\u9001\u5be9\u8cc7\u8a0a]",this.finalDoc.CApproveRemark),this.valid.required("[\u7cfb\u7d71\u64cd\u4f5c\u8aaa\u660e]",this.finalDoc.CNote)}goBack(){this._eventService.push({action:"GET_BUILDCASE",payload:this.buildCaseId}),this.location.back()}getCheckedCIDs(t){return t&&t.length?t.filter(o=>o.isChecked).map(o=>o.CID):[]}onCreateFinalDoc(t){if(this.validation(),this.valid.errorMessages.length>0)return void this.message.showErrorMSGs(this.valid.errorMessages);const o={...this.finalDoc,CSpecialChange:this.getCheckedCIDs(this.listSpecialChangeAvailable)};this._finalDocumentService.apiFinalDocumentCreateFinalDocPost$Json({body:o}).subscribe(n=>{0===n.StatusCode?(this.getListFinalDoc(),this.message.showSucessMSG("\u57f7\u884c\u6210\u529f"),t.close()):this.message.showErrorMSG(n.Message)})}pageChanged(t){this.pageIndex=t,this.getListFinalDoc()}addNew(t){this.finalDoc={CHouseID:this.houseID,CDocumentName:"",CApproveRemark:"",CNote:""},this.getListSpecialChangeAvailable(),this.dialogService.open(t)}onOpenModel(t){this.dialogService.open(t)}onClose(t){t.close()}formatDate(t){return T(t).format("YYYY/MM/DD HH:mm")}static{this.\u0275fac=function(o){return new(o||s)(e.rXU(I.O),e.rXU(r.S0W),e.rXU(P.E),e.rXU(h.RG),e.rXU(M.b),e.rXU(C.nX),e.rXU(p.aZ),e.rXU(v.U),e.rXU(h.SD),e.rXU(h.E2))}}static{this.\u0275cmp=e.VBU({type:s,selectors:[["ngx-sample-selection-result"]],features:[e.Vt3],decls:38,vars:4,consts:[["dialogConfirmImage",""],["accent","success"],[2,"font-size","32px"],[1,"font-bold","text-[#818181]"],[1,"d-flex","flex-wrap"],[1,"col-md-12"],[1,"d-flex","justify-content-end","w-full"],[1,"btn","btn-info",3,"click"],[1,"table-responsive","mt-4"],[1,"table",2,"min-width","1000px"],[1,"table-header"],[1,"text-center"],["scope","col",1,"col-1"],["class","text-center",4,"ngFor","ngForOf"],[1,"d-flex","justify-content-center"],["aria-label","Pagination",3,"pageChange","page","pageSize","collectionSize"],[1,"btn","btn-secondary","btn-sm",3,"click"],[1,"w-32"],[1,"btn","btn-outline-primary","btn-sm","m-1",3,"click"],[2,"width","1000px","max-height","95vh"],[1,"px-4"],[1,"form-group"],["for","CDocumentName","baseLabel","",1,"required-field","mr-4",2,"min-width","75px"],["type","text","nbInput","","placeholder","\u6587\u4ef6\u540d\u7a31",1,"w-full",3,"ngModelChange","ngModel"],["for","isChecked","baseLabel","",1,"required-field","mr-4",2,"min-width","75px"],["status","basic","disabled","",3,"checkedChange","checked"],["for","\u5ba2\u8b8a\u5716","baseLabel","",1,"mr-4",2,"min-width","75px"],[1,"table",2,"min-width","600px"],[4,"ngFor","ngForOf"],[1,"form-group","d-flex","align-items-center"],["for","remark","baseLabel","",1,"required-field","align-self-start","col-3"],[2,"color","red"],["name","remark","id","remark","rows","5","nbInput","",1,"w-full",2,"resize","none","max-width","none",3,"ngModelChange","ngModel"],["for","CNote","baseLabel","",1,"required-field","align-self-start","col-3"],["name","CNote","id","CNote","rows","5","nbInput","",1,"w-full",2,"resize","none","max-width","none",3,"ngModelChange","ngModel"],[1,"btn","btn-outline-secondary","m-2",3,"click"],[1,"btn","btn-success","m-2",3,"click"],["status","basic",3,"checkedChange","checked"]],template:function(o,n){if(1&o){const i=e.RV6();e.j41(0,"nb-card",1)(1,"nb-card-header")(2,"div",2),e.EFF(3,"\u6236\u5225\u7ba1\u7406 / \u5ba2\u8b8a\u78ba\u8a8d\u5716\u8aaa"),e.k0s()(),e.j41(4,"nb-card-body")(5,"h1",3),e.EFF(6,"\u60a8\u53ef\u8207\u6b64\u6aa2\u8996\u8a72\u6236\u5225\u5ba2\u6236\u5c0d\u65bc\u9078\u6a23\u7d50\u679c\u4e4b\u7c3d\u8a8d\u6587\u4ef6\uff0c\u4e26\u53ef\u9078\u64c7\u8981\u5c07\u54ea\u4e9b\u5ba2\u8b8a\u5716\u9762\u6574\u5408\u70ba\u4e00\u4efd\u5716\u9762\u8acb\u5ba2\u6236\u7c3d\u56de\u78ba\u8a8d\u3002 \u5982\u679c\u8a72\u4f4d\u5ba2\u6236\u6709\u591a\u4efd\u7c3d\u56de\u6a94\u6848\uff0c\u65bc\u5ba2\u6236\u7aef\u50c5\u6703\u986f\u793a\u6700\u65b0\u7684\u4e00\u4efd\u6587\u4ef6\u3002"),e.k0s(),e.j41(7,"div",4)(8,"div",5)(9,"div",6)(10,"button",7),e.bIt("click",function(){e.eBV(i);const u=e.sdS(37);return e.Njj(n.addNew(u))}),e.EFF(11," \u65b0\u589e\u78ba\u8a8d\u5ba2\u8b8a\u5716"),e.k0s()()()(),e.j41(12,"div",8)(13,"table",9)(14,"thead",10)(15,"tr",11)(16,"th",12),e.EFF(17,"\u985e\u578b"),e.k0s(),e.j41(18,"th",12),e.EFF(19,"\u6587\u4ef6\u540d\u7a31"),e.k0s(),e.j41(20,"th",12),e.EFF(21,"\u5efa\u7acb\u65e5\u671f "),e.k0s(),e.j41(22,"th",12),e.EFF(23,"\u7c3d\u56de\u65e5\u671f "),e.k0s(),e.j41(24,"th",12),e.EFF(25,"\u72c0\u614b"),e.k0s(),e.j41(26,"th",12),e.EFF(27,"\u64cd\u4f5c"),e.k0s()()(),e.j41(28,"tbody"),e.DNE(29,lt,17,12,"tr",13),e.k0s()()()(),e.j41(30,"nb-card-footer",14)(31,"ngb-pagination",15),e.mxI("pageChange",function(u){return e.eBV(i),e.DH7(n.pageIndex,u)||(n.pageIndex=u),e.Njj(u)}),e.bIt("pageChange",function(u){return e.eBV(i),e.Njj(n.pageChanged(u))}),e.k0s()(),e.j41(32,"nb-card-footer")(33,"div",14)(34,"button",16),e.bIt("click",function(){return e.eBV(i),e.Njj(n.goBack())}),e.EFF(35," \u8fd4\u56de\u4e0a\u4e00\u9801 "),e.k0s()()()(),e.DNE(36,rt,55,7,"ng-template",null,0,e.C5r)}2&o&&(e.R7$(29),e.Y8G("ngForOf",n.listFinalDoc),e.R7$(2),e.R50("page",n.pageIndex),e.Y8G("pageSize",n.pageSize)("collectionSize",n.totalRecords))},dependencies:[p.Sq,d.me,d.BC,d.vS,r.SrT,r.KH5,r.VDm,r.zJv,r.DMy,r.i6h,R.s5,w.F,p.vh,st.a,at.UW,Y]})}}return s})();function dt(s,l){if(1&s&&(e.j41(0,"nb-option",28),e.EFF(1),e.k0s()),2&s){const t=l.$implicit;e.Y8G("value",t),e.R7$(),e.SpI(" ",t.label," ")}}function pt(s,l){if(1&s){const t=e.RV6();e.j41(0,"th")(1,"div",34)(2,"nb-checkbox",35),e.bIt("checkedChange",function(n){const i=e.eBV(t).index,a=e.XpG(3);return e.Njj(a.enableAllAtIndex(n,i))}),e.j41(3,"span",36),e.EFF(4,"\u5168\u9078\u7121\u6b64\u6236\u578b "),e.k0s()()()()}if(2&s){const t=l.index,o=e.XpG(3);e.R7$(2),e.Y8G("checked",o.isCheckAllColumnChecked(t))}}function mt(s,l){if(1&s&&(e.j41(0,"tr"),e.nrm(1,"th"),e.DNE(2,pt,5,1,"th",33),e.k0s()),2&s){const t=e.XpG(2);e.R7$(2),e.Y8G("ngForOf",t.houseList[0])}}function ht(s,l){if(1&s){const t=e.RV6();e.j41(0,"td")(1,"div",34)(2,"p",37),e.EFF(3),e.k0s(),e.j41(4,"nb-checkbox",35),e.mxI("checkedChange",function(n){const i=e.eBV(t).$implicit;return e.DH7(i.CIsSelected,n)||(i.CIsSelected=n),e.Njj(n)}),e.j41(5,"span",36),e.EFF(6,"\u7121\u6b64\u6236\u578b"),e.k0s()()()()}if(2&s){const t=l.$implicit;e.R7$(3),e.Lme("",t.CHouseHold||"null"," - ",t.CFloor,""),e.R7$(),e.R50("checked",t.CIsSelected)}}function gt(s,l){if(1&s){const t=e.RV6();e.j41(0,"tr")(1,"td")(2,"div",34)(3,"p"),e.EFF(4,"\xa0"),e.k0s(),e.j41(5,"nb-checkbox",35),e.bIt("checkedChange",function(n){const i=e.eBV(t).$implicit,a=e.XpG(2);return e.Njj(a.enableAllRow(n,i))}),e.j41(6,"span",36),e.EFF(7,"\u5168\u9078\u7121\u6b64\u6236\u578b"),e.k0s()()()(),e.DNE(8,ht,7,3,"td",33),e.k0s()}if(2&s){const t=l.$implicit,o=e.XpG(2);e.R7$(5),e.Y8G("checked",o.isCheckAllRowChecked(t)),e.R7$(3),e.Y8G("ngForOf",t)}}function _t(s,l){if(1&s&&(e.j41(0,"div",29)(1,"table",30)(2,"thead",31),e.DNE(3,mt,3,1,"tr",32),e.k0s(),e.j41(4,"tbody"),e.DNE(5,gt,9,2,"tr",33),e.k0s()()()),2&s){const t=e.XpG();e.R7$(3),e.Y8G("ngIf",t.houseList.length),e.R7$(2),e.Y8G("ngForOf",t.houseList)}}let Ct=(()=>{class s extends E.${constructor(t,o,n,i,a,u,m,F){super(t),this._allow=t,this.dialogService=o,this._houseService=n,this.route=i,this.location=a,this.message=u,this.router=m,this._eventService=F,this.buildingSelectedOptions=[{value:"",label:"\u5168\u90e8"}],this.isHouseList=!1}getListBuilding(){this._houseService.apiHouseGetListBuildingPost$Json({body:{CBuildCaseID:this.buildCaseId}}).subscribe(t=>{t.Entries&&0==t.StatusCode&&(this.buildingSelectedOptions=[{value:"",label:"\u5168\u90e8"},...t.Entries.map(o=>({value:o,label:o}))])})}clear(){this.searchQuery={CFrom:1,CTo:100,CBuildingNameSelected:this.buildingSelectedOptions[0]}}groupByFloor(t){const o=[],n=Array.from(new Set(t.map(i=>i.CFloor).filter(i=>null!==i)));for(const i of n)o.push([]);for(const i of t){const a=n.indexOf(i.CFloor);-1!==a&&o[a].push({...i,CIsSelected:!1===i.CIsEnable})}return o}sortByFloorDescending(t){return t.sort((o,n)=>(n.CFloor??0)-(o.CFloor??0))}getHouseList(){this.isHouseList=!1,this.buildCaseId&&this._houseService.apiHouseGetHouseListPost$Json({body:{CBuildCaseID:this.buildCaseId,CBuildingName:this.searchQuery.CBuildingNameSelected.value||null,CFloor:{CFrom:this.searchQuery.CFrom,CTo:this.searchQuery.CTo},CIsPagi:!1}}).subscribe(t=>{if(0==t.StatusCode&&t.Entries){const o=this.sortByFloorDescending(t.Entries);this.houseList=this.groupByFloor(o),this.isHouseList=!0}})}goBack(){this._eventService.push({action:"GET_BUILDCASE",payload:this.buildCaseId}),this.location.back()}onSubmit(){let t=this.houseList.flat().map(o=>({CIsEnable:!o.CIsSelected,CHouseID:o.CID}));this._houseService.apiHouseEditListHousePost$Json({body:{mode:1,Args:t}}).subscribe(o=>{0==o.StatusCode&&(this.message.showSucessMSG("\u57f7\u884c\u6210\u529f"),console.log(o),this.getHouseList())})}isCheckAllRowChecked(t){return t.every(o=>o.CIsSelected)}isCheckAllColumnChecked(t){if(this.isHouseList){if(t<0||t>=this.houseList[0].length)throw new Error("Invalid index. Index must be within the bounds of the array.");for(const o of this.houseList)if(t>=o.length||!o[t].CIsSelected)return!1;return!0}return!1}enableAllAtIndex(t,o){if(o<0)throw new Error("Invalid index. Index must be a non-negative number.");for(const n of this.houseList)o<n.length&&(n[o].CIsSelected=t)}enableAllRow(t,o){for(const n of o)n.CIsSelected=t}ngOnInit(){this.searchQuery={CBuildingNameSelected:this.buildingSelectedOptions[0],CFrom:1,CTo:100},this.route.paramMap.subscribe(t=>{if(t){const o=t.get("id");this.buildCaseId=o?+o:0,this.getListBuilding(),this.getHouseList()}})}pageChanged(t){this.pageIndex=t}onOpen(t){this.dialogService.open(t)}onClose(t){t.close()}onNavigateWithId(t){this.router.navigate([`/pages/household-management/${t}`,this.buildCaseId])}static{this.\u0275fac=function(o){return new(o||s)(e.rXU(I.O),e.rXU(r.S0W),e.rXU(h.SD),e.rXU(C.nX),e.rXU(p.aZ),e.rXU(M.b),e.rXU(C.Ix),e.rXU(v.U))}}static{this.\u0275cmp=e.VBU({type:s,selectors:[["ngx-modify-floor-plan"]],features:[e.Vt3],decls:47,vars:5,consts:[["accent","success"],[1,"font-bold","text-[#818181]"],[1,"d-flex","flex-wrap"],[1,"col-md-4"],[1,"form-group","d-flex","align-items-center","w-full"],["for","buildingName",1,"label","col-3"],["placeholder","\u72c0\u614b",1,"w-full",3,"ngModelChange","ngModel"],[3,"value",4,"ngFor","ngForOf"],[1,"col-md-5"],[1,"form-group","d-flex","align-items-center"],["for","cFloorFrom",1,"label","col-3"],[1,"ml-3"],["type","number","id","search","nbInput","",1,"w-full","col-4",3,"ngModelChange","ngModel"],["for","cFloorTo",1,"label","col-1"],[1,"mr-3"],["type","number","id","search","nbInput","",1,"w-full",3,"ngModelChange","ngModel"],[1,"col-md-3"],[1,"d-flex","justify-content-end","w-full"],[1,"btn","btn-secondary","mx-2",3,"click"],[1,"btn","btn-secondary",3,"click"],[1,"fas","fa-search"],[1,"col-md-12"],[1,"btn","btn-primary",3,"click"],[1,"btn","btn-primary","mx-2",3,"click"],["class","table-responsive mt-4",4,"ngIf"],[1,"d-flex","justify-content-center"],[1,"inline"],[1,"d-flex","justify-content-center","w-full"],[3,"value"],[1,"table-responsive","mt-4"],[1,"table",2,"min-width","800px"],[1,"table-header"],[4,"ngIf"],[4,"ngFor","ngForOf"],[1,"w-max"],["status","basic",3,"checkedChange","checked"],[1,"font-medium"],[1,"font-bold"]],template:function(o,n){1&o&&(e.j41(0,"nb-card",0)(1,"nb-card-header"),e.nrm(2,"ngx-breadcrumb"),e.k0s(),e.j41(3,"nb-card-body"),e.nrm(4,"h1",1),e.j41(5,"div",2)(6,"div",3)(7,"div",4)(8,"label",5),e.EFF(9,"\u68df\u5225"),e.k0s(),e.j41(10,"nb-select",6),e.mxI("ngModelChange",function(a){return e.DH7(n.searchQuery.CBuildingNameSelected,a)||(n.searchQuery.CBuildingNameSelected=a),a}),e.DNE(11,dt,2,2,"nb-option",7),e.k0s()()(),e.j41(12,"div",8)(13,"div",9)(14,"label",10),e.EFF(15,"\u6a13 "),e.k0s(),e.j41(16,"nb-form-field",11)(17,"input",12),e.mxI("ngModelChange",function(a){return e.DH7(n.searchQuery.CFrom,a)||(n.searchQuery.CFrom=a),a}),e.k0s()(),e.j41(18,"label",13),e.EFF(19,"~ "),e.k0s(),e.j41(20,"nb-form-field",14)(21,"input",15),e.mxI("ngModelChange",function(a){return e.DH7(n.searchQuery.CTo,a)||(n.searchQuery.CTo=a),a}),e.k0s()()()(),e.j41(22,"div",16)(23,"div",17)(24,"button",18),e.bIt("click",function(){return n.clear()}),e.EFF(25," \u6e05\u9664 "),e.k0s(),e.j41(26,"button",19),e.bIt("click",function(){return n.getHouseList()}),e.EFF(27," \u67e5\u8a62 "),e.nrm(28,"i",20),e.k0s()()(),e.j41(29,"div",21)(30,"div",17)(31,"button",22),e.bIt("click",function(){return n.onNavigateWithId("modify-floor-plan")}),e.EFF(32," 1.\u8abf\u6574\u6236\u578b\u7d44\u6210 "),e.k0s(),e.j41(33,"button",23),e.bIt("click",function(){return n.onNavigateWithId("modify-household")}),e.EFF(34," 2.\u4fee\u6539\u6236\u578b\u540d\u7a31 "),e.k0s(),e.j41(35,"button",22),e.bIt("click",function(){return n.onNavigateWithId("modify-house-type")}),e.EFF(36," 3.\u8a2d\u5b9a\u5730\u4e3b\u6236 "),e.k0s()()()(),e.DNE(37,_t,6,2,"div",24),e.k0s(),e.j41(38,"nb-card-footer",25)(39,"div",26)(40,"div",27)(41,"button",22),e.bIt("click",function(){return n.goBack()}),e.EFF(42," \u8fd4\u56de\u4e0a\u4e00\u9801 "),e.k0s(),e.j41(43,"button",23),e.bIt("click",function(){return n.getHouseList()}),e.EFF(44," \u53d6\u6d88 "),e.k0s(),e.j41(45,"button",22),e.bIt("click",function(){return n.onSubmit()}),e.EFF(46," \u5132\u5b58 "),e.k0s()()()()()),2&o&&(e.R7$(10),e.R50("ngModel",n.searchQuery.CBuildingNameSelected),e.R7$(),e.Y8G("ngForOf",n.buildingSelectedOptions),e.R7$(6),e.R50("ngModel",n.searchQuery.CFrom),e.R7$(4),e.R50("ngModel",n.searchQuery.CTo),e.R7$(16),e.Y8G("ngIf",n.isHouseList))},dependencies:[p.Sq,p.bT,d.me,d.Q0,d.BC,d.vS,r.SrT,r.KH5,r.VDm,r.zJv,r.DMy,r.i6h,r.S81,r.ZJ2,r.u_9,j.D]})}}return s})();const J=s=>({"opacity-50 cursor-not-allowed":s});function ft(s,l){if(1&s&&(e.j41(0,"nb-option",28),e.EFF(1),e.k0s()),2&s){const t=l.$implicit;e.Y8G("value",t),e.R7$(),e.SpI(" ",t.label," ")}}function bt(s,l){if(1&s){const t=e.RV6();e.j41(0,"div",37)(1,"input",38),e.mxI("ngModelChange",function(n){e.eBV(t);const i=e.XpG().$implicit;return e.DH7(i.CHouseHold,n)||(i.CHouseHold=n),e.Njj(n)}),e.k0s(),e.nrm(2,"br"),e.j41(3,"span",39),e.bIt("click",function(){e.eBV(t);const n=e.XpG().$implicit,i=e.XpG(3);return e.Njj(i.clearForm(n))}),e.EFF(4," \u53d6\u6d88 "),e.k0s(),e.j41(5,"span",40),e.bIt("click",function(){e.eBV(t);const n=e.XpG(),i=n.$implicit,a=n.index,u=e.XpG(3);return e.Njj(u.onUpdateAllCol(i,a))}),e.EFF(6," \u6279\u6b21\u5132\u5b58 "),e.k0s()()}if(2&s){const t=e.XpG().$implicit;e.R7$(),e.R50("ngModel",t.CHouseHold),e.R7$(4),e.Y8G("ngClass",e.eq3(2,J,""===t.CHouseHold))}}function Ft(s,l){if(1&s){const t=e.RV6();e.j41(0,"div",41)(1,"span",42),e.bIt("click",function(){e.eBV(t);const n=e.XpG().$implicit;return e.Njj(n.isEdit=!n.isEdit)}),e.EFF(2,"\u6279\u6b21\u4fee\u6539\u540d\u7a31 "),e.k0s()()}}function St(s,l){if(1&s&&(e.j41(0,"th"),e.DNE(1,bt,7,4,"div",35)(2,Ft,3,0,"div",36),e.k0s()),2&s){const t=l.$implicit;e.R7$(),e.Y8G("ngIf",!0===t.isEdit),e.R7$(),e.Y8G("ngIf",!0!==t.isEdit)}}function Et(s,l){if(1&s&&(e.j41(0,"tr")(1,"th")(2,"span",34),e.EFF(3,"\u6a13\u5c64"),e.k0s()(),e.DNE(4,St,3,2,"th",33),e.k0s()),2&s){const t=e.XpG(2);e.R7$(4),e.Y8G("ngForOf",t.houseListItem)}}function It(s,l){if(1&s&&(e.j41(0,"td")(1,"p"),e.EFF(2),e.k0s()()),2&s){const t=e.XpG().$implicit;e.R7$(2),e.JRh(t[0].CFloor)}}function Mt(s,l){if(1&s&&(e.j41(0,"p",46),e.EFF(1),e.k0s()),2&s){const t=e.XpG().$implicit;e.R7$(),e.JRh(t.CHouseHold||"null")}}function kt(s,l){if(1&s){const t=e.RV6();e.j41(0,"div",37)(1,"input",38),e.mxI("ngModelChange",function(n){e.eBV(t);const i=e.XpG().$implicit;return e.DH7(i.CHouseHold,n)||(i.CHouseHold=n),e.Njj(n)}),e.k0s(),e.nrm(2,"br"),e.j41(3,"span",47),e.bIt("click",function(){e.eBV(t);const n=e.XpG().$implicit,i=e.XpG(3);return e.Njj(i.closeEditItem(n))}),e.EFF(4," \u53d6\u6d88 "),e.k0s(),e.j41(5,"span",48),e.bIt("click",function(){e.eBV(t);const n=e.XpG().$implicit,i=e.XpG(3);return e.Njj(i.onUpdateItem(n))}),e.EFF(6," \u5132\u5b58 "),e.k0s()()}if(2&s){const t=e.XpG().$implicit;e.R7$(),e.R50("ngModel",t.CHouseHold),e.R7$(4),e.Y8G("ngClass",e.eq3(2,J,""===t.CHouseHold))}}function Dt(s,l){if(1&s){const t=e.RV6();e.j41(0,"div",46)(1,"span",49),e.bIt("click",function(){e.eBV(t);const n=e.XpG().$implicit,i=e.XpG(3);return e.Njj(i.onEditItem(n))}),e.EFF(2,"\u4fee\u6539\u540d\u7a31 "),e.k0s()()}}function yt(s,l){if(1&s&&(e.j41(0,"td",44),e.DNE(1,Mt,2,1,"p",45)(2,kt,7,4,"div",35)(3,Dt,3,0,"div",45),e.k0s()),2&s){const t=l.$implicit;e.Y8G("ngClass",t.CIsEnable?"":"bg-slate-400"),e.R7$(),e.Y8G("ngIf",!t.isEdit),e.R7$(),e.Y8G("ngIf",t.CIsEnable&&!0===t.isEdit),e.R7$(),e.Y8G("ngIf",t.CIsEnable&&!0!==t.isEdit)}}function Ht(s,l){if(1&s&&(e.j41(0,"tr"),e.DNE(1,It,3,1,"td",32)(2,yt,4,4,"td",43),e.k0s()),2&s){const t=l.$implicit;e.R7$(),e.Y8G("ngIf",t.length),e.R7$(),e.Y8G("ngForOf",t)}}function vt(s,l){if(1&s&&(e.j41(0,"div",29)(1,"table",30)(2,"thead",31),e.DNE(3,Et,5,1,"tr",32),e.k0s(),e.j41(4,"tbody"),e.DNE(5,Ht,3,2,"tr",33),e.k0s()()()),2&s){const t=e.XpG();e.R7$(3),e.Y8G("ngIf",t.houseList.length),e.R7$(2),e.Y8G("ngForOf",t.houseList)}}let jt=(()=>{class s extends E.${constructor(t,o,n,i,a,u,m){super(t),this._allow=t,this.dialogService=o,this._houseService=n,this.route=i,this.location=a,this.message=u,this.router=m,this.buildingSelectedOptions=[{value:"",label:"\u5168\u90e8"}],this.isHouseList=!1}getListBuilding(){this._houseService.apiHouseGetListBuildingPost$Json({body:{CBuildCaseID:this.buildCaseId}}).subscribe(t=>{t.Entries&&0==t.StatusCode&&(this.buildingSelectedOptions=[{value:"",label:"\u5168\u90e8"},...t.Entries.map(o=>({value:o,label:o}))])})}groupByFloor(t){const o=[],n=Array.from(new Set(t.map(i=>i.CFloor).filter(i=>null!==i)));for(const i of n)o.push([]);for(const i of t){const a=n.indexOf(i.CFloor);-1!==a&&o[a].push(i)}return o}sortByFloorDescending(t){return t.sort((o,n)=>(n.CFloor??0)-(o.CFloor??0))}getHouseList(){this.isHouseList=!1,this.buildCaseId&&this._houseService.apiHouseGetHouseListPost$Json({body:{CBuildCaseID:this.buildCaseId,CBuildingName:this.searchQuery.CBuildingNameSelected.value||null,CFloor:{CFrom:this.searchQuery.CFrom,CTo:this.searchQuery.CTo},CIsPagi:!1}}).subscribe(t=>{if(t.Entries&&0==t.StatusCode){const o=this.sortByFloorDescending(t.Entries);this.houseList=this.groupByFloor(o),this.houseListItem=this.houseList[0].map(n=>({CHouseHold:"",isEdit:!1})),this.houseList.forEach(n=>{n.CIsEnable&&(n.isEdit=!1)}),this.isHouseList=!0}})}goBack(){this.location.back()}onSubmit(){let t=this.houseList.flat().map(o=>({CIsEnable:o.CIsEnable,CHouseID:o.CID,CHouseHold:o.CHouseHold}));this._houseService.apiHouseEditListHousePost$Json({body:{mode:2,Args:t.filter(o=>o.CIsEnable)}}).subscribe(o=>{0==o.StatusCode&&(this.message.showSucessMSG("\u57f7\u884c\u6210\u529f"),this.getHouseList())})}onEditItem(t){t.isEdit=!t.isEdit,this.currentCHouseHold=t.CHouseHold}closeEditItem(t){t.isEdit=!t.isEdit,this.currentCHouseHold&&(t.CHouseHold=this.currentCHouseHold)}onUpdateItem(t){this._houseService.apiHouseEditListHousePost$Json({body:{mode:2,Args:[{CIsEnable:t.CIsEnable,CHouseID:t.CID,CHouseHold:t.CHouseHold}]}}).subscribe(o=>{0==o.StatusCode&&(this.message.showSucessMSG("\u57f7\u884c\u6210\u529f"),this.getHouseList())})}clearForm(t){t.isEdit=!t.isEdit,t.CHouseHold=""}onUpdateAllCol(t,o){if(""===t.CHouseHold)return;let n=[];this.houseList.forEach(i=>{i[o].CIsEnable&&n.push({CHouseHold:t.CHouseHold,CHouseID:i[o].CID,CIsEnable:i[o].CIsEnable})}),this._houseService.apiHouseEditListHousePost$Json({body:{mode:2,Args:n}}).subscribe(i=>{0==i.StatusCode&&(this.message.showSucessMSG("\u57f7\u884c\u6210\u529f"),this.getHouseList())})}ngOnInit(){this.searchQuery={CBuildingNameSelected:this.buildingSelectedOptions[0],CFrom:1,CTo:100},this.route.paramMap.subscribe(t=>{if(t){const o=t.get("id");this.buildCaseId=o?+o:0,this.getListBuilding(),this.getHouseList()}})}pageChanged(t){this.pageIndex=t}onOpen(t){this.dialogService.open(t)}onClose(t){t.close()}clear(){this.searchQuery={CFrom:1,CTo:100,CBuildingNameSelected:this.buildingSelectedOptions[0]}}onNavigateWithId(t){this.router.navigate([`/pages/household-management/${t}`,this.buildCaseId])}static{this.\u0275fac=function(o){return new(o||s)(e.rXU(I.O),e.rXU(r.S0W),e.rXU(h.SD),e.rXU(C.nX),e.rXU(p.aZ),e.rXU(M.b),e.rXU(C.Ix))}}static{this.\u0275cmp=e.VBU({type:s,selectors:[["ngx-modify-household"]],features:[e.Vt3],decls:47,vars:5,consts:[["accent","success"],[1,"font-bold","text-[#818181]"],[1,"d-flex","flex-wrap"],[1,"col-md-4"],[1,"form-group","d-flex","align-items-center","w-full"],["for","buildingName",1,"label","col-3"],["placeholder","\u72c0\u614b",1,"w-full",3,"ngModelChange","ngModel"],[3,"value",4,"ngFor","ngForOf"],[1,"col-md-5"],[1,"form-group","d-flex","align-items-center"],["for","cFloorFrom",1,"label","col-3"],[1,"ml-3"],["type","number","id","search","nbInput","",1,"w-full","col-4",3,"ngModelChange","ngModel"],["for","cFloorTo",1,"label","col-1"],[1,"mr-3"],["type","number","id","search","nbInput","",1,"w-full",3,"ngModelChange","ngModel"],[1,"col-md-3"],[1,"d-flex","justify-content-end","w-full"],[1,"btn","btn-secondary","mx-2",3,"click"],[1,"btn","btn-secondary",3,"click"],[1,"fas","fa-search"],[1,"col-md-12"],[1,"btn","btn-primary",3,"click"],[1,"btn","btn-primary","mx-2",3,"click"],["class","table-responsive mt-4",4,"ngIf"],[1,"d-flex","justify-content-center"],[1,"inline"],[1,"d-flex","justify-content-center","w-full"],[3,"value"],[1,"table-responsive","mt-4"],[1,"table",2,"min-width","800px"],[1,"table-header"],[4,"ngIf"],[4,"ngFor","ngForOf"],[1,"block","w-8"],["class","font-bold float-left w-32",4,"ngIf"],["class","font-bold w-max",4,"ngIf"],[1,"font-bold","float-left","w-32"],["type","text","id","CHouseHold","nbInput","",1,"w-full",3,"ngModelChange","ngModel"],[1,"font-normal","text-blue-400","underline","inline-block","w-[40%]","hover:underline","cursor-pointer",3,"click"],[1,"font-normal","text-blue-400","underline","inline-block","w-[60%]","hover:underline","cursor-pointer",3,"click","ngClass"],[1,"font-bold","w-max"],[1,"font-normal","text-blue-400","hover:underline","underline","cursor-pointer","block","min-w-3",3,"click"],[3,"ngClass",4,"ngFor","ngForOf"],[3,"ngClass"],["class","font-bold",4,"ngIf"],[1,"font-bold"],[1,"font-normal","text-blue-400","underline","inline-block","w-[50%]","hover:underline","cursor-pointer",3,"click"],[1,"font-normal","text-blue-400","underline","inline-block","w-[50%]","hover:underline","cursor-pointer",3,"click","ngClass"],[1,"font-normal","text-blue-400","hover:underline","underline","cursor-pointer",3,"click"]],template:function(o,n){1&o&&(e.j41(0,"nb-card",0)(1,"nb-card-header"),e.nrm(2,"ngx-breadcrumb"),e.k0s(),e.j41(3,"nb-card-body"),e.nrm(4,"h1",1),e.j41(5,"div",2)(6,"div",3)(7,"div",4)(8,"label",5),e.EFF(9,"\u68df\u5225"),e.k0s(),e.j41(10,"nb-select",6),e.mxI("ngModelChange",function(a){return e.DH7(n.searchQuery.CBuildingNameSelected,a)||(n.searchQuery.CBuildingNameSelected=a),a}),e.DNE(11,ft,2,2,"nb-option",7),e.k0s()()(),e.j41(12,"div",8)(13,"div",9)(14,"label",10),e.EFF(15,"\u6a13 "),e.k0s(),e.j41(16,"nb-form-field",11)(17,"input",12),e.mxI("ngModelChange",function(a){return e.DH7(n.searchQuery.CFrom,a)||(n.searchQuery.CFrom=a),a}),e.k0s()(),e.j41(18,"label",13),e.EFF(19,"~ "),e.k0s(),e.j41(20,"nb-form-field",14)(21,"input",15),e.mxI("ngModelChange",function(a){return e.DH7(n.searchQuery.CTo,a)||(n.searchQuery.CTo=a),a}),e.k0s()()()(),e.j41(22,"div",16)(23,"div",17)(24,"button",18),e.bIt("click",function(){return n.clear()}),e.EFF(25," \u6e05\u9664 "),e.k0s(),e.j41(26,"button",19),e.bIt("click",function(){return n.getHouseList()}),e.EFF(27," \u67e5\u8a62 "),e.nrm(28,"i",20),e.k0s()()(),e.j41(29,"div",21)(30,"div",17)(31,"button",22),e.bIt("click",function(){return n.onNavigateWithId("modify-floor-plan")}),e.EFF(32," 1.\u8abf\u6574\u6236\u578b\u7d44\u6210 "),e.k0s(),e.j41(33,"button",23),e.bIt("click",function(){return n.onNavigateWithId("modify-household")}),e.EFF(34," 2.\u4fee\u6539\u6236\u578b\u540d\u7a31 "),e.k0s(),e.j41(35,"button",22),e.bIt("click",function(){return n.onNavigateWithId("modify-house-type")}),e.EFF(36," 3.\u8a2d\u5b9a\u5730\u4e3b\u6236 "),e.k0s()()()(),e.DNE(37,vt,6,2,"div",24),e.k0s(),e.j41(38,"nb-card-footer",25)(39,"div",26)(40,"div",27)(41,"button",22),e.bIt("click",function(){return n.goBack()}),e.EFF(42," \u8fd4\u56de\u4e0a\u4e00\u9801 "),e.k0s(),e.j41(43,"button",23),e.bIt("click",function(){return n.getHouseList()}),e.EFF(44," \u53d6\u6d88 "),e.k0s(),e.j41(45,"button",22),e.bIt("click",function(){return n.onSubmit()}),e.EFF(46," \u5132\u5b58 "),e.k0s()()()()()),2&o&&(e.R7$(10),e.R50("ngModel",n.searchQuery.CBuildingNameSelected),e.R7$(),e.Y8G("ngForOf",n.buildingSelectedOptions),e.R7$(6),e.R50("ngModel",n.searchQuery.CFrom),e.R7$(4),e.R50("ngModel",n.searchQuery.CTo),e.R7$(16),e.Y8G("ngIf",n.isHouseList))},dependencies:[p.YU,p.Sq,p.bT,d.me,d.Q0,d.BC,d.vS,r.SrT,r.KH5,r.VDm,r.zJv,r.i6h,r.S81,r.ZJ2,r.u_9,j.D]})}}return s})();function xt(s,l){if(1&s&&(e.j41(0,"nb-option",28),e.EFF(1),e.k0s()),2&s){const t=l.$implicit;e.Y8G("value",t),e.R7$(),e.SpI(" ",t.label," ")}}function Bt(s,l){if(1&s){const t=e.RV6();e.j41(0,"th",35)(1,"div",37)(2,"nb-checkbox",38),e.bIt("checkedChange",function(n){const i=e.eBV(t).index,a=e.XpG(3);return e.Njj(a.enableAllAtIndex(n,i))}),e.j41(3,"span",39),e.EFF(4,"\u5168\u9078\u70ba\u5730\u4e3b\u6236 "),e.k0s()()()()}if(2&s){const t=l.index,o=e.XpG(3);e.R7$(2),e.Y8G("checked",o.isCheckAllColumnChecked(t))}}function Tt(s,l){if(1&s&&(e.j41(0,"tr",34),e.nrm(1,"th",35),e.DNE(2,Bt,5,1,"th",36),e.k0s()),2&s){const t=e.XpG(2);e.R7$(2),e.Y8G("ngForOf",t.houseList[0])}}function Pt(s,l){if(1&s){const t=e.RV6();e.j41(0,"nb-checkbox",38),e.mxI("checkedChange",function(n){e.eBV(t);const i=e.XpG().$implicit;return e.DH7(i.CHouseTypeBool,n)||(i.CHouseTypeBool=n),e.Njj(n)}),e.j41(1,"span",39),e.EFF(2," \u662f\u5730\u4e3b\u6236 "),e.k0s()()}if(2&s){const t=e.XpG().$implicit;e.R50("checked",t.CHouseTypeBool)}}function Rt(s,l){if(1&s&&(e.j41(0,"td",41)(1,"div",37)(2,"p",42),e.EFF(3),e.k0s(),e.DNE(4,Pt,3,1,"nb-checkbox",43),e.k0s()()),2&s){const t=l.$implicit;e.Y8G("ngClass",t.CIsEnable?"":"bg-slate-400"),e.R7$(3),e.Lme("",t.CHouseHold||"null"," - ",t.CFloor,""),e.R7$(),e.Y8G("ngIf",t.CIsEnable)}}function At(s,l){if(1&s){const t=e.RV6();e.j41(0,"tr",34)(1,"td",35)(2,"div",37)(3,"p"),e.EFF(4,"\xa0"),e.k0s(),e.j41(5,"nb-checkbox",38),e.bIt("checkedChange",function(n){const i=e.eBV(t).$implicit,a=e.XpG(2);return e.Njj(a.enableAllRow(n,i))}),e.j41(6,"span",39),e.EFF(7,"\u5168\u9078\u70ba\u5730\u4e3b\u6236 "),e.k0s()()()(),e.DNE(8,Rt,5,4,"td",40),e.k0s()}if(2&s){const t=l.$implicit,o=e.XpG(2);e.R7$(5),e.Y8G("checked",o.isCheckAllRowChecked(t)),e.R7$(3),e.Y8G("ngForOf",t)}}function Ot(s,l){if(1&s&&(e.j41(0,"div",29)(1,"table",30)(2,"thead",31),e.DNE(3,Tt,3,1,"tr",32),e.k0s(),e.j41(4,"tbody"),e.DNE(5,At,9,2,"tr",33),e.k0s()()()),2&s){const t=e.XpG();e.R7$(3),e.Y8G("ngIf",t.houseList.length),e.R7$(2),e.Y8G("ngForOf",t.houseList)}}let Nt=(()=>{class s extends E.${constructor(t,o,n,i,a,u,m){super(t),this._allow=t,this.dialogService=o,this._houseService=n,this.route=i,this.location=a,this.message=u,this.router=m,this.buildingSelectedOptions=[{value:"",label:"\u5168\u90e8"}],this.isHouseList=!1}getListBuilding(){this._houseService.apiHouseGetListBuildingPost$Json({body:{CBuildCaseID:this.buildCaseId}}).subscribe(t=>{t.Entries&&0==t.StatusCode&&(this.buildingSelectedOptions=[{value:"",label:"\u5168\u90e8"},...t.Entries.map(o=>({value:o,label:o}))])})}groupByFloor(t){const o=[],n=Array.from(new Set(t.map(i=>i.CFloor).filter(i=>null!==i)));for(const i of n)o.push([]);for(const i of t){const a=n.indexOf(i.CFloor);if(-1!==a){let u={...i};i.CIsEnable&&(u={...u,CHouseTypeBool:!(!i.CHouseType||1!=i.CHouseType)}),o[a].push(u)}}return o}sortByFloorDescending(t){return t.sort((o,n)=>(n.CFloor??0)-(o.CFloor??0))}getHouseList(){this.isHouseList=!1,this.buildCaseId&&this._houseService.apiHouseGetHouseListPost$Json({body:{CBuildCaseID:this.buildCaseId,CBuildingName:this.searchQuery.CBuildingNameSelected.value||null,CFloor:{CFrom:this.searchQuery.CFrom,CTo:this.searchQuery.CTo},CIsPagi:!1}}).subscribe(t=>{if(t.Entries&&0==t.StatusCode){const o=this.sortByFloorDescending(t.Entries);this.houseList=this.groupByFloor(o),this.isHouseList=!0,console.log("this.houseList",this.houseList)}})}goBack(){this.location.back()}onSubmit(){let t=this.houseList.flat().map(o=>({CHouseType:!0===o.CHouseTypeBool?1:2,CHouseID:o.CID,CIsEnable:o.CIsEnable}));this._houseService.apiHouseEditListHousePost$Json({body:{mode:3,Args:t.filter(o=>o.CIsEnable)}}).subscribe(o=>{0==o.StatusCode&&(this.message.showSucessMSG("\u57f7\u884c\u6210\u529f"),this.getHouseList())})}isCheckAllRowChecked(t){let o=0;for(let n=0;n<t.length;n++){const i=t[n];if(!i.CHouseTypeBool&&i.CIsEnable)return!1;i.CIsEnable||(o+=1)}return o!==t.length}isCheckAllColumnChecked(t){if(this.isHouseList){if(t<0||t>=this.houseList[0].length)throw new Error("Invalid index. Index must be within the bounds of the array.");let o=0;for(const n of this.houseList){if(n[t].CIsEnable){if(t>=n.length||!n[t].CHouseTypeBool)return!1}else o+=1;if(o===this.houseList.length)return!1}return!0}return!1}enableAllAtIndex(t,o){if(o<0)throw new Error("Invalid index. Index must be a non-negative number.");for(const n of this.houseList)o<n.length&&n[o].CIsEnable&&(n[o].CHouseTypeBool=t)}enableAllRow(t,o){for(const n of o)n.CIsEnable&&(n.CHouseTypeBool=t)}ngOnInit(){this.searchQuery={CBuildingNameSelected:this.buildingSelectedOptions[0],CFrom:1,CTo:100},this.route.paramMap.subscribe(t=>{if(t){const o=t.get("id");this.buildCaseId=o?+o:0,this.getListBuilding(),this.getHouseList()}})}pageChanged(t){this.pageIndex=t}clear(){this.searchQuery={CFrom:1,CTo:100,CBuildingNameSelected:this.buildingSelectedOptions[0]}}onOpen(t){this.dialogService.open(t)}onClose(t){t.close()}onNavigateWithId(t){this.router.navigate([`/pages/household-management/${t}`,this.buildCaseId])}static{this.\u0275fac=function(o){return new(o||s)(e.rXU(I.O),e.rXU(r.S0W),e.rXU(h.SD),e.rXU(C.nX),e.rXU(p.aZ),e.rXU(M.b),e.rXU(C.Ix))}}static{this.\u0275cmp=e.VBU({type:s,selectors:[["ngx-modify-house-type"]],features:[e.Vt3],decls:47,vars:5,consts:[["accent","success"],[1,"font-bold","text-[#818181]"],[1,"d-flex","flex-wrap"],[1,"col-md-4"],[1,"form-group","d-flex","align-items-center","w-full"],["for","buildingName",1,"label","col-3"],["placeholder","\u72c0\u614b",1,"w-full",3,"ngModelChange","ngModel"],[3,"value",4,"ngFor","ngForOf"],[1,"col-md-5"],[1,"form-group","d-flex","align-items-center"],["for","cFloorFrom",1,"label","col-3"],[1,"ml-3"],["type","number","id","search","nbInput","",1,"w-full","col-4",3,"ngModelChange","ngModel"],["for","cFloorTo",1,"label","col-1"],[1,"mr-3"],["type","number","id","search","nbInput","",1,"w-full",3,"ngModelChange","ngModel"],[1,"col-md-3"],[1,"d-flex","justify-content-end","w-full"],[1,"btn","btn-secondary","mx-2",3,"click"],[1,"btn","btn-secondary",3,"click"],[1,"fas","fa-search"],[1,"col-md-12"],[1,"btn","btn-primary",3,"click"],[1,"btn","btn-primary","mx-2",3,"click"],["class","table-responsive mt-4",4,"ngIf"],[1,"d-flex","justify-content-center"],[1,"inline"],[1,"d-flex","justify-content-center","w-full"],[3,"value"],[1,"table-responsive","mt-4"],[1,"table",2,"min-width","1000px"],[1,"table-header"],["class","text-center",4,"ngIf"],["class","text-center",4,"ngFor","ngForOf"],[1,"text-center"],[1,"px-1"],["class"," px-1",4,"ngFor","ngForOf"],[1,"w-max"],["status","basic",3,"checkedChange","checked"],[1,"font-medium"],[3,"ngClass",4,"ngFor","ngForOf"],[3,"ngClass"],[1,"font-bold"],["status","basic",3,"checked","checkedChange",4,"ngIf"]],template:function(o,n){1&o&&(e.j41(0,"nb-card",0)(1,"nb-card-header"),e.nrm(2,"ngx-breadcrumb"),e.k0s(),e.j41(3,"nb-card-body"),e.nrm(4,"h1",1),e.j41(5,"div",2)(6,"div",3)(7,"div",4)(8,"label",5),e.EFF(9,"\u68df\u5225"),e.k0s(),e.j41(10,"nb-select",6),e.mxI("ngModelChange",function(a){return e.DH7(n.searchQuery.CBuildingNameSelected,a)||(n.searchQuery.CBuildingNameSelected=a),a}),e.DNE(11,xt,2,2,"nb-option",7),e.k0s()()(),e.j41(12,"div",8)(13,"div",9)(14,"label",10),e.EFF(15,"\u6a13 "),e.k0s(),e.j41(16,"nb-form-field",11)(17,"input",12),e.mxI("ngModelChange",function(a){return e.DH7(n.searchQuery.CFrom,a)||(n.searchQuery.CFrom=a),a}),e.k0s()(),e.j41(18,"label",13),e.EFF(19,"~ "),e.k0s(),e.j41(20,"nb-form-field",14)(21,"input",15),e.mxI("ngModelChange",function(a){return e.DH7(n.searchQuery.CTo,a)||(n.searchQuery.CTo=a),a}),e.k0s()()()(),e.j41(22,"div",16)(23,"div",17)(24,"button",18),e.bIt("click",function(){return n.clear()}),e.EFF(25," \u6e05\u9664 "),e.k0s(),e.j41(26,"button",19),e.bIt("click",function(){return n.getHouseList()}),e.EFF(27," \u67e5\u8a62 "),e.nrm(28,"i",20),e.k0s()()(),e.j41(29,"div",21)(30,"div",17)(31,"button",22),e.bIt("click",function(){return n.onNavigateWithId("modify-floor-plan")}),e.EFF(32," 1.\u8abf\u6574\u6236\u578b\u7d44\u6210 "),e.k0s(),e.j41(33,"button",23),e.bIt("click",function(){return n.onNavigateWithId("modify-household")}),e.EFF(34," 2.\u4fee\u6539\u6236\u578b\u540d\u7a31 "),e.k0s(),e.j41(35,"button",22),e.bIt("click",function(){return n.onNavigateWithId("modify-house-type")}),e.EFF(36," 3.\u8a2d\u5b9a\u5730\u4e3b\u6236 "),e.k0s()()()(),e.DNE(37,Ot,6,2,"div",24),e.k0s(),e.j41(38,"nb-card-footer",25)(39,"div",26)(40,"div",27)(41,"button",22),e.bIt("click",function(){return n.goBack()}),e.EFF(42," \u8fd4\u56de\u4e0a\u4e00\u9801 "),e.k0s(),e.j41(43,"button",23),e.bIt("click",function(){return n.getHouseList()}),e.EFF(44," \u53d6\u6d88 "),e.k0s(),e.j41(45,"button",22),e.bIt("click",function(){return n.onSubmit()}),e.EFF(46," \u5132\u5b58 "),e.k0s()()()()()),2&o&&(e.R7$(10),e.R50("ngModel",n.searchQuery.CBuildingNameSelected),e.R7$(),e.Y8G("ngForOf",n.buildingSelectedOptions),e.R7$(6),e.R50("ngModel",n.searchQuery.CFrom),e.R7$(4),e.R50("ngModel",n.searchQuery.CTo),e.R7$(16),e.Y8G("ngIf",n.isHouseList))},dependencies:[p.YU,p.Sq,p.bT,d.me,d.Q0,d.BC,d.vS,r.SrT,r.KH5,r.VDm,r.zJv,r.DMy,r.i6h,r.S81,r.ZJ2,r.u_9,j.D]})}}return s})();function wt(s,l){if(1&s&&(e.j41(0,"nb-option",20),e.EFF(1),e.k0s()),2&s){const t=l.$implicit;e.Y8G("value",t),e.R7$(),e.SpI(" ",t.label," ")}}function Gt(s,l){if(1&s&&(e.j41(0,"span"),e.EFF(1),e.k0s()),2&s){const t=l.$implicit;e.R7$(),e.SpI(" ",t.CHouseHold," \u3001 ")}}function Qt(s,l){if(1&s&&(e.j41(0,"tr")(1,"td"),e.EFF(2),e.k0s(),e.j41(3,"td"),e.DNE(4,Gt,2,1,"span",17),e.k0s()()),2&s){const t=l.$implicit;e.R7$(2),e.JRh(t.CFileName),e.R7$(2),e.Y8G("ngForOf",t.CHouse)}}function $t(s,l){if(1&s){const t=e.RV6();e.j41(0,"button",27),e.bIt("click",function(){e.eBV(t);const n=e.XpG().dialogRef,i=e.XpG();return e.Njj(i.submitEditHouseRegularPic(n))}),e.EFF(1,"\u4e0a\u50b3\u6a94\u6848"),e.k0s()}}function Lt(s,l){if(1&s){const t=e.RV6();e.j41(0,"label",32)(1,"nb-checkbox",34),e.mxI("checkedChange",function(n){const i=e.eBV(t).$implicit;return e.DH7(i.CIsSelect,n)||(i.CIsSelect=n),e.Njj(n)}),e.bIt("checkedChange",function(n){const i=e.eBV(t).$implicit,a=e.XpG().index,u=e.XpG(2);return e.Njj(u.checkItem(n,a,i))}),e.k0s(),e.j41(2,"span",39),e.EFF(3),e.k0s()()}if(2&s){const t=l.$implicit;e.R7$(),e.R50("checked",t.CIsSelect),e.R7$(2),e.JRh(t.CHouseHold||"null")}}function Vt(s,l){if(1&s){const t=e.RV6();e.j41(0,"div",28)(1,"div",29)(2,"label",30),e.EFF(3),e.k0s()(),e.j41(4,"div")(5,"label",31),e.EFF(6," \u9069\u7528\u6236\u578b "),e.k0s(),e.j41(7,"label",32)(8,"span",33),e.EFF(9,"\u9069\u7528\u6236\u578b "),e.k0s(),e.j41(10,"nb-checkbox",34),e.bIt("checkedChange",function(n){const i=e.eBV(t).$implicit,a=e.XpG(2);return e.Njj(a.checkAll(n,i))}),e.EFF(11,"\u5168\u9078 "),e.k0s()(),e.j41(12,"div",35),e.DNE(13,Lt,4,2,"label",36),e.j41(14,"div",37)(15,"button",38),e.bIt("click",function(){const n=e.eBV(t).$implicit,i=e.XpG(2);return e.Njj(i.onDeleteHouseRegularPic(n))}),e.EFF(16," \u522a\u9664 "),e.k0s()()()()()}if(2&s){const t=l.$implicit,o=e.XpG(2);e.R7$(3),e.SpI(" ",t.CFileName," "),e.R7$(7),e.Y8G("checked",o.isAllChecked(t)),e.R7$(3),e.Y8G("ngForOf",t.CHouse)}}function Xt(s,l){if(1&s){const t=e.RV6();e.j41(0,"nb-card",21)(1,"nb-card-header"),e.EFF(2," \u6236\u5225\u7ba1\u7406\u300b\u8a2d\u5b9a\u6236\u578b\u6a19\u6e96\u5716\u300b\u4fee\u6539 "),e.k0s(),e.j41(3,"nb-card-body",22)(4,"div",23),e.DNE(5,$t,2,0,"button",24),e.k0s(),e.DNE(6,Vt,17,3,"div",25),e.k0s(),e.j41(7,"nb-card-footer",18)(8,"button",26),e.bIt("click",function(){const n=e.eBV(t).dialogRef,i=e.XpG();return e.Njj(i.onClose(n))}),e.EFF(9,"\u8fd4\u56de\u4e0a\u4e00\u9801"),e.k0s(),e.j41(10,"button",26),e.bIt("click",function(){const n=e.eBV(t).dialogRef,i=e.XpG();return e.Njj(i.onClose(n))}),e.EFF(11,"\u5132\u5b58"),e.k0s()()()}if(2&s){const t=e.XpG();e.R7$(5),e.Y8G("ngIf",t.listHouseRegularPic.length),e.R7$(),e.Y8G("ngForOf",t.listHouseRegularPic)}}let Ut=(()=>{class s extends E.${constructor(t,o,n,i,a){super(t),this._allow=t,this.dialogService=o,this._houseService=n,this.route=i,this.message=a,this.buildingSelectedOptions=[{value:"",label:"\u5168\u90e8"}]}getListBuilding(){this._houseService.apiHouseGetListBuildingPost$Json({body:{CBuildCaseID:this.buildCaseId}}).subscribe(t=>{t.Entries&&0==t.StatusCode&&(this.buildingSelectedOptions=[{value:"",label:"\u5168\u90e8"},...t.Entries.map(o=>({value:o,label:o}))],this.selectedBuilding=this.buildingSelectedOptions[0],this.getListHouseRegularPic())})}getListHouseRegularPic(){let t={CBuildCaseID:this.buildCaseId,CBuildingName:this.selectedBuilding.value,PageIndex:this.pageIndex,PageSize:this.pageSize};this.selectedBuilding.value||delete t.CBuildingName,this._houseService.apiHouseGetListHouseRegularPicPost$Json({body:t}).subscribe(o=>{o.Entries&&0==o.StatusCode&&(this.listHouseRegularPic=o.Entries??[],this.totalRecords=o.TotalItems)})}checkAll(t,o){o.CHouse&&o.CHouse.length>0&&o.CHouse.forEach(n=>n.CIsSelect=t),t&&this.listHouseRegularPic.forEach((n,i)=>{n.CRegularPictureID!==o.CRegularPictureID&&n.CHouse&&Array.isArray(n.CHouse)&&n.CHouse.forEach((a,u)=>{a.CIsSelect=!1})})}checkItem(t,o,n){t&&this.listHouseRegularPic.forEach((i,a)=>{a!==o&&i.CHouse&&Array.isArray(i.CHouse)&&i.CHouse.forEach((u,m)=>{u.CHouseID===n.CHouseID&&(u.CIsSelect=!1)})})}isAllChecked(t){return t.CHouse.every(o=>o.CIsSelect)}extractSelectedHouses(t){const o=[];for(const n of t)for(const i of n.CHouse)i.CIsSelect&&o.push({CHouseID:i.CHouseID,CRegularPictureID:n.CRegularPictureID});return o}submitEditHouseRegularPic(t){let o=this.extractSelectedHouses(this.listHouseRegularPic);this._houseService.apiHouseEditHouseRegularPicPost$Json({body:{CHousePic:o}}).subscribe(n=>{0===n.StatusCode&&(this.message.showSucessMSG("\u57f7\u884c\u6210\u529f"),t.close())})}onDeleteHouseRegularPic(t){window.confirm(`\u78ba\u5b9a\u8981\u522a\u9664\u3010\u9805\u76ee${t.CFileName}\u3011?`)&&this._houseService.apiHouseDeleteRegularPicturePost$Json({body:{CRegularPictureID:t.CRegularPictureID}}).subscribe(o=>{0===o.StatusCode&&(this.message.showSucessMSG("\u57f7\u884c\u6210\u529f"),this.getListHouseRegularPic())})}clear(){this.selectedBuilding=this.buildingSelectedOptions[0]}ngOnInit(){this.route.paramMap.subscribe(t=>{if(t){const o=t.get("id");this.buildCaseId=o?+o:0,this.getListBuilding()}})}pageChanged(t){this.pageIndex=t,this.getListHouseRegularPic()}onOpen(t){this.dialogService.open(t)}onClose(t){t.close()}static{this.\u0275fac=function(o){return new(o||s)(e.rXU(I.O),e.rXU(r.S0W),e.rXU(h.SD),e.rXU(C.nX),e.rXU(M.b))}}static{this.\u0275cmp=e.VBU({type:s,selectors:[["ngx-standard-house-plan"]],features:[e.Vt3],decls:34,vars:6,consts:[["dialog",""],["accent","success"],[1,"font-bold","text-[#818181]"],[1,"d-flex","flex-wrap"],[1,"col-md-6"],[1,"form-group","d-flex","align-items-center","w-full"],["for","buildingName",1,"label","col-3"],["placeholder","\u72c0\u614b",1,"w-full",3,"ngModelChange","ngModel"],[3,"value",4,"ngFor","ngForOf"],[1,"d-flex","justify-content-end","w-full"],[1,"btn","btn-secondary",3,"click"],[1,"btn","btn-secondary","mx-2",3,"click"],[1,"btn","btn-info",3,"click"],[1,"table-responsive","mt-4"],[1,"table",2,"min-width","1000px"],[1,"table-header"],["scope","col",1,"col-1"],[4,"ngFor","ngForOf"],[1,"d-flex","justify-content-center"],["aria-label","Pagination",3,"pageChange","page","pageSize","collectionSize"],[3,"value"],[2,"width","700px","max-height","95vh"],[1,"px-4"],[1,"d-flex","justify-end"],["class","btn btn-primary mx-2",3,"click",4,"ngIf"],["class","bg-white p-4 rounded shadow m-2",4,"ngFor","ngForOf"],[1,"btn","btn-primary","btn-sm","mx-2",3,"click"],[1,"btn","btn-primary","mx-2",3,"click"],[1,"bg-white","p-4","rounded","shadow","m-2"],[1,"mb-2"],["for","standard-drawing",1,"block","text-gray-700","font-bold","mb-2"],["for","applicable-models",1,"block","text-gray-700","font-bold","mb-2"],[1,"inline-flex","items-center","mr-4"],[1,"mr-2"],["status","basic",3,"checkedChange","checked"],[1,"flex","flex-wrap"],["class","inline-flex items-center mr-4",4,"ngFor","ngForOf"],[1,"w-full","text-right"],["type","button",1,"btn","btn-outline-dark","py-2","px-4","btn-sm","ml-6",3,"click"],[1,"ml-2"]],template:function(o,n){if(1&o){const i=e.RV6();e.j41(0,"nb-card",1)(1,"nb-card-header"),e.nrm(2,"ngx-breadcrumb"),e.k0s(),e.j41(3,"nb-card-body"),e.nrm(4,"h1",2),e.j41(5,"div",3)(6,"div",4)(7,"div",5)(8,"label",6),e.EFF(9,"\u68df\u5225"),e.k0s(),e.j41(10,"nb-select",7),e.mxI("ngModelChange",function(u){return e.eBV(i),e.DH7(n.selectedBuilding,u)||(n.selectedBuilding=u),e.Njj(u)}),e.DNE(11,wt,2,2,"nb-option",8),e.k0s()()(),e.j41(12,"div",4)(13,"div",9)(14,"button",10),e.bIt("click",function(){return e.eBV(i),e.Njj(n.clear())}),e.EFF(15," \u6e05\u9664 "),e.k0s(),e.j41(16,"button",11),e.bIt("click",function(){return e.eBV(i),e.Njj(n.getListHouseRegularPic())}),e.EFF(17," \u67e5\u8a62 "),e.k0s(),e.j41(18,"button",12),e.bIt("click",function(){e.eBV(i);const u=e.sdS(33);return e.Njj(n.onOpen(u))}),e.EFF(19," \u68df\u5225 "),e.k0s()()()(),e.j41(20,"div",13)(21,"table",14)(22,"thead",15)(23,"tr")(24,"th",16),e.EFF(25,"\u6a94\u6848"),e.k0s(),e.j41(26,"th",16),e.EFF(27,"\u9069\u7528\u6236\u578b "),e.k0s()()(),e.j41(28,"tbody"),e.DNE(29,Qt,5,2,"tr",17),e.k0s()()()(),e.j41(30,"nb-card-footer",18)(31,"ngb-pagination",19),e.mxI("pageChange",function(u){return e.eBV(i),e.DH7(n.pageIndex,u)||(n.pageIndex=u),e.Njj(u)}),e.bIt("pageChange",function(u){return e.eBV(i),e.Njj(n.pageChanged(u))}),e.k0s()()(),e.DNE(32,Xt,12,2,"ng-template",null,0,e.C5r)}2&o&&(e.R7$(10),e.R50("ngModel",n.selectedBuilding),e.R7$(),e.Y8G("ngForOf",n.buildingSelectedOptions),e.R7$(18),e.Y8G("ngForOf",n.listHouseRegularPic),e.R7$(2),e.R50("page",n.pageIndex),e.Y8G("pageSize",n.pageSize)("collectionSize",n.totalRecords))},dependencies:[p.Sq,p.bT,d.BC,d.vS,r.SrT,r.KH5,r.VDm,r.zJv,r.DMy,r.S81,r.ZJ2,R.s5,j.D]})}}return s})();var Yt=c(534),Jt=c(6064),zt=c(5833),qt=c(9423),z=c(1710),Wt=c(5197),Kt=c(2561);const Zt=["calendar"];function en(s,l){if(1&s){const t=e.RV6();e.j41(0,"tr",19)(1,"td",27),e.EFF(2),e.k0s(),e.j41(3,"td",28),e.EFF(4),e.k0s(),e.j41(5,"td",29)(6,"button",30),e.bIt("click",function(){const n=e.eBV(t).$implicit,i=e.XpG();return e.Njj(i.openPdfInNewTab(n))}),e.EFF(7,"\u9023\u7d50"),e.k0s()()()}if(2&s){const t=l.$implicit;e.R7$(2),e.JRh(t.CDocumentName),e.R7$(2),e.JRh(t.CSignDate)}}function tn(s,l){if(1&s){const t=e.RV6();e.j41(0,"div",52)(1,"span",53),e.EFF(2),e.k0s(),e.j41(3,"button",54),e.bIt("click",function(){e.eBV(t);const n=e.XpG(2);return e.Njj(n.clearFile())}),e.nrm(4,"i",55),e.k0s()()}if(2&s){const t=e.XpG(2);e.R7$(2),e.JRh(t.fileName)}}function nn(s,l){if(1&s){const t=e.RV6();e.j41(0,"nb-card",31)(1,"nb-card-header"),e.EFF(2),e.k0s(),e.j41(3,"nb-card-body",32)(4,"div",33)(5,"div",34)(6,"label",35),e.EFF(7,"\u6587\u4ef6 "),e.k0s()(),e.j41(8,"div",36)(9,"input",37),e.bIt("change",function(n){e.eBV(t);const i=e.XpG();return e.Njj(i.onFileSelected(n))}),e.k0s(),e.j41(10,"label",38),e.nrm(11,"i",39),e.EFF(12," \u4e0a\u50b3 "),e.k0s(),e.DNE(13,tn,5,1,"div",40),e.k0s()(),e.j41(14,"div",41)(15,"label",42),e.EFF(16," \u6587\u4ef6\u540d\u7a31 "),e.k0s(),e.j41(17,"input",43),e.mxI("ngModelChange",function(n){e.eBV(t);const i=e.XpG();return e.DH7(i.CDocumentName,n)||(i.CDocumentName=n),e.Njj(n)}),e.k0s()(),e.j41(18,"div",44)(19,"label",45),e.EFF(20,"\u9001\u5be9\u8cc7\u8a0a "),e.j41(21,"p",46),e.EFF(22,"\u5167\u90e8\u5be9\u6838\u4eba\u54e1\u67e5\u770b"),e.k0s()(),e.j41(23,"textarea",47),e.mxI("ngModelChange",function(n){e.eBV(t);const i=e.XpG();return e.DH7(i.CApproveRemark,n)||(i.CApproveRemark=n),e.Njj(n)}),e.EFF(24,"        "),e.k0s()(),e.j41(25,"div",44)(26,"label",48),e.EFF(27,"\u6458\u8981\u8a3b\u8a18 "),e.j41(28,"p",46),e.EFF(29,"\u5ba2\u6236\u65bc\u6587\u4ef6\u4e2d\u67e5\u770b"),e.k0s()(),e.j41(30,"textarea",49),e.mxI("ngModelChange",function(n){e.eBV(t);const i=e.XpG();return e.DH7(i.CNote,n)||(i.CNote=n),e.Njj(n)}),e.EFF(31,"        "),e.k0s()()(),e.j41(32,"nb-card-footer",25)(33,"button",50),e.bIt("click",function(){const n=e.eBV(t).dialogRef;return e.Njj(n.close())}),e.EFF(34,"\u53d6\u6d88"),e.k0s(),e.j41(35,"button",51),e.bIt("click",function(){const n=e.eBV(t).dialogRef,i=e.XpG();return e.Njj(i.onCreateFinalDoc(n))}),e.EFF(36,"\u78ba\u8a8d\u9001\u51fa\u5be9\u6838"),e.k0s()()()}if(2&s){const t=e.XpG();e.R7$(2),e.Lme(" \u6236\u5225\u7ba1\u7406 > \u7c3d\u7f72\u6587\u4ef6\u6b77\u7a0b > ",t.houseByID.CHousehold," ",t.houseByID.CFloor,"F "),e.R7$(11),e.Y8G("ngIf",t.fileName),e.R7$(4),e.R50("ngModel",t.CDocumentName),e.R7$(6),e.R50("ngModel",t.CApproveRemark),e.R7$(7),e.R50("ngModel",t.CNote)}}const on=[{path:"",component:ze},{path:"customer-change-picture/:id1/:id2",component:it},{path:"sample-selection-result/:id1/:id2",component:ct},{path:"modify-floor-plan/:id",component:Ct},{path:"modify-household/:id",component:jt},{path:"modify-house-type/:id",component:Nt},{path:"standard-house-plan/:id",component:Ut},{path:"finaldochouse_management/:id1/:id2",component:(()=>{class s extends E.${constructor(t,o,n,i,a,u,m,F,S,D,x){super(t),this._allow=t,this.enumHelper=o,this.dialogService=n,this.message=i,this.valid=a,this.finalDocumentService=u,this.route=m,this._eventService=F,this.location=S,this._houseService=D,this.fileService=x,this.calendarOptions={plugins:[zt.Ay,qt.A,z.A,Wt.A,z.A,Kt.A],locale:"zh-tw",headerToolbar:{left:"prev",center:"title",right:"next"}},this.file=null,this.getListFinalDocRequest={},this.uploadFinaldocRequest={},this.listFinalDoc=[],this.maxDate=new Date}ngOnInit(){this.route.paramMap.subscribe(t=>{if(t){const o=t.get("id1");this.buildCaseId=o?+o:0;const i=t.get("id2");this.currentHouseID=i?+i:0,this.getList(),this.getHouseById()}})}addNew(t){this.CApproveRemark=null,this.CDocumentName=null,this.CNote=null,this.file=null,this.dialogService.open(t)}openPdfInNewTab(t){if(t){let o;if(o=t.CSignDate&&t.CSign?t.CFileAfter:t.CFileBefore,!t.CDocumentName)return void console.error("\u6a94\u6848\u540d\u7a31\u4e0d\u5b58\u5728");this.fileService.getFile(o,t.CDocumentName).subscribe({next:n=>{const i=URL.createObjectURL(n);window.open(i,"_blank"),setTimeout(()=>URL.revokeObjectURL(i),1e4)},error:n=>{console.error("\u53d6\u5f97\u6a94\u6848\u5931\u6557:",n),this.message.showErrorMSG("\u7121\u6cd5\u958b\u555f\u6a94\u6848\uff0c\u8acb\u7a0d\u5f8c\u518d\u8a66")}})}}goBack(){this._eventService.push({action:"GET_BUILDCASE",payload:this.buildCaseId}),this.location.back()}onFileSelected(t){const o=t.target.files[0];/pdf/i.test(o.type)?o&&["application/pdf"].includes(o.type)&&(this.fileName=o.name,this.file=o):this.message.showErrorMSG("\u6587\u4ef6\u683c\u5f0f\u4e0d\u6b63\u78ba\uff0c\u50c5\u5141\u8a31 pdf \u6587\u4ef6")}clearFile(){this.file&&(this.file=null,this.fileName=null)}getHouseById(){this._houseService.apiHouseGetHouseByIdPost$Json({body:{CHouseID:this.currentHouseID}}).subscribe(t=>{t.Entries&&0==t.StatusCode&&(this.houseByID=t.Entries)})}getList(){this.getListFinalDocRequest.PageSize=this.pageSize,this.getListFinalDocRequest.PageIndex=this.pageIndex,0!=this.currentHouseID&&(this.getListFinalDocRequest.CHouseID=this.currentHouseID,this.finalDocumentService.apiFinalDocumentGetListFinalDocByHousePost$Json({body:this.getListFinalDocRequest}).pipe().subscribe(t=>{if(0==t.StatusCode&&t.Entries&&(this.listFinalDoc=t.Entries,this.totalRecords=t.TotalItems,this.listFinalDoc))for(let o=0;o<this.listFinalDoc.length;o++)this.listFinalDoc[o].CSignDate&&(this.listFinalDoc[o].CSignDate=T(this.listFinalDoc[o].CSignDate).format("yyyy/MM/DD H:mm:ss"))}))}onCreateFinalDoc(t){this.validation(),this.valid.errorMessages.length>0?this.message.showErrorMSGs(this.valid.errorMessages):this.finalDocumentService.apiFinalDocumentUploadFinalDocPost$Json({body:{CHouseID:this.currentHouseID,CBuildCaseID:this.buildCaseId,CDocumentName:this.CDocumentName,CApproveRemark:this.CApproveRemark,CNote:this.CNote,CFile:this.file}}).subscribe(o=>{o.Entries&&0==o.StatusCode?(this.getList(),this.message.showSucessMSG("\u57f7\u884c\u6210\u529f"),t.close()):this.message.showErrorMSG(o.Message)})}convertToBlob(t,o="application/octet-stream"){return t instanceof ArrayBuffer||"string"==typeof t?new Blob([t],{type:o}):void 0}validation(){this.valid.clear(),this.valid.required("[\u6587\u4ef6\u683c\u5f0f\u4e0d\u6b63\u78ba]",this.file),this.valid.required("[\u6587\u4ef6\u540d\u7a31]",this.CDocumentName),this.valid.required("[\u9001\u5be9\u8cc7\u8a0a]",this.CApproveRemark),this.valid.required("[\u7cfb\u7d71\u64cd\u4f5c\u8aaa\u660e]",this.CNote)}static{this.\u0275fac=function(o){return new(o||s)(e.rXU(I.O),e.rXU(U.e),e.rXU(r.S0W),e.rXU(M.b),e.rXU(P.E),e.rXU(h.RG),e.rXU(C.nX),e.rXU(v.U),e.rXU(p.aZ),e.rXU(h.SD),e.rXU(h.E2))}}static{this.\u0275cmp=e.VBU({type:s,selectors:[["app-finaldochouse-management"]],viewQuery:function(o,n){if(1&o&&e.GBs(Zt,5),2&o){let i;e.mGM(i=e.lsd())&&(n.calendarComponent=i.first)}},standalone:!0,features:[e.Vt3,e.aNF],decls:44,vars:10,consts:[["dialogUploadFinaldoc",""],["accent","success"],[2,"font-size","32px"],[1,"bg-white"],[1,"col-12"],[1,"row"],[1,"flex","form-group","col-12","col-md-9","text-right"],["for","date-select1",1,"mr-3","mt-2"],["placeholder","\u5e74/\u6708/\u65e5","dateFormat","yy/mm/dd",3,"ngModelChange","appendTo","ngModel","maxDate"],["for","date-select1",1,"mr-1","ml-1","mt-2"],[1,"form-group","col-12","col-md-3","text-right"],[1,"btn","btn-info","mr-2",3,"click"],[1,"fas","fa-search","mr-1"],[1,"col-md-12"],[1,"d-flex","justify-content-end","w-full"],[1,"btn","btn-info",3,"click"],[1,"table-responsive"],[1,"table",2,"min-width","800px"],[1,"table-header"],[1,"d-flex"],["scope","col",1,"col-5"],["scope","col",1,"col-4"],["scope","col",1,"col-3"],["class","d-flex",4,"ngFor","ngForOf"],[3,"PageChange","CollectionSize","Page","PageSize"],[1,"d-flex","justify-content-center"],[1,"btn","btn-secondary","btn-sm",3,"click"],[1,"col-5"],[1,"col-4"],[1,"col-3"],[1,"btn","btn-outline-primary","btn-sm","m-1",3,"click"],[2,"width","1000px","max-height","95vh"],[1,"px-4"],[1,"form-group","d-flex","align-items-baseline"],[1,"d-flex","flex-col","col-3"],["for","file","baseLabel","",1,"required-field","align-self-start",2,"min-width","100px","position","static"],[1,"flex","flex-col","items-start","space-y-4"],["type","file","id","fileInput","accept","image/jpeg, image/jpg, application/pdf",1,"hidden",2,"display","none",3,"change"],["for","fileInput",1,"cursor-pointer","bg-blue-500","hover:bg-blue-700","text-white","font-bold","py-2","px-4","rounded"],[1,"fa-solid","fa-cloud-arrow-up","mr-2"],["class","flex items-center space-x-2",4,"ngIf"],[1,"form-group"],["for","CDocumentName","baseLabel","",1,"required-field","align-self-start","col-3",2,"min-width","75px"],["type","text","nbInput","","placeholder","\u6587\u4ef6\u540d\u7a31",1,"w-full",3,"ngModelChange","ngModel"],[1,"form-group","d-flex","align-items-center"],["for","remark","baseLabel","",1,"required-field","align-self-start","col-3"],[2,"color","red"],["name","remark","id","remark","rows","5","nbInput","",1,"w-full",2,"resize","none","max-width","none",3,"ngModelChange","ngModel"],["for","CNote","baseLabel","",1,"required-field","align-self-start","col-3"],["name","CNote","id","CNote","rows","5","nbInput","",1,"w-full",2,"resize","none","max-width","none",3,"ngModelChange","ngModel"],[1,"btn","btn-outline-secondary","m-2",3,"click"],[1,"btn","btn-success","m-2",3,"click"],[1,"flex","items-center","space-x-2"],[1,"text-gray-600"],["type","button",1,"text-red-500","hover:text-red-700",3,"click"],[1,"fa-solid","fa-trash"]],template:function(o,n){if(1&o){const i=e.RV6();e.j41(0,"nb-card",1)(1,"nb-card-header")(2,"div",2),e.EFF(3,"\u6236\u5225\u7ba1\u7406 / \u7c3d\u7f72\u6587\u4ef6\u6b77\u7a0b"),e.k0s()(),e.j41(4,"nb-card-body",3)(5,"div",4)(6,"div",5)(7,"div",6)(8,"span",7),e.EFF(9," \u5efa\u7acb\u6642\u9593 "),e.k0s(),e.j41(10,"p-calendar",8),e.mxI("ngModelChange",function(u){return e.eBV(i),e.DH7(n.getListFinalDocRequest.CDateStart,u)||(n.getListFinalDocRequest.CDateStart=u),e.Njj(u)}),e.k0s(),e.j41(11,"span",9),e.EFF(12,"~"),e.k0s(),e.j41(13,"p-calendar",8),e.mxI("ngModelChange",function(u){return e.eBV(i),e.DH7(n.getListFinalDocRequest.CDateEnd,u)||(n.getListFinalDocRequest.CDateEnd=u),e.Njj(u)}),e.k0s()(),e.j41(14,"div",10)(15,"button",11),e.bIt("click",function(){return e.eBV(i),e.Njj(n.getList())}),e.nrm(16,"i",12),e.EFF(17,"\u67e5\u8a62"),e.k0s()()(),e.j41(18,"div",5)(19,"div",13)(20,"div",14)(21,"button",15),e.bIt("click",function(){e.eBV(i);const u=e.sdS(43);return e.Njj(n.addNew(u))}),e.EFF(22," \u65b0\u589e\u6587\u6a94 "),e.k0s()()()()()(),e.j41(23,"nb-card-body",3)(24,"div",4)(25,"div",16)(26,"table",17)(27,"thead",18)(28,"tr",19)(29,"th",20),e.EFF(30,"\u6587\u4ef6\u540d\u7a31"),e.k0s(),e.j41(31,"th",21),e.EFF(32,"\u5ba2\u6236\u7c3d\u540d\u6642\u9593"),e.k0s(),e.j41(33,"th",22),e.EFF(34,"\u9023\u7d50"),e.k0s()()(),e.j41(35,"tbody"),e.DNE(36,en,8,2,"tr",23),e.k0s()()(),e.j41(37,"ngx-pagination",24),e.mxI("PageChange",function(u){return e.eBV(i),e.DH7(n.pageIndex,u)||(n.pageIndex=u),e.Njj(u)}),e.bIt("PageChange",function(){return e.eBV(i),e.Njj(n.getList())}),e.k0s()()(),e.j41(38,"nb-card-footer")(39,"div",25)(40,"button",26),e.bIt("click",function(){return e.eBV(i),e.Njj(n.goBack())}),e.EFF(41," \u8fd4\u56de\u4e0a\u4e00\u9801 "),e.k0s()()()(),e.DNE(42,nn,37,6,"ng-template",null,0,e.C5r)}2&o&&(e.R7$(10),e.Y8G("appendTo","body"),e.R50("ngModel",n.getListFinalDocRequest.CDateStart),e.Y8G("maxDate",n.maxDate),e.R7$(3),e.Y8G("appendTo","body"),e.R50("ngModel",n.getListFinalDocRequest.CDateEnd),e.Y8G("maxDate",n.maxDate),e.R7$(23),e.Y8G("ngForOf",n.listFinalDoc),e.R7$(),e.Y8G("CollectionSize",n.totalRecords),e.R50("Page",n.pageIndex),e.Y8G("PageSize",n.pageSize))},dependencies:[r.lVF,r.SrT,r.KH5,r.VDm,r.zJv,r.pty,r.i6h,d.YN,d.me,d.BC,d.vS,r.VcP,r.OKj,p.bT,p.pM,Yt.e,r.Ad8,Jt.E,B.rO,B.Vv]})}}return s})()}];let sn=(()=>{class s{static{this.\u0275fac=function(o){return new(o||s)}}static{this.\u0275mod=e.$C({type:s})}static{this.\u0275inj=e.G2t({imports:[C.iI.forChild(on),C.iI]})}}return s})(),an=(()=>{class s{static{this.\u0275fac=function(o){return new(o||s)}}static{this.\u0275mod=e.$C({type:s})}static{this.\u0275inj=e.G2t({imports:[p.MD,sn,$.G,q.O,B.rO,r.anj.forRoot()]})}}return s})()}}]);