# Template Viewer 組件使用說明

## 更新內容

已將 `ModuleType` 改為 `CTemplateType`，其中：
- `1` = 客變需求
- 此值從父元件傳入且不可異動

## 使用方式

### 在父元件中使用

```typescript
// 父元件 TypeScript
export class ParentComponent {
  templateData = [
    {
      CReleateId: 1,
      CReleateName: '廚房改裝',
      CLocation: '室內裝修',
      CRequirement: '廚房改裝',  // 用於顯示
      selected: false
    },
    {
      CReleateId: 2,
      CReleateName: '浴室翻新',
      CLocation: '室內裝修',
      CRequirement: '浴室翻新',  // 用於顯示
      selected: false
    },
    // ... 更多資料
  ];

  onTemplateSelected(template: any) {
    console.log('選中的模板:', template);
  }

  onTemplateViewerClose() {
    // 關閉模板查看器的邏輯
  }
}
```

```html
<!-- 父元件 HTML - 基本使用 -->
<app-template-viewer
  [availableData]="templateData"
  [templateType]="1"
  [showOnlyAddForm]="false"
  (selectTemplate)="onTemplateSelected($event)"
  (close)="onTemplateViewerClose()">
</app-template-viewer>

<!-- 父元件 HTML - 只顯示新增表單 -->
<app-template-viewer
  [availableData]="templateData"
  [templateType]="1"
  [showOnlyAddForm]="true"
  (selectTemplate)="onTemplateSelected($event)"
  (close)="onTemplateViewerClose()">
</app-template-viewer>
```

## 參數說明

| 參數 | 類型 | 說明 | 預設值 |
|------|------|------|--------|
| `availableData` | `any[]` | 父元件傳入的可選資料，必須包含 CLocation, CReleateName, CReleateId | `[]` |
| `templateType` | `number` | 模板類型 (1=客變需求) | `1` |
| `showOnlyAddForm` | `boolean` | 是否只顯示新增模板表單 | `false` |

### availableData 資料結構要求

父元件傳入的 `availableData` 必須包含以下欄位：

```typescript
interface AvailableDataItem {
  // 必要欄位 - SaveTemplate API 需要
  CLocation: string | null;     // 群組名稱
  CReleateName: string | null;   // 關聯名稱
  CReleateId: number;            // 關聯ID

  // 可選欄位 - 用於顯示和選擇
  selected?: boolean;            // 是否被選中
  CRequirement?: string;         // 需求描述 (用於顯示)
  // ... 其他顯示用欄位
}
```

這三個欄位 (CLocation, CReleateName, CReleateId) 是 SaveTemplate API 的必要資訊，父元件必須確保每個項目都包含這些欄位。

## 事件說明

| 事件 | 參數 | 說明 |
|------|------|------|
| `selectTemplate` | `Template` | 當選擇模板時觸發 |
| `close` | `void` | 當關閉組件時觸發 |

## 資料結構

### Template
```typescript
interface Template {
  TemplateID?: number;
  TemplateName: string;
  Description?: string;
}
```

### TemplateDetail
```typescript
interface TemplateDetail {
  TemplateDetailID?: number;
  TemplateID: number;
  RefID: number; // 關聯主檔ID
  CTemplateType: number; // 模板類型，1=客變需求
  FieldName: string;
  FieldValue: string;
}
```

## API 對應

組件已完成以下 API 串接：

1. **POST /api/Template/GetTemplateList** - 獲取模板列表 ✅ 已串接
2. **POST /api/Template/SaveTemplate** - 保存模板 ✅ 已串接
3. **POST /api/Template/DeleteTemplate** - 刪除模板 ✅ 已串接
4. **POST /api/Template/GetTemplateDetailById** - 獲取模板詳情 ✅ 已串接
5. **POST /api/Template/GetTemplateById** - 根據ID獲取模板 (待實作)

## API 使用說明

### SaveTemplate API 請求格式
```typescript
{
  CTemplateId: null,           // 新增時為 null
  CTemplateName: "模板名稱",    // 模板名稱
  CTemplateType: 1,            // 1=客變需求
  CStatus: 1,                  // 啟用狀態
  Details: [                   // 模板詳情陣列
    {
      CTemplateDetailId: null, // 新增時為 null
      CReleateId: 123,         // 關聯主檔ID
      CReleateName: "項目名稱", // 關聯名稱
      CLocation: "結構工程"    // 群組名稱 (新增欄位)
    }
  ]
}
```

### GetTemplateList API 請求格式
```typescript
{
  CTemplateType: 1,            // 1=客變需求
  PageIndex: 1,                // 頁碼
  PageSize: 100,               // 每頁筆數
  CTemplateName: null          // 模板名稱篩選 (可選)
}
```

### DeleteTemplate API 請求格式
```typescript
{
  CTemplateId: 123             // 要刪除的模板ID
}
```

### GetTemplateDetailById API 請求格式
```typescript
{
  templateId: 123              // 模板ID (必填)
}
```

### GetTemplateDetailById API 回應格式
```typescript
{
  StatusCode: 0,               // 狀態碼 (0=成功)
  Message: "成功",             // 回應訊息
  TotalItems: 50,              // 總項目數
  Entries: [                   // 詳情項目陣列
    {
      CTemplateDetailId: 456,
      CTemplateId: 123,
      CReleateId: 789,         // 關聯主檔ID
      CReleateName: "項目名稱", // 關聯名稱
      CLocation: "結構工程"    // 群組名稱 (新增欄位)
    }
  ]
}
```

**注意事項：**
- GetTemplateDetailById API 不支援分頁和搜尋參數，組件會在前端處理這些功能
- API 回應的 TemplateDetailItem 現在包含 CLocation 欄位，組件會擴展為完整的顯示格式
- 搜尋功能在前端實現，基於 CReleateName 和 CLocation 欄位進行篩選
- 分頁功能在前端實現，支援每頁顯示指定數量的項目
```

## 新功能特色

### 🔍 詳情搜尋功能
- 支援在模板詳情中搜尋項目名稱、群組名稱、備註、分類
- 即時搜尋結果顯示
- 清除搜尋功能

### 📊 增強的詳情顯示
- 顯示更豐富的詳情資訊：群組名稱、分類、單價、數量、單位
- 美化的 UI 設計，包含彩色標籤和圖示
- 支援備註顯示
- 顯示建立時間

### ⚡ 效能優化
- 按需載入詳情，不再一次載入所有模板的詳情
- 伺服器端分頁支援
- 優化的資料載入機制

### 🔄 向後相容
- 保留舊的詳情顯示邏輯，確保現有功能正常運作
- 自動偵測使用新或舊的資料結構

## 注意事項

1. `templateType` 參數固定為 `1` (客變需求)，從父元件傳入且不可異動
2. 組件內部已完成所有 API 串接，包含錯誤處理和成功回饋
3. 模板詳情結構已調整為使用 `CTemplateType` 而非 `ModuleType`
4. 所有 API 調用都包含完整的錯誤處理和用戶提示
5. 當 `showOnlyAddForm=true` 時，組件不會載入模板列表，避免不必要的 API 調用
6. 新增模板成功後，只有在非純新增模式下才會重新載入模板列表
7. 在純新增模式下，新增模板成功後會自動發出 `close` 事件關閉 dialog
8. **GetTemplateDetail API 已完全整合**，使用真實的 API 調用
9. 詳情搜尋和分頁功能已完全實作，支援伺服器端處理
