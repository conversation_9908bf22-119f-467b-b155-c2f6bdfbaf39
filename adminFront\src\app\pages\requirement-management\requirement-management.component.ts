import { Component, DestroyRef, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { NbCardModule, NbCheckboxModule, NbDialogService, NbInputModule, NbOptionModule, NbSelectModule } from '@nebular/theme';
import { MessageService } from 'src/app/shared/services/message.service';
import { AllowHelper } from 'src/app/shared/helper/allowHelper';
import { EnumHelper } from 'src/app/shared/helper/enumHelper';
import { PetternHelper } from 'src/app/shared/helper/petternHelper';
import { ValidationHelper } from 'src/app/shared/helper/validationHelper';
import { BuildCaseService, RequirementService } from 'src/services/api/services';
import { BaseComponent } from '../components/base/baseComponent';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { BuildCaseGetListReponse, GetListRequirementRequest, GetRequirement, GetRequirementByIdRequest, SaveDataRequirement, SaveTemplayeDataReq } from 'src/services/api/models';
import { BreadcrumbComponent } from '../components/breadcrumb/breadcrumb.component';
import { FormsModule } from '@angular/forms';
import { NgFor, NgIf } from '@angular/common';
import { PaginationComponent } from '../components/pagination/pagination.component';
import { StatusPipe } from 'src/app/@theme/pipes/mapping.pipe';
import { FormGroupComponent } from 'src/app/@theme/components/form-group/form-group.component';
import { NumberWithCommasPipe } from 'src/app/@theme/pipes/number-with-commas.pipe';
import { EnumHouseType } from 'src/app/shared/enum/enumHouseType';
import { SpaceTemplateSelectorService } from 'src/app/shared/components/space-template-selector/space-template-selector.service';
import { SpaceTemplateConfig } from 'src/app/shared/components/space-template-selector/space-template-selector.component';

@Component({
  selector: 'app-requirement-management',
  standalone: true,
  imports: [
    NbCardModule,
    BreadcrumbComponent,
    NbInputModule,
    FormsModule,
    NbSelectModule,
    NbOptionModule,
    NgIf,
    NgFor,
    PaginationComponent,
    StatusPipe,
    NbCheckboxModule,
    FormGroupComponent,
    NumberWithCommasPipe
  ],
  templateUrl: './requirement-management.component.html',
  styleUrl: './requirement-management.component.scss'
})
export class RequirementManagementComponent extends BaseComponent implements OnInit {
  constructor(
    private _allow: AllowHelper,
    private enumHelper: EnumHelper,
    private dialogService: NbDialogService,
    private message: MessageService,
    private valid: ValidationHelper,
    private buildCaseService: BuildCaseService,
    private requirementService: RequirementService,
    private pettern: PetternHelper,
    private router: Router,
    private destroyref: DestroyRef,
    private spaceTemplateSelectorService: SpaceTemplateSelectorService
  ) {
    super(_allow);
    this.initializeSearchForm();
    this.getBuildCaseList();
  }

  // request
  getListRequirementRequest = {} as GetListRequirementRequest & { CIsShow?: boolean | null; CIsSimple?: boolean | null };
  getRequirementRequest: GetRequirementByIdRequest = {};

  // response
  buildCaseList: BuildCaseGetListReponse[] = [];
  requirementList: GetRequirement[] = [];
  saveRequirement: SaveDataRequirement & { CIsShow?: boolean; CIsSimple?: boolean } = { CHouseType: [] };

  statusOptions = [
    { value: 0, label: '停用' },
    { value: 1, label: '啟用' },
  ];
  houseType = this.enumHelper.getEnumOptions(EnumHouseType);
  isNew = false;
  currentBuildCase = 0;

  // 批次編輯相關屬性
  selectedItems: GetRequirement[] = [];
  isAllSelected = false;
  isBatchEditMode = false;
  // 批次編輯時的項目資料副本
  batchEditItems: (SaveDataRequirement & { CIsShow?: boolean; CIsSimple?: boolean })[] = [];

  override ngOnInit(): void { }

  // 初始化搜尋表單
  initializeSearchForm() {
    this.getListRequirementRequest.CStatus = -1;
    this.getListRequirementRequest.CIsShow = null;
    this.getListRequirementRequest.CIsSimple = null;
    this.getListRequirementRequest.CRequirement = '';
    this.getListRequirementRequest.CLocation = '';
    // 預設全選所有房屋類型
    this.getListRequirementRequest.CHouseType = this.houseType.map(type => type.value);
  }

  // 重置搜尋
  resetSearch() {
    this.initializeSearchForm();
    // 清除選擇狀態
    this.selectedItems = [];
    this.isAllSelected = false;
    // 重置後如果有建案資料，重新設定預設選擇第一個建案
    if (this.buildCaseList && this.buildCaseList.length > 0) {
      setTimeout(() => {
        this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;
        this.getList();
      }, 0);
    } else {
      this.getList();
    }
  }

  getHouseType(hTypes: number[] | number | null | undefined): string {
    if (!hTypes) {
      return '';
    }

    if (!Array.isArray(hTypes)) {
      hTypes = [hTypes];
    }

    let labels: string[] = [];
    hTypes.forEach(htype => {
      let findH = this.houseType.find(x => x.value == htype);
      if (findH) {
        labels.push(findH.label);
      }
    });
    return labels.join(', ');
  }

  validation() {
    this.valid.clear();

    // 建案頁面需要驗證建案名稱
    this.valid.required('[建案名稱]', this.saveRequirement.CBuildCaseID);
    this.valid.required('[需求]', this.saveRequirement.CRequirement);
    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);
    this.valid.required('[排序]', this.saveRequirement.CSort);
    this.valid.required('[狀態]', this.saveRequirement.CStatus);
    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);
    this.valid.required('[單位]', this.saveRequirement.CUnit);

    // 群組名稱長度驗證
    if (this.saveRequirement.CLocation && this.saveRequirement.CLocation.length > 20) {
      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');
    }

    // 備註說明長度驗證
    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {
      this.valid.errorMessages.push('[備註說明] 不能超過100個字');
    }
  }

  add(dialog: TemplateRef<any>) {
    this.isNew = true;
    this.saveRequirement = { CHouseType: [], CIsShow: false, CIsSimple: false };
    this.saveRequirement.CStatus = 1;
    this.saveRequirement.CUnitPrice = 0;

    // 建案頁面 - 使用當前選擇的建案或第一個建案
    if (this.currentBuildCase != 0) {
      this.saveRequirement.CBuildCaseID = this.currentBuildCase;
    } else if (this.buildCaseList && this.buildCaseList.length > 0) {
      this.saveRequirement.CBuildCaseID = this.buildCaseList[0].cID;
    }

    this.dialogService.open(dialog);
  }

  async onEdit(data: GetRequirement, dialog: TemplateRef<any>) {
    this.getRequirementRequest.CRequirementID = data.CRequirementID!;
    this.isNew = false;
    try {
      await this.getData();
      this.dialogService.open(dialog);
    } catch (error) {
      console.log("Failed to get function data", error)
    }
  }

  save(ref: any) {
    this.validation();
    if (this.valid.errorMessages.length > 0) {
      this.message.showErrorMSGs(this.valid.errorMessages);
      return;
    }

    this.requirementService.apiRequirementSaveDataPost$Json({
      body: this.saveRequirement
    }).subscribe(res => {
      if (res.StatusCode === 0) {
        this.message.showSucessMSG('執行成功');
        this.getList();
      } else {
        this.message.showErrorMSG(res.Message!)
      }
    });
    ref.close();
  }

  onDelete(data: GetRequirement) {
    this.saveRequirement.CRequirementID = data.CRequirementID!;
    this.isNew = false;
    if (window.confirm('是否確定刪除?')) {
      this.remove();
    } else {
      return;
    }
  }

  remove() {
    this.requirementService.apiRequirementDeleteDataPost$Json({
      body: {
        CRequirementID: this.saveRequirement.CRequirementID!
      }
    }).subscribe(res => {
      this.message.showSucessMSG('執行成功');
      this.getList();
    });
  }

  getBuildCaseList() {
    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({ body: {} })
      .pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {
        this.buildCaseList = res.Entries!;
        // 如果有建案時才查詢
        if (this.buildCaseList.length > 0) {
          this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;
          this.getList();
        }
      })
  }

  getList() {
    this.getListRequirementRequest.PageSize = this.pageSize;
    this.getListRequirementRequest.PageIndex = this.pageIndex;
    this.requirementList = [] as GetRequirement[];
    this.totalRecords = 0;

    // 建案頁面的邏輯
    if (this.getListRequirementRequest.CBuildCaseID && this.getListRequirementRequest.CBuildCaseID != 0) {
      this.currentBuildCase = this.getListRequirementRequest.CBuildCaseID;
    }

    this.requirementService.apiRequirementGetListPost$Json({ body: this.getListRequirementRequest })
      .pipe()
      .subscribe(res => {
        if (res.StatusCode == 0) {
          if (res.Entries) {
            this.requirementList = res.Entries;
            this.totalRecords = res.TotalItems!;

            // 清理已選擇的項目，移除不存在於新列表中的項目
            this.selectedItems = this.selectedItems.filter(selectedItem =>
              this.requirementList.some(listItem => listItem.CRequirementID === selectedItem.CRequirementID)
            );

            // 更新選擇狀態
            this.updateSelectAllState();
          }
        }
      })
  }

  getData() {
    this.requirementService.apiRequirementGetDataPost$Json({ body: this.getRequirementRequest })
      .pipe()
      .subscribe(res => {
        if (res.StatusCode == 0) {
          if (res.Entries) {
            this.saveRequirement = { CHouseType: [], CIsShow: false, CIsSimple: false };
            this.saveRequirement.CBuildCaseID = res.Entries.CBuildCaseID;
            this.saveRequirement.CLocation = res.Entries.CLocation;
            this.saveRequirement.CHouseType = res.Entries.CHouseType ? (Array.isArray(res.Entries.CHouseType) ? res.Entries.CHouseType : [res.Entries.CHouseType]) : [];
            this.saveRequirement.CRemark = res.Entries.CRemark;
            this.saveRequirement.CRequirement = res.Entries.CRequirement;
            this.saveRequirement.CRequirementID = res.Entries.CRequirementID;
            this.saveRequirement.CSort = res.Entries.CSort;
            this.saveRequirement.CStatus = res.Entries.CStatus;
            this.saveRequirement.CUnitPrice = res.Entries.CUnitPrice ?? 0;
            this.saveRequirement.CUnit = res.Entries.CUnit;
            this.saveRequirement.CIsShow = res.Entries.CIsShow || false;
            this.saveRequirement.CIsSimple = res.Entries.CIsSimple || false;
          }
        }
      })
  }

  onHouseTypeChange(value: number, checked: any) {
    console.log(checked);

    if (checked) {
      if (!this.saveRequirement.CHouseType?.includes(value)) {
        this.saveRequirement.CHouseType?.push(value);
      }
      console.log(this.saveRequirement.CHouseType);
    } else {
      this.saveRequirement.CHouseType = this.saveRequirement.CHouseType?.filter(v => v !== value);
    }
  }

  getCIsShowText(data: any): string {
    return data.CIsShow ? '是' : '否';
  }

  getCIsSimpleText(data: any): string {
    return data.CIsSimple ? '是' : '否';
  }

  // 空間模板相關方法
  openSpaceTemplateSelector() {
    if (!this.getListRequirementRequest.CBuildCaseID) {
      this.message.showErrorMSG('請先選擇建案');
      return;
    }

    // 使用新的服務開啟對話框
    this.spaceTemplateSelectorService.openSelector(this.getListRequirementRequest.CBuildCaseID?.toString() || '')
      .subscribe(result => {
        if (result) {
          this.onSpaceTemplateApplied(result);
        }
      });
  }

  onSpaceTemplateApplied(config: SpaceTemplateConfig) {
    console.log('套用空間模板配置:', config);

    // 使用 SaveTemplayeData API 批次套用模板
    this.batchApplyTemplates(config.selectedItems);
  }

  // 批次套用模板方法
  private batchApplyTemplates(selectedTemplates: any[]) {
    if (!this.getListRequirementRequest.CBuildCaseID) {
      this.message.showErrorMSG('建案 ID 不存在');
      return;
    }

    // 準備批次請求
    const batchRequests = selectedTemplates.map(template => {
      const saveTemplateDataReq = {
        CBuildCaseID: this.getListRequirementRequest.CBuildCaseID,
        CTemplateID: template.CTemplateId
      };

      return this.requirementService.apiRequirementSaveTemplayeDataPost$Json({
        body: saveTemplateDataReq
      });
    });

    // 同時執行所有請求
    Promise.all(batchRequests.map(req => req.toPromise()))
      .then(responses => {
        const successCount = responses.filter(res => res?.StatusCode === 0).length;
        const failCount = responses.length - successCount;

        if (failCount === 0) {
          this.message.showSucessMSG(`成功套用 ${successCount} 個模板項目`);
        } else if (successCount > 0) {
          this.message.showSucessMSG(`成功套用 ${successCount} 個模板項目，${failCount} 個失敗`);
        } else {
          this.message.showErrorMSG('模板套用失敗');
        }

        // 重新載入資料
        this.getList();
      })
      .catch(error => {
        console.error('批次套用模板失敗:', error);
        this.message.showErrorMSG('批次套用模板時發生錯誤');
      });
  }

  // 批次編輯相關方法

  // 切換單一項目選擇狀態
  toggleItemSelection(item: GetRequirement) {
    const index = this.selectedItems.findIndex(selected => selected.CRequirementID === item.CRequirementID);
    if (index > -1) {
      this.selectedItems.splice(index, 1);
    } else {
      this.selectedItems.push(item);
    }
    this.updateSelectAllState();
  }

  // 切換全選狀態
  toggleSelectAll(newValue: boolean) {
    console.log('toggleSelectAll called with:', newValue);
    console.log('requirementList.length:', this.requirementList.length);

    if (this.requirementList.length === 0) {
      this.selectedItems = [];
      this.isAllSelected = false;
      console.log('No items in list, clearing selection');
      return;
    }

    // 更新 isAllSelected 狀態
    this.isAllSelected = newValue;

    // 根據新值更新 selectedItems
    if (this.isAllSelected) {
      this.selectedItems = [...this.requirementList];
      console.log('Selected all items:', this.selectedItems.length);
    } else {
      this.selectedItems = [];
      console.log('Cleared all selections');
    }
  }

  // 更新全選狀態
  updateSelectAllState() {
    if (this.requirementList.length === 0) {
      this.isAllSelected = false;
    } else {
      this.isAllSelected = this.selectedItems.length === this.requirementList.length;
    }
  }

  // 檢查項目是否被選中
  isItemSelected(item: GetRequirement): boolean {
    return this.selectedItems.some(selected => selected.CRequirementID === item.CRequirementID);
  }

  // 開啟批次編輯對話框
  openBatchEdit(dialog: TemplateRef<any>) {
    if (this.selectedItems.length === 0) {
      this.message.showErrorMSG('請先選擇要編輯的項目');
      return;
    }

    this.isBatchEditMode = true;

    // 初始化批次編輯項目資料
    this.batchEditItems = this.selectedItems.map(item => ({
      CRequirementID: item.CRequirementID,
      CBuildCaseID: item.CBuildCaseID,
      CLocation: item.CLocation,
      CRequirement: item.CRequirement,
      CHouseType: item.CHouseType ? [...item.CHouseType] : [],
      CSort: item.CSort,
      CStatus: item.CStatus,
      CUnitPrice: item.CUnitPrice || 0,
      CUnit: item.CUnit,
      CIsShow: item.CIsShow || false,
      CIsSimple: item.CIsSimple || false,
      CRemark: item.CRemark
    }));

    this.dialogService.open(dialog);
  }

  // 批次保存
  batchSave(ref: any) {
    if (this.batchEditItems.length === 0) {
      this.message.showErrorMSG('沒有要更新的項目');
      return;
    }

    // 直接使用編輯後的資料
    const batchData: SaveDataRequirement[] = this.batchEditItems.map(item => ({
      CRequirementID: item.CRequirementID,
      CBuildCaseID: item.CBuildCaseID,
      CLocation: item.CLocation,
      CRequirement: item.CRequirement,
      CHouseType: item.CHouseType,
      CSort: item.CSort,
      CStatus: item.CStatus,
      CUnitPrice: item.CUnitPrice,
      CUnit: item.CUnit,
      CIsShow: item.CIsShow,
      CIsSimple: item.CIsSimple,
      CRemark: item.CRemark
    }));

    // 調用批次保存 API
    this.requirementService.apiRequirementBatchSaveDataPost$Json({
      body: batchData
    }).subscribe(res => {
      if (res.StatusCode === 0) {
        this.message.showSucessMSG(`成功批次更新 ${this.batchEditItems.length} 個項目`);
        this.selectedItems = [];
        this.batchEditItems = [];
        this.updateSelectAllState();
        this.getList();
        ref.close();
      } else {
        this.message.showErrorMSG(res.Message || '批次更新失敗');
      }
    });
  }

  // 取消批次編輯
  cancelBatchEdit(ref: any) {
    this.isBatchEditMode = false;
    this.batchEditItems = [];
    ref.close();
  }

  // 備用：如果需要手動建立需求項目的方法
  private batchCreateRequirements(requirements: SaveDataRequirement[]) {
    const batchRequests = requirements.map(requirement =>
      this.requirementService.apiRequirementSaveDataPost$Json({
        body: requirement
      })
    );

    Promise.all(batchRequests.map(req => req.toPromise()))
      .then(responses => {
        const successCount = responses.filter(res => res?.StatusCode === 0).length;
        this.message.showSucessMSG(`成功建立 ${successCount} 個需求項目`);
        this.getList();
      })
      .catch(error => {
        console.error('批次建立需求失敗:', error);
        this.message.showErrorMSG('批次建立需求時發生錯誤');
      });
  }
}
