<nb-card accent="success">
  <nb-card-header>
    <ngx-breadcrumb></ngx-breadcrumb>
  </nb-card-header>

  <!-- 搜尋區域 -->
  <nb-card-body class="bg-white pb-0">
    <div class="col-12">
      <div class="row">
        <div class="form-group col-12 col-md-4">
          <label for="buildCase" class="label mr-2">建案</label>
          <nb-select [(ngModel)]="getListRequirementRequest.CBuildCaseID" class="col-9">
            <nb-option *ngFor="let case of buildCaseList" [value]="case.cID">
              {{ case.CBuildCaseName }}
            </nb-option>
          </nb-select>
        </div>
        <div class="form-group col-12 col-md-4">
          <label for="groupName" class="label mr-2">區域</label>
          <input type="text" nbInput id="groupName" name="groupName" placeholder="區域"
            [(ngModel)]="getListRequirementRequest.CLocation">
        </div>
        <div class="form-group col-12 col-md-4">
          <label for="requirement" class="label mr-2">工程項目</label>
          <input type="text" nbInput id="requirement" name="requirement" placeholder="工程項目"
            [(ngModel)]="getListRequirementRequest.CRequirement">
        </div>
      </div>
      <div class="row">
        <div class="form-group col-12 col-md-4">
          <label for="houseType" class="label mr-2">類型</label>
          <nb-select [(ngModel)]="getListRequirementRequest.CHouseType" class="col-9" multiple>
            <nb-option *ngFor="let type of houseType" [value]="type.value">
              {{ type.label }}
            </nb-option>
          </nb-select>
        </div>
        <div class="form-group col-12 col-md-4">
          <label for="status" class="label mr-2">狀態</label>
          <nb-select [(ngModel)]="getListRequirementRequest.CStatus" class="col-9">
            <nb-option [value]="-1">全部</nb-option>
            <nb-option [value]="1">啟用</nb-option>
            <nb-option [value]="0">停用</nb-option>
          </nb-select>
        </div>
        <div class="form-group col-12 col-md-4">
          <label for="isShow" class="label mr-2">預約需求</label>
          <nb-select [(ngModel)]="getListRequirementRequest.CIsShow" class="col-9" placeholder="全部">
            <nb-option [value]="null">全部</nb-option>
            <nb-option [value]="true">是</nb-option>
            <nb-option [value]="false">否</nb-option>
          </nb-select>
        </div>
      </div>
      <div class="row">
        <div class="form-group col-12 col-md-4">
          <label for="isSimple" class="label mr-2">簡易客變</label>
          <nb-select [(ngModel)]="getListRequirementRequest.CIsSimple" class="col-9" placeholder="全部">
            <nb-option [value]="null">全部</nb-option>
            <nb-option [value]="true">是</nb-option>
            <nb-option [value]="false">否</nb-option>
          </nb-select>
        </div>
        <div class="form-group col-12 col-md-4"></div>
      </div>
      <div class="row">
        <div class="col-md-6"></div>
        <div class="form-group col-12 col-md-6 text-right">
          <button class="btn btn-secondary mr-2" (click)="resetSearch()"><i class="fas fa-undo mr-1"></i>重置</button>
          <button class="btn btn-info mr-2" (click)="getList()"><i class="fas fa-search mr-1"></i>查詢</button>
          <button class="btn btn-warning mr-2" (click)="openSpaceTemplateSelector()"><i
              class="fas fa-layer-group mr-1"></i>模板新增</button>
          <button class="btn btn-primary mr-2" (click)="openBatchEdit(batchEditDialog)"
            *ngIf="selectedItems.length > 0"><i class="fas fa-edit mr-1"></i>批次編輯 ({{selectedItems.length}})</button>
          <button class="btn btn-success mr-2" (click)="add(dialog)" *ngIf="isCreate"><i
              class="fas fa-plus mr-1"></i>新增</button>
        </div>
      </div>
    </div>
  </nb-card-body>

  <!-- 建案需求列表 -->
  <nb-card-body class="bg-white pb-0">
    <div class="col-12 mt-3">
      <div class="table-responsive">
        <table class="table table-striped border " style="min-width: 800px; background-color:#f3f3f3;">
          <thead>
            <tr style="background-color: #27ae60;" class="d-flex text-white">
              <th scope="col" class="col-1 text-center">
                <nb-checkbox [(ngModel)]="isAllSelected" (ngModelChange)="toggleSelectAll()">
                  全選
                </nb-checkbox>
              </th>
              <th scope="col" class="col-2">建案名稱</th>
              <th scope="col" class="col-1">區域</th>
              <th scope="col" class="col-2">工程項目</th>
              <th scope="col" class="col-1">類型</th>
              <th scope="col" class="col-1">排序</th>
              <th scope="col" class="col-1">狀態</th>
              <th scope="col" class="col-1">預約需求</th>
              <th scope="col" class="col-1">簡易客變</th>
              <th scope="col" class="col-1">單價</th>
              <th scope="col" class="col-1">操作功能</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let data of requirementList; let i = index" class="d-flex">
              <td class="col-1 text-center">
                <nb-checkbox [ngModel]="isItemSelected(data)" (ngModelChange)="toggleItemSelection(data)">
                </nb-checkbox>
              </td>
              <td class="col-2">{{ data.CBuildCaseName }}</td>
              <td class="col-1">{{ data.CLocation }}</td>
              <td class="col-2">{{ data.CRequirement }}</td>
              <td class="col-1">{{ getHouseType(data.CHouseType || []) }}</td>
              <td class="col-1">{{ data.CSort }}</td>
              <td class="col-1">{{ data.CStatus | getStatusName }}</td>
              <td class="col-1">{{ getCIsShowText(data) }}</td>
              <td class="col-1">{{ getCIsSimpleText(data) }}</td>
              <td class="col-1">{{ (data.CUnitPrice || 0) | ngxNumberWithCommas }}</td>
              <td class="col-1">
                <button *ngIf="isUpdate" type="button" class="btn btn-outline-success m-1  btn-sm"
                  (click)="onEdit(data,dialog)"><i class="fas fa-edit mr-1"></i>編輯</button>
                <button *ngIf="isDelete" type="button" class="btn btn-outline-danger m-1  btn-sm"
                  (click)="onDelete(data)"><i class="far fa-trash-alt mr-1"></i>刪除</button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <ngx-pagination [CollectionSize]="totalRecords" [(Page)]="pageIndex" [PageSize]="pageSize"
        (PageChange)="getList()">
      </ngx-pagination>
    </div>
  </nb-card-body>
</nb-card>

<!-- 建案對話框 -->
<ng-template #dialog let-data let-ref="dialogRef">
  <nb-card style="height: 100%; overflow: auto; max-width: 500px;" class="mr-md-5 ml-md-5">
    <nb-card-header>
      <span *ngIf="isNew===true">新增建案需求</span>
      <span *ngIf="isNew===false">編輯建案需求</span>
    </nb-card-header>
    <nb-card-body style="padding:1rem 2rem">
      <div class="row">
        <div class="col-12 col-md-12">
          <div class="row">
            <app-form-group [label]="'建案名稱'" [labelFor]="'CBuildCaseID'" [isRequired]="true">
              <nb-select class="flex-grow-1" placeholder="請選擇" id="CBuildCaseID" name="CBuildCaseID"
                [(selected)]="saveRequirement.CBuildCaseID">
                <nb-option langg *ngFor="let b of buildCaseList" [value]="b.cID"> {{b.CBuildCaseName}}</nb-option>
              </nb-select>
            </app-form-group>
            <app-form-group [label]="'區域'" [labelFor]="'CLocation'" [isRequired]="false">
              <input type="text" nbInput class="flex-grow-1" id="CLocation" name="CLocation" placeholder="區域"
                [(ngModel)]="saveRequirement.CLocation" maxlength="20">
            </app-form-group>
            <app-form-group [label]="'工程項目'" [labelFor]="'CRequirement'" [isRequired]="true">
              <input type="text" nbInput class="flex-grow-1" id="CRequirement" name="CRequirement" placeholder="工程項目"
                [(ngModel)]="saveRequirement.CRequirement" maxlength="50">
            </app-form-group>
            <app-form-group [label]="'排序'" [labelFor]="'CSort'" [isRequired]="true">
              <input type="number" nbInput class="flex-grow-1" id="CSort" name="CSort" placeholder="排序"
                [(ngModel)]="saveRequirement.CSort">
            </app-form-group>
            <app-form-group [label]="'類型'" [labelFor]="'CHouseType'" [isRequired]="true">
              <nb-select class="flex-grow-1" placeholder="請選擇" id="CHouseType" name="CHouseType"
                [(selected)]="saveRequirement.CHouseType" multiple>
                <nb-option langg *ngFor="let type of houseType" [value]="type.value"> {{type.label}}</nb-option>
              </nb-select>
            </app-form-group>
            <app-form-group [label]="'狀態'" [labelFor]="'CStatus'" [isRequired]="true">
              <nb-select class="flex-grow-1" placeholder="請選擇" id="CStatus" name="CStatus"
                [(selected)]="saveRequirement.CStatus">
                <nb-option langg *ngFor="let status of statusOptions" [value]="status.value">
                  {{status.label}}</nb-option>
              </nb-select>
            </app-form-group>
            <app-form-group [label]="'單價'" [labelFor]="'CUnitPrice'" [isRequired]="true">
              <input type="number" nbInput class="flex-grow-1" id="CUnitPrice" name="CUnitPrice" placeholder="單價"
                [(ngModel)]="saveRequirement.CUnitPrice">
            </app-form-group>
            <app-form-group [label]="'單位'" [labelFor]="'CUnit'" [isRequired]="true">
              <input type="text" nbInput class="flex-grow-1" id="CUnit" name="CUnit" placeholder="單位"
                [(ngModel)]="saveRequirement.CUnit">
            </app-form-group>
            <app-form-group [label]="'預約需求'" [labelFor]="'CIsShow'" [isRequired]="true">
              <nb-checkbox id="CIsShow" name="CIsShow" [(ngModel)]="saveRequirement.CIsShow" class="flex-grow-1">
                顯示在預約需求清單
              </nb-checkbox>
            </app-form-group>
            <app-form-group [label]="'簡易客變'" [labelFor]="'CIsSimple'" [isRequired]="true">
              <nb-checkbox id="CIsSimple" name="CIsSimple" [(ngModel)]="saveRequirement.CIsSimple" class="flex-grow-1">
                設定為簡易客變
              </nb-checkbox>
            </app-form-group>
            <app-form-group [label]="'備註說明'" [labelFor]="'CRemark'" [isRequired]="false">
              <textarea nbInput class="flex-grow-1" id="CRemark" name="CRemark" placeholder="備註說明"
                [(ngModel)]="saveRequirement.CRemark" maxlength="100" rows="3"></textarea>
            </app-form-group>
          </div>
        </div>
      </div>
    </nb-card-body>
    <nb-card-footer>
      <div class="row">
        <div class="col-12 text-center">
          <button class="btn btn-success mr-2" (click)="save(ref)">確定</button>
          <button class="btn btn-danger mr-2" (click)="ref.close()">取消</button>
        </div>
      </div>
    </nb-card-footer>
  </nb-card>
</ng-template>

<!-- 批次編輯對話框 -->
<ng-template #batchEditDialog let-data let-ref="dialogRef">
  <nb-card style="height: 100%; overflow: auto; max-width: 600px;" class="mr-md-5 ml-md-5">
    <nb-card-header>
      <span>批次編輯建案需求 (已選擇 {{selectedItems.length}} 個項目)</span>
    </nb-card-header>
    <nb-card-body style="padding:1rem 2rem">
      <div class="row">
        <div class="col-12">
          <div class="alert alert-info">
            <i class="fas fa-info-circle mr-2"></i>
            請選擇要批次更新的欄位，未勾選的欄位將保持原值不變
          </div>

          <!-- 狀態批次編輯 -->
          <app-form-group [label]="'狀態'" [labelFor]="'batchStatus'" [isRequired]="false">
            <div class="d-flex align-items-center">
              <nb-checkbox [(ngModel)]="batchEditData.updateStatus" class="mr-3">
                批次更新
              </nb-checkbox>
              <nb-select class="flex-grow-1" placeholder="請選擇" id="batchStatus" name="batchStatus"
                [(selected)]="batchEditData.CStatus" [disabled]="!batchEditData.updateStatus">
                <nb-option *ngFor="let status of statusOptions" [value]="status.value">
                  {{status.label}}
                </nb-option>
              </nb-select>
            </div>
          </app-form-group>

          <!-- 預約需求批次編輯 -->
          <app-form-group [label]="'預約需求'" [labelFor]="'batchIsShow'" [isRequired]="false">
            <div class="d-flex align-items-center">
              <nb-checkbox [(ngModel)]="batchEditData.updateIsShow" class="mr-3">
                批次更新
              </nb-checkbox>
              <nb-checkbox [(ngModel)]="batchEditData.CIsShow" [disabled]="!batchEditData.updateIsShow"
                class="flex-grow-1">
                顯示在預約需求清單
              </nb-checkbox>
            </div>
          </app-form-group>

          <!-- 簡易客變批次編輯 -->
          <app-form-group [label]="'簡易客變'" [labelFor]="'batchIsSimple'" [isRequired]="false">
            <div class="d-flex align-items-center">
              <nb-checkbox [(ngModel)]="batchEditData.updateIsSimple" class="mr-3">
                批次更新
              </nb-checkbox>
              <nb-checkbox [(ngModel)]="batchEditData.CIsSimple" [disabled]="!batchEditData.updateIsSimple"
                class="flex-grow-1">
                設定為簡易客變
              </nb-checkbox>
            </div>
          </app-form-group>

          <!-- 排序批次編輯 -->
          <app-form-group [label]="'排序'" [labelFor]="'batchSort'" [isRequired]="false">
            <div class="d-flex align-items-center">
              <nb-checkbox [(ngModel)]="batchEditData.updateSort" class="mr-3">
                批次更新
              </nb-checkbox>
              <div class="flex-grow-1 d-flex">
                <nb-select [(selected)]="batchEditData.sortOperation" [disabled]="!batchEditData.updateSort"
                  class="mr-2" style="width: 120px;">
                  <nb-option value="set">設定為</nb-option>
                  <nb-option value="add">增加</nb-option>
                </nb-select>
                <input type="number" nbInput [(ngModel)]="batchEditData.CSort" [disabled]="!batchEditData.updateSort"
                  placeholder="排序值" class="flex-grow-1">
              </div>
            </div>
          </app-form-group>

          <!-- 單價批次編輯 -->
          <app-form-group [label]="'單價'" [labelFor]="'batchUnitPrice'" [isRequired]="false">
            <div class="d-flex align-items-center">
              <nb-checkbox [(ngModel)]="batchEditData.updateUnitPrice" class="mr-3">
                批次更新
              </nb-checkbox>
              <div class="flex-grow-1 d-flex">
                <nb-select [(selected)]="batchEditData.priceOperation" [disabled]="!batchEditData.updateUnitPrice"
                  class="mr-2" style="width: 120px;">
                  <nb-option value="set">設定為</nb-option>
                  <nb-option value="multiply">乘以</nb-option>
                </nb-select>
                <input type="number" nbInput [(ngModel)]="batchEditData.CUnitPrice"
                  [disabled]="!batchEditData.updateUnitPrice" placeholder="單價值" class="flex-grow-1" step="0.01">
              </div>
            </div>
          </app-form-group>
        </div>
      </div>
    </nb-card-body>
    <nb-card-footer>
      <div class="row">
        <div class="col-12 text-center">
          <button class="btn btn-success mr-2" (click)="batchSave(ref)">確定批次更新</button>
          <button class="btn btn-danger mr-2" (click)="cancelBatchEdit(ref)">取消</button>
        </div>
      </div>
    </nb-card-footer>
  </nb-card>
</ng-template>