(function(){"use strict";var xs=function(t){if(null===t)return"null";if(void 0===t)return"undefined";var e=typeof t;return"object"===e&&(Array.prototype.isPrototypeOf(t)||t.constructor&&"Array"===t.constructor.name)?"array":"object"===e&&(String.prototype.isPrototypeOf(t)||t.constructor&&"String"===t.constructor.name)?"string":e},mo=function(t){return{eq:t}},Wr=mo(function(t,e){return t===e}),Wn=function(t){return mo(function(e,n){if(e.length!==n.length)return!1;for(var o=e.length,r=0;r<o;r++)if(!t.eq(e[r],n[r]))return!1;return!0})},hr=function(t){return mo(function(e,n){var l,d,u,o=Object.keys(e),r=Object.keys(n);if(!(l=Wr,d=Wn(l),u=function(d){return Array.prototype.slice.call(d).sort(undefined)},mo(function(m,p){return d.eq(u(m),u(p))})).eq(o,r))return!1;for(var a=o.length,s=0;s<a;s++){var i=o[s];if(!t.eq(e[i],n[i]))return!1}return!0})},br=mo(function(t,e){if(t===e)return!0;var n=xs(t);return n===xs(e)&&(-1!==["undefined","boolean","number","string","function","xml","null"].indexOf(n)?t===e:"array"===n?Wn(br).eq(t,e):"object"===n&&hr(br).eq(t,e))});const Na=Object.getPrototypeOf,vr=(t,e,n)=>{var o;return!!n(t,e.prototype)||(null===(o=t.constructor)||void 0===o?void 0:o.name)===e.name},sn=t=>e=>(n=>{const o=typeof n;return null===n?"null":"object"===o&&Array.isArray(n)?"array":"object"===o&&vr(n,String,(r,a)=>a.isPrototypeOf(r))?"string":o})(e)===t,Kr=t=>e=>typeof e===t,Ra=t=>e=>t===e,Ce=(t,e)=>At(t)&&vr(t,e,(n,o)=>Na(n)===o),Nt=sn("string"),At=sn("object"),ln=t=>Ce(t,Object),Ee=sn("array"),Oe=Ra(null),xn=Kr("boolean"),dt=Ra(void 0),pe=t=>null==t,st=t=>!pe(t),Yt=Kr("function"),nn=Kr("number"),Sn=(t,e)=>{if(Ee(t)){for(let n=0,o=t.length;n<o;++n)if(!e(t[n]))return!1;return!0}return!1},jt=()=>{},Fe=(t,e)=>(...n)=>t(e.apply(null,n)),Aa=(t,e)=>n=>t(e(n)),ut=t=>()=>t,qe=t=>t,kn=(t,e)=>t===e;function Ct(t,...e){return(...n)=>{const o=e.concat(n);return t.apply(null,o)}}const $o=t=>e=>!t(e),Ue=t=>()=>{throw new Error(t)},je=t=>t(),En=t=>{t()},de=ut(!1),ye=ut(!0);class S{constructor(e,n){this.tag=e,this.value=n}static some(e){return new S(!0,e)}static none(){return S.singletonNone}fold(e,n){return this.tag?n(this.value):e()}isSome(){return this.tag}isNone(){return!this.tag}map(e){return this.tag?S.some(e(this.value)):S.none()}bind(e){return this.tag?e(this.value):S.none()}exists(e){return this.tag&&e(this.value)}forall(e){return!this.tag||e(this.value)}filter(e){return!this.tag||e(this.value)?this:S.none()}getOr(e){return this.tag?this.value:e}or(e){return this.tag?this:e}getOrThunk(e){return this.tag?this.value:e()}orThunk(e){return this.tag?this:e()}getOrDie(e){if(this.tag)return this.value;throw new Error(e??"Called getOrDie on None")}static from(e){return st(e)?S.some(e):S.none()}getOrNull(){return this.tag?this.value:null}getOrUndefined(){return this.value}each(e){this.tag&&e(this.value)}toArray(){return this.tag?[this.value]:[]}toString(){return this.tag?`some(${this.value})`:"none()"}}S.singletonNone=new S(!1);const Ro=Array.prototype.slice,qo=Array.prototype.indexOf,Ss=Array.prototype.push,Vo=(t,e)=>qo.call(t,e),Lt=(t,e)=>Vo(t,e)>-1,ie=(t,e)=>{for(let n=0,o=t.length;n<o;n++)if(e(t[n],n))return!0;return!1},te=(t,e)=>{const n=t.length,o=new Array(n);for(let r=0;r<n;r++)o[r]=e(t[r],r);return o},J=(t,e)=>{for(let n=0,o=t.length;n<o;n++)e(t[n],n)},Ta=(t,e)=>{for(let n=t.length-1;n>=0;n--)e(t[n],n)},Ve=(t,e)=>{const n=[],o=[];for(let r=0,a=t.length;r<a;r++){const s=t[r];(e(s,r)?n:o).push(s)}return{pass:n,fail:o}},Ot=(t,e)=>{const n=[];for(let o=0,r=t.length;o<r;o++){const a=t[o];e(a,o)&&n.push(a)}return n},Kn=(t,e,n)=>(Ta(t,(o,r)=>{n=e(n,o,r)}),n),Re=(t,e,n)=>(J(t,(o,r)=>{n=e(n,o,r)}),n),yr=(t,e,n)=>{for(let o=0,r=t.length;o<r;o++){const a=t[o];if(e(a,o))return S.some(a);if(n(a,o))break}return S.none()},he=(t,e)=>yr(t,e,de),go=(t,e)=>{for(let n=0,o=t.length;n<o;n++)if(e(t[n],n))return S.some(n);return S.none()},Cr=t=>{const e=[];for(let n=0,o=t.length;n<o;++n){if(!Ee(t[n]))throw new Error("Arr.flatten item "+n+" was not an array, input: "+t);Ss.apply(e,t[n])}return e},gn=(t,e)=>Cr(te(t,e)),_n=(t,e)=>{for(let n=0,o=t.length;n<o;++n)if(!0!==e(t[n],n))return!1;return!0},Ao=t=>{const e=Ro.call(t,0);return e.reverse(),e},Gn=(t,e)=>Ot(t,n=>!Lt(e,n)),wr=(t,e)=>{const n={};for(let o=0,r=t.length;o<r;o++){const a=t[o];n[String(a)]=e(a,o)}return n},To=(t,e)=>{const n=Ro.call(t,0);return n.sort(e),n},Wo=(t,e)=>e>=0&&e<t.length?S.some(t[e]):S.none(),We=t=>Wo(t,0),Nn=t=>Wo(t,t.length-1),Ae=Yt(Array.from)?Array.from:t=>Ro.call(t),Ko=(t,e)=>{for(let n=0;n<t.length;n++){const o=e(t[n],n);if(o.isSome())return o}return S.none()},tn=Object.keys,Gr=Object.hasOwnProperty,le=(t,e)=>{const n=tn(t);for(let o=0,r=n.length;o<r;o++){const a=n[o];e(t[a],a)}},Yr=(t,e)=>xr(t,(n,o)=>({k:o,v:e(n,o)})),xr=(t,e)=>{const n={};return le(t,(o,r)=>{const a=e(o,r);n[a.k]=a.v}),n},Rn=t=>(e,n)=>{t[n]=e},Sr=(t,e,n,o)=>{le(t,(r,a)=>{(e(r,a)?n:o)(r,a)})},Go=(t,e)=>{const n={};return Sr(t,e,Rn(n),jt),n},kr=(t,e)=>{const n=[];return le(t,(o,r)=>{n.push(e(o,r))}),n},Er=t=>kr(t,qe),ce=(t,e)=>It(t,e)?S.from(t[e]):S.none(),It=(t,e)=>Gr.call(t,e),Hn=(t,e)=>It(t,e)&&null!=t[e],Oa=t=>{const e={};return J(t,n=>{e[n]={}}),tn(e)},Ba=t=>void 0!==t.length,_r=Array.isArray,Yo=(t,e,n)=>{if(!t)return!1;if(n=n||t,Ba(t)){for(let o=0,r=t.length;o<r;o++)if(!1===e.call(n,t[o],o,t))return!1}else for(const o in t)if(It(t,o)&&!1===e.call(n,t[o],o,t))return!1;return!0},Nr=(t,e)=>{const n=[];return Yo(t,(o,r)=>{n.push(e(o,r,t))}),n},Oo=(t,e)=>{const n=[];return Yo(t,(o,r)=>{e&&!e(o,r,t)||n.push(o)}),n},Bo=(t,e,n,o)=>{let r=dt(n)?t[0]:n;for(let a=0;a<t.length;a++)r=e.call(o,r,t[a],a);return r},Xo=(t,e,n)=>{for(let o=0,r=t.length;o<r;o++)if(e.call(n,t[o],o,t))return o;return-1},Fn=t=>t[t.length-1],fo=t=>{let e,n=!1;return(...o)=>(n||(n=!0,e=t.apply(null,o)),e)},Xr=()=>Yn(0,0),Yn=(t,e)=>({major:t,minor:e}),U={nu:Yn,detect:(t,e)=>{const n=String(e).toLowerCase();return 0===t.length?Xr():((o,r)=>{const a=((i,l)=>{for(let c=0;c<i.length;c++){const d=i[c];if(d.test(l))return d}})(o,r);if(!a)return{major:0,minor:0};const s=i=>Number(r.replace(a,"$"+i));return Yn(s(1),s(2))})(t,n)},unknown:Xr},L=(t,e)=>{const n=String(e).toLowerCase();return he(t,o=>o.search(n))},G=(t,e,n)=>""===e||t.length>=e.length&&t.substr(n,n+e.length)===e,Q=(t,e)=>ht(t,e)?t.substring(e.length):t,ct=(t,e,n=0,o)=>{const r=t.indexOf(e,n);return-1!==r&&(!!dt(o)||r+e.length<=o)},ht=(t,e)=>G(t,e,0),pt=(t,e)=>G(t,e,t.length-e.length),Ft=t=>e=>e.replace(t,""),Wt=Ft(/^\s+|\s+$/g),ne=Ft(/^\s+/g),oe=Ft(/\s+$/g),Qt=t=>t.length>0,Xt=t=>!Qt(t),we=(t,e=10)=>{const n=parseInt(t,e);return isNaN(n)?S.none():S.some(n)},Ke=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,ue=t=>e=>ct(e,t),Qo=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:t=>ct(t,"edge/")&&ct(t,"chrome")&&ct(t,"safari")&&ct(t,"applewebkit")},{name:"Chromium",brand:"Chromium",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,Ke],search:t=>ct(t,"chrome")&&!ct(t,"chromeframe")},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:t=>ct(t,"msie")||ct(t,"trident")},{name:"Opera",versionRegexes:[Ke,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:ue("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:ue("firefox")},{name:"Safari",versionRegexes:[Ke,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:t=>(ct(t,"safari")||ct(t,"mobile/"))&&ct(t,"applewebkit")}],Rr=[{name:"Windows",search:ue("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:t=>ct(t,"iphone")||ct(t,"ipad"),versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:ue("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"macOS",search:ue("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:ue("linux"),versionRegexes:[]},{name:"Solaris",search:ue("sunos"),versionRegexes:[]},{name:"FreeBSD",search:ue("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:ue("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],Xn={browsers:ut(Qo),oses:ut(Rr)},Ar="Chromium",jl="Firefox",Hl=t=>{const e=t.current,o=r=>()=>e===r;return{current:e,version:t.version,isEdge:o("Edge"),isChromium:o(Ar),isIE:o("IE"),isOpera:o("Opera"),isFirefox:o(jl),isSafari:o("Safari")}},su=()=>Hl({current:void 0,version:U.unknown()}),$l=Hl,pi=(ut("Edge"),ut(Ar),ut("IE"),ut("Opera"),ut(jl),ut("Safari"),"Windows"),cn="Android",ks="Solaris",Es="FreeBSD",_s="ChromeOS",Tr=t=>{const e=t.current,o=r=>()=>e===r;return{current:e,version:t.version,isWindows:o(pi),isiOS:o("iOS"),isAndroid:o(cn),isMacOS:o("macOS"),isLinux:o("Linux"),isSolaris:o(ks),isFreeBSD:o(Es),isChromeOS:o(_s)}},iu=()=>Tr({current:void 0,version:U.unknown()}),lu=Tr,Or=(ut(pi),ut("iOS"),ut(cn),ut("Linux"),ut("macOS"),ut(ks),ut(Es),ut(_s),t=>window.matchMedia(t).matches);let vi=fo(()=>((t,e,n)=>{const o=Xn.browsers(),r=Xn.oses(),a=e.bind(l=>{return c=o,Ko(l.brands,u=>{const m=u.brand.toLowerCase();return he(c,p=>{var g;return m===(null===(g=p.brand)||void 0===g?void 0:g.toLowerCase())}).map(p=>({current:p.name,version:U.nu(parseInt(u.version,10),0)}))});var c}).orThunk(()=>{return L(o,c=t).map(d=>{const u=U.detect(d.versionRegexes,c);return{current:d.name,version:u}});var c}).fold(su,$l),s=(l=r,c=t,L(l,c).map(d=>{const u=U.detect(d.versionRegexes,c);return{current:d.name,version:u}})).fold(iu,lu),i=((l,c,d,u)=>{const m=l.isiOS()&&!0===/ipad/i.test(d),p=l.isiOS()&&!m,g=l.isiOS()||l.isAndroid(),h=g||u("(pointer:coarse)"),f=m||!p&&g&&u("(min-device-width:768px)"),b=p||g&&!f,y=c.isSafari()&&l.isiOS()&&!1===/safari/i.test(d),C=!b&&!f&&!y;return{isiPad:ut(m),isiPhone:ut(p),isTablet:ut(f),isPhone:ut(b),isTouch:ut(h),isAndroid:l.isAndroid,isiOS:l.isiOS,isWebView:ut(y),isDesktop:ut(C)}})(s,a,t,n);var l,c;return{browser:a,os:s,deviceType:i}})(navigator.userAgent,S.from(navigator.userAgentData),Or));const po=()=>vi(),yi=navigator.userAgent,Ci=po(),Qn=Ci.browser,Jn=Ci.os,Zo=Ci.deviceType,ql=-1!==yi.indexOf("Windows Phone"),Jt={transparentSrc:"data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",documentMode:Qn.isIE()?document.documentMode||7:10,cacheSuffix:null,container:null,canHaveCSP:!Qn.isIE(),windowsPhone:ql,browser:{current:Qn.current,version:Qn.version,isChromium:Qn.isChromium,isEdge:Qn.isEdge,isFirefox:Qn.isFirefox,isIE:Qn.isIE,isOpera:Qn.isOpera,isSafari:Qn.isSafari},os:{current:Jn.current,version:Jn.version,isAndroid:Jn.isAndroid,isChromeOS:Jn.isChromeOS,isFreeBSD:Jn.isFreeBSD,isiOS:Jn.isiOS,isLinux:Jn.isLinux,isMacOS:Jn.isMacOS,isSolaris:Jn.isSolaris,isWindows:Jn.isWindows},deviceType:{isDesktop:Zo.isDesktop,isiPad:Zo.isiPad,isiPhone:Zo.isiPhone,isPhone:Zo.isPhone,isTablet:Zo.isTablet,isTouch:Zo.isTouch,isWebView:Zo.isWebView}},Vl=/^\s*|\s*$/g,Wl=t=>pe(t)?"":(""+t).replace(Vl,""),Kl=function(t,e,n,o){o=o||this,t&&(n&&(t=t[n]),Yo(t,(r,a)=>!1!==e.call(o,r,a,n)&&(Kl(r,e,n,o),!0)))},ot={trim:Wl,isArray:_r,is:(t,e)=>e?!("array"!==e||!_r(t))||typeof t===e:void 0!==t,toArray:t=>{if(_r(t))return t;{const e=[];for(let n=0,o=t.length;n<o;n++)e[n]=t[n];return e}},makeMap:(t,e,n={})=>{const o=Nt(t)?t.split(e||","):t||[];let r=o.length;for(;r--;)n[o[r]]={};return n},each:Yo,map:Nr,grep:Oo,inArray:(t,e)=>{if(t)for(let n=0,o=t.length;n<o;n++)if(t[n]===e)return n;return-1},hasOwn:It,extend:(t,...e)=>{for(let n=0;n<e.length;n++){const o=e[n];for(const r in o)if(It(o,r)){const a=o[r];void 0!==a&&(t[r]=a)}}return t},walk:Kl,resolve:(t,e=window)=>{const n=t.split(".");for(let o=0,r=n.length;o<r&&(e=e[n[o]]);o++);return e},explode:(t,e)=>Ee(t)?t:""===t?[]:Nr(t.split(e||","),Wl),_addCacheSuffix:t=>{const e=Jt.cacheSuffix;return e&&(t+=(-1===t.indexOf("?")?"?":"&")+e),t}},tr=(t,e,n=kn)=>t.exists(o=>n(o,e)),on=(t,e,n)=>t.isSome()&&e.isSome()?S.some(n(t.getOrDie(),e.getOrDie())):S.none(),ho=(t,e)=>t?S.some(e):S.none(),Gl=typeof window<"u"?window:Function("return this;")(),wi=(t,e)=>((n,o)=>{let r=o??Gl;for(let a=0;a<n.length&&null!=r;++a)r=r[n[a]];return r})(t.split("."),e),Yl=Object.getPrototypeOf,ge=t=>t.dom.nodeName.toLowerCase(),Xl=t=>t.dom.nodeType,Ns=t=>e=>Xl(e)===t,xi=t=>fn(t)&&(t=>{const e=wi("ownerDocument.defaultView",t);return At(t)&&((n=e,((o,r)=>{const a=wi(o,r);if(null==a)throw new Error(o+" not available on this browser");return a})("HTMLElement",n)).prototype.isPrototypeOf(t)||/^HTML\w*Element$/.test(Yl(t).constructor.name));var n})(t.dom),fn=Ns(1),pn=Ns(3),Si=Ns(9),ki=Ns(11),Pa=t=>e=>fn(e)&&ge(e)===t,Rs=(t,e,n)=>{if(!(Nt(n)||xn(n)||nn(n)))throw console.error("Invalid call to Attribute.set. Key ",e,":: Value ",n,":: Element ",t),new Error("Attribute value was not simple");t.setAttribute(e,n+"")},Me=(t,e,n)=>{Rs(t.dom,e,n)},Do=(t,e)=>{const n=t.dom;le(e,(o,r)=>{Rs(n,r,o)})},An=(t,e)=>{const n=t.dom.getAttribute(e);return null===n?void 0:n},Zn=(t,e)=>S.from(An(t,e)),As=(t,e)=>{const n=t.dom;return!(!n||!n.hasAttribute)&&n.hasAttribute(e)},Le=(t,e)=>{t.dom.removeAttribute(e)},Ql=t=>Re(t.dom.attributes,(e,n)=>(e[n.name]=n.value,e),{}),Ei=(t,e)=>{const n=An(t,e);return void 0===n||""===n?[]:n.split(" ")},Qr=t=>void 0!==t.dom.classList,Ts=t=>Ei(t,"class"),Os=(t,e)=>((n,o,r)=>{const a=Ei(n,o).concat([r]);return Me(n,o,a.join(" ")),!0})(t,"class",e),Jl=(t,e)=>((n,o,r)=>{const a=Ot(Ei(n,o),s=>s!==r);return a.length>0?Me(n,o,a.join(" ")):Le(n,o),!1})(t,"class",e),Jr=(t,e)=>{Qr(t)?t.dom.classList.add(e):Os(t,e)},Bs=t=>{0===(Qr(t)?t.dom.classList:Ts(t)).length&&Le(t,"class")},Br=(t,e)=>{Qr(t)?t.dom.classList.remove(e):Jl(t,e),Bs(t)},Ds=(t,e)=>Qr(t)&&t.dom.classList.contains(e),Dr=t=>{if(null==t)throw new Error("Node cannot be null or undefined");return{dom:t}},Ma=(t,e)=>{const n=(e||document).createElement("div");if(n.innerHTML=t,!n.hasChildNodes()||n.childNodes.length>1){const o="HTML does not have a single root node";throw console.error(o,t),new Error(o)}return Dr(n.childNodes[0])},Be=(t,e)=>{const n=(e||document).createElement(t);return Dr(n)},bo=(t,e)=>{const n=(e||document).createTextNode(t);return Dr(n)},A=Dr,ta=(t,e)=>{const n=[],o=a=>(n.push(a),e(a));let r=e(t);do{r=r.bind(o)}while(r.isSome());return n},hn=(t,e)=>{const n=t.dom;if(1!==n.nodeType)return!1;{const o=n;if(void 0!==o.matches)return o.matches(e);if(void 0!==o.msMatchesSelector)return o.msMatchesSelector(e);if(void 0!==o.webkitMatchesSelector)return o.webkitMatchesSelector(e);if(void 0!==o.mozMatchesSelector)return o.mozMatchesSelector(e);throw new Error("Browser lacks native selectors")}},La=t=>1!==t.nodeType&&9!==t.nodeType&&11!==t.nodeType||0===t.childElementCount,re=(t,e)=>t.dom===e.dom,vo=(t,e)=>{const n=t.dom,o=e.dom;return n!==o&&n.contains(o)},Zl=t=>A(t.dom.ownerDocument),yo=t=>Si(t)?t:Zl(t),Pr=t=>A(yo(t).dom.defaultView),Tn=t=>S.from(t.dom.parentNode).map(A),_i=t=>S.from(t.dom.parentElement).map(A),tc=(t,e)=>{const n=Yt(e)?e:de;let o=t.dom;const r=[];for(;null!=o.parentNode;){const a=o.parentNode,s=A(a);if(r.push(s),!0===n(s))break;o=a}return r},Mr=t=>S.from(t.dom.previousSibling).map(A),ea=t=>S.from(t.dom.nextSibling).map(A),ec=t=>Ao(ta(t,Mr)),Ni=t=>ta(t,ea),ze=t=>te(t.dom.childNodes,A),na=(t,e)=>S.from(t.dom.childNodes[e]).map(A),Ri=t=>na(t,0),Ai=t=>na(t,t.dom.childNodes.length-1),oa=t=>t.dom.childNodes.length,Ia=t=>ki(t)&&st(t.dom.host),Ti=Yt(Element.prototype.attachShadow)&&Yt(Node.prototype.getRootNode),nc=ut(Ti),Co=Ti?t=>A(t.dom.getRootNode()):yo,Oi=t=>Ia(t)?t:(e=>{const n=e.dom.head;if(null==n)throw new Error("Head is not available yet");return A(n)})(yo(t)),du=t=>A(t.dom.host),Bi=t=>{if(nc()&&st(t.target)){const e=A(t.target);if(fn(e)&&uu(e)&&t.composed&&t.composedPath){const n=t.composedPath();if(n)return We(n)}}return S.from(t.target)},uu=t=>st(t.dom.shadowRoot),ra=t=>{const e=pn(t)?t.dom.parentNode:t.dom;if(null==e||null===e.ownerDocument)return!1;const n=e.ownerDocument;return(o=>{const r=Co(o);return Ia(r)?S.some(r):S.none()})(A(e)).fold(()=>n.body.contains(e),Aa(ra,du))};var oc=(t,e,n,o,r)=>t(n,o)?S.some(n):Yt(r)&&r(n)?S.none():e(n,o,r);const wo=(t,e,n)=>{let o=t.dom;const r=Yt(n)?n:de;for(;o.parentNode;){o=o.parentNode;const a=A(o);if(e(a))return S.some(a);if(r(a))break}return S.none()},Lr=(t,e,n)=>oc((o,r)=>r(o),wo,t,e,n),Fa=(t,e,n)=>wo(t,o=>hn(o,e),n),Ua=(t,e)=>((n,o)=>{const r=void 0===o?document:o.dom;return La(r)?S.none():S.from(r.querySelector(n)).map(A)})(e,t),er=(t,e,n)=>oc((o,r)=>hn(o,r),Fa,t,e,n),aa=(t,e=!1)=>{return ra(t)?t.dom.isContentEditable:(n=t,er(n,"[contenteditable]")).fold(ut(e),o=>"true"===rc(o));var n},rc=t=>t.dom.contentEditable,ja=t=>void 0!==t.style&&Yt(t.style.getPropertyValue),ac=(t,e,n)=>{if(!Nt(n))throw console.error("Invalid call to CSS.set. Property ",e,":: Value ",n,":: Element ",t),new Error("CSS value must be a string: "+n);ja(t)&&t.style.setProperty(e,n)},sc=(t,e,n)=>{ac(t.dom,e,n)},Ps=(t,e)=>{const n=t.dom;le(e,(o,r)=>{ac(n,r,o)})},xo=(t,e)=>{const n=t.dom,o=window.getComputedStyle(n).getPropertyValue(e);return""!==o||ra(t)?o:ic(n,e)},ic=(t,e)=>ja(t)?t.style.getPropertyValue(e):"",za=(t,e)=>{const o=ic(t.dom,e);return S.from(o).filter(r=>r.length>0)},lc=t=>{const e={},n=t.dom;if(ja(n))for(let o=0;o<n.style.length;o++){const r=n.style.item(o);e[r]=n.style[r]}return e},cc=(t,e)=>{var n,o;o=e,ja(n=t.dom)&&n.style.removeProperty(o),tr(Zn(t,"style").map(Wt),"")&&Le(t,"style")},bn=(t,e)=>{Tn(t).each(n=>{n.dom.insertBefore(e.dom,t.dom)})},to=(t,e)=>{ea(t).fold(()=>{Tn(t).each(n=>{Se(n,e)})},n=>{bn(n,e)})},Ha=(t,e)=>{Ri(t).fold(()=>{Se(t,e)},n=>{t.dom.insertBefore(e.dom,n.dom)})},Se=(t,e)=>{t.dom.appendChild(e.dom)},Ms=(t,e)=>{bn(t,e),Se(e,t)},v=(t,e)=>{J(e,n=>{Se(t,n)})},x=t=>{t.dom.textContent="",J(ze(t),e=>{w(e)})},w=t=>{const e=t.dom;null!==e.parentNode&&e.parentNode.removeChild(e)},_=t=>{const e=ze(t);var n,o;e.length>0&&(n=t,J(o=e,(r,a)=>{to(0===a?n:o[a-1],r)})),w(t)},M=t=>te(t,A),F=t=>t.dom.innerHTML,X=(t,e)=>{const n=Zl(t).dom,o=A(n.createDocumentFragment()),r=((a,s)=>{const i=(s||document).createElement("div");return i.innerHTML=a,ze(A(i))})(e,n);v(o,r),x(t),Se(t,o)},et=(t,e,n,o)=>{t.dom.removeEventListener(e,n,o)},nt=(t,e)=>({left:t,top:e,translate:(n,o)=>nt(t+n,e+o)}),bt=nt,xt=(t,e)=>void 0!==t?t:void 0!==e?e:0,ae=t=>{const e=t.dom,n=e.ownerDocument.body;return n===e?bt(n.offsetLeft,n.offsetTop):ra(t)?(o=>{const r=o.getBoundingClientRect();return bt(r.left,r.top)})(e):bt(0,0)},Pt=t=>{const e=void 0!==t?t.dom:document;return bt(e.body.scrollLeft||e.documentElement.scrollLeft,e.body.scrollTop||e.documentElement.scrollTop)},Gt=(t,e)=>{po().browser.isSafari()&&Yt(t.dom.scrollIntoViewIfNeeded)?t.dom.scrollIntoViewIfNeeded(!1):t.dom.scrollIntoView(e)},ee=(t,e,n,o)=>({x:t,y:e,width:n,height:o,right:t+n,bottom:e+o}),Et=t=>{const e=void 0===t?window:t,o=Pt(A(e.document));return(r=>{const a=void 0===r?window:r;return po().browser.isFirefox()?S.none():S.from(a.visualViewport)})(e).fold(()=>{const r=e.document.documentElement;return ee(o.left,o.top,r.clientWidth,r.clientHeight)},r=>ee(Math.max(r.pageLeft,o.left),Math.max(r.pageTop,o.top),r.width,r.height))},$t=(t,e)=>{let n=[];return J(ze(t),o=>{e(o)&&(n=n.concat([o])),n=n.concat($t(o,e))}),n},Rt=(t,e)=>((n,o)=>{const r=void 0===o?document:o.dom;return La(r)?[]:te(r.querySelectorAll(n),A)})(e,t),Zt=(t,e,n)=>Fa(t,e,n).isSome();class Kt{constructor(e,n){this.node=e,this.rootNode=n,this.current=this.current.bind(this),this.next=this.next.bind(this),this.prev=this.prev.bind(this),this.prev2=this.prev2.bind(this)}current(){return this.node}next(e){return this.node=this.findSibling(this.node,"firstChild","nextSibling",e),this.node}prev(e){return this.node=this.findSibling(this.node,"lastChild","previousSibling",e),this.node}prev2(e){return this.node=this.findPreviousNode(this.node,e),this.node}findSibling(e,n,o,r){if(e){if(!r&&e[n])return e[n];if(e!==this.rootNode){let a=e[o];if(a)return a;for(let s=e.parentNode;s&&s!==this.rootNode;s=s.parentNode)if(a=s[o],a)return a}}}findPreviousNode(e,n){if(e){const o=e.previousSibling;if(this.rootNode&&o===this.rootNode)return;if(o){if(!n)for(let a=o.lastChild;a;a=a.lastChild)if(!a.lastChild)return a;return o}const r=e.parentNode;if(r&&r!==this.rootNode)return r}}}const be=t=>e=>!!e&&e.nodeType===t,He=t=>!!t&&!Object.getPrototypeOf(t),it=be(1),rn=t=>{const e=t.toLowerCase();return n=>st(n)&&n.nodeName.toLowerCase()===e},ke=t=>{const e=t.map(n=>n.toLowerCase());return n=>{if(n&&n.nodeName){const o=n.nodeName.toLowerCase();return Lt(e,o)}return!1}},So=(t,e)=>{const n=e.toLowerCase().split(" ");return o=>{if(it(o)){const r=o.ownerDocument.defaultView;if(r)for(let a=0;a<n.length;a++){const s=r.getComputedStyle(o,null);if((s?s.getPropertyValue(t):null)===n[a])return!0}}return!1}},cp=t=>e=>it(e)&&e.hasAttribute(t),$a=t=>it(t)&&t.hasAttribute("data-mce-bogus"),Ir=t=>it(t)&&"TABLE"===t.tagName,dp=t=>e=>!(!it(e)||e.contentEditable!==t&&e.getAttribute("data-mce-contenteditable")!==t),mu=ke(["textarea","input"]),tt=be(3),zS=be(4),HS=be(7),sa=be(8),Di=be(9),gu=be(11),_e=rn("br"),up=rn("img"),Po=dp("true"),ve=dp("false"),dc=ke(["td","th"]),$S=ke(["td","th","caption"]),nr=ke(["video","audio","object","embed"]),qS=rn("li"),mp=rn("details"),VS=rn("summary"),Ge="\xa0",fu=t=>"\ufeff"===t,pu=(t=>{const n=o=>t(o)?S.from(o.dom.nodeValue):S.none();return{get:o=>{if(!t(o))throw new Error("Can only get text value of a text node");return n(o).getOr("")},getOption:n,set:(o,r)=>{if(!t(o))throw new Error("Can only set raw text value of a text node");o.dom.nodeValue=r}}})(pn),uc=t=>pu.get(t),gp=t=>pu.getOption(t),WS=["pre"].concat(["h1","h2","h3","h4","h5","h6"]),ia=t=>{let e;return n=>(e=e||wr(t,ye),It(e,ge(n)))},eo=ia(["article","aside","details","div","dt","figcaption","footer","form","fieldset","header","hgroup","html","main","nav","section","summary","body","p","dl","multicol","dd","figure","address","center","blockquote","h1","h2","h3","h4","h5","h6","listing","xmp","pre","plaintext","menu","dir","ul","ol","li","hr","table","tbody","thead","tfoot","th","tr","td","caption"]),mc=t=>fn(t)&&!eo(t),qa=t=>fn(t)&&"br"===ge(t),fp=ia(["h1","h2","h3","h4","h5","h6","p","div","address","pre","form","blockquote","center","dir","fieldset","header","footer","article","section","hgroup","aside","nav","figure"]),hu=ia(["ul","ol","dl"]),Pi=ia(["li","dd","dt"]),KS=ia(["thead","tbody","tfoot"]),Mi=ia(["td","th"]),gc=ia(["pre","script","textarea","style"]),GS=ia(WS),YS=t=>GS(t)||mc(t),fc=()=>{const t=Be("br");return Me(t,"data-mce-bogus","1"),t},la=t=>{x(t),Se(t,fc())},XS=t=>{Ai(t).each(e=>{Mr(e).each(n=>{eo(t)&&qa(e)&&eo(n)&&w(e)})})},dn="\ufeff",pc=fu,Fr=t=>t.replace(/\uFEFF/g,""),QS=it,Is=tt,Fs=t=>(Is(t)&&(t=t.parentNode),QS(t)&&t.hasAttribute("data-mce-caret")),ca=t=>Is(t)&&pc(t.data),no=t=>Fs(t)||ca(t),pp=t=>t.firstChild!==t.lastChild||!_e(t.firstChild),bu=t=>{const e=t.container();return!!tt(e)&&(e.data.charAt(t.offset())===dn||t.isAtStart()&&ca(e.previousSibling))},vu=t=>{const e=t.container();return!!tt(e)&&(e.data.charAt(t.offset()-1)===dn||t.isAtEnd()&&ca(e.nextSibling))},hc=t=>Is(t)&&t.data[0]===dn,bc=t=>Is(t)&&t.data[t.data.length-1]===dn,yu=t=>t&&t.hasAttribute("data-mce-caret")?((e=>{var n;const o=e.getElementsByTagName("br"),r=o[o.length-1];$a(r)&&(null===(n=r.parentNode)||void 0===n||n.removeChild(r))})(t),t.removeAttribute("data-mce-caret"),t.removeAttribute("data-mce-bogus"),t.removeAttribute("style"),t.removeAttribute("data-mce-style"),t.removeAttribute("_moz_abspos"),t):null,hp=t=>Fs(t.startContainer),bp=Po,JS=ve,ZS=_e,tk=tt,ek=ke(["script","style","textarea"]),vp=ke(["img","input","textarea","hr","iframe","video","audio","object","embed"]),nk=ke(["table"]),ok=no,ko=t=>!ok(t)&&(tk(t)?!ek(t.parentNode):vp(t)||ZS(t)||nk(t)||Cu(t)),Cu=t=>{return!(it(e=t)&&"true"===e.getAttribute("unselectable"))&&JS(t);var e},yp=(t,e)=>ko(t)&&((n,o)=>{for(let r=n.parentNode;r&&r!==o;r=r.parentNode){if(Cu(r))return!1;if(bp(r))return!0}return!0})(t,e),rk=/^[ \t\r\n]*$/,Va=t=>rk.test(t),ak=t=>{for(const e of t)if(!fu(e))return!1;return!0},Cp=t=>"\n"===t||"\r"===t,wp=(t,e=4,n=!0,o=!0)=>{const r=(l=e)<=0?"":new Array(l+1).join(" "),a=t.replace(/\t/g,r);var l;return Re(a,(i,l)=>{return-1!==" \f\t\v".indexOf(l)||l===Ge?i.pcIsSpace||""===i.str&&n||i.str.length===a.length-1&&o||(d=i.str.length+1)<(c=a).length&&d>=0&&Cp(c[d])?{pcIsSpace:!1,str:i.str+Ge}:{pcIsSpace:!0,str:i.str+" "}:{pcIsSpace:Cp(l),str:i.str+l};var c,d},{pcIsSpace:!1,str:""}).str},vc=(t,e)=>{return ko(t)&&(o=e,!(tt(n=t)&&Va(n.data)&&!((r,a)=>{const s=A(a),i=A(r);return Zt(i,"pre,code",Ct(re,s))})(n,o)))||(n=>it(n)&&"A"===n.nodeName&&!n.hasAttribute("href")&&(n.hasAttribute("name")||n.hasAttribute("id")))(t)||sk(t);var n,o},sk=cp("data-mce-bookmark"),ik=cp("data-mce-bogus"),lk=t=>it(t)&&"all"===t.getAttribute("data-mce-bogus"),De=(t,e=!0)=>((n,o)=>{let r=0;if(vc(n,n))return!1;{let a=n.firstChild;if(!a)return!0;const s=new Kt(a,n);do{if(o){if(lk(a)){a=s.next(!0);continue}if(ik(a)){a=s.next();continue}}if(_e(a))r++,a=s.next();else{if(vc(a,n))return!1;a=s.next()}}while(a);return r<=1}})(t.dom,e),Li="data-mce-block",wu=t=>{return(e=t,Ot(tn(e),n=>!/[A-Z]/.test(n))).join(",");var e},xp=(t,e)=>st(e.querySelector(t))?(e.setAttribute(Li,"true"),"inline-boundary"===e.getAttribute("data-mce-selected")&&e.removeAttribute("data-mce-selected"),!0):(e.removeAttribute(Li),!1),Sp=(t,e)=>{const n=wu(t.getTransparentElements()),o=wu(t.getBlockElements());return Ot(e.querySelectorAll(n),r=>xp(o,r))},kp=(t,e)=>{var n;const o=e?"lastChild":"firstChild";for(let r=t[o];r;r=r[o])if(De(A(r)))return void(null===(n=r.parentNode)||void 0===n||n.removeChild(r))},xu=(t,e)=>{const n=Sp(t,e);var o,r,a;((t,e,n)=>{const o=t.getBlockElements(),r=A(e),a=i=>ge(i)in o,s=i=>re(i,r);J(M(n),i=>{wo(i,a,s).each(l=>{const c=Ot(ze(i),m=>a(m)&&!t.isValidChild(ge(l),ge(m)));if(c.length>0){const d=_i(l);J(c,u=>{wo(u,a,s).each(m=>{((p,g)=>{const h=document.createRange(),f=p.parentNode;if(f){h.setStartBefore(p),h.setEndBefore(g);const b=h.extractContents();kp(b,!0),h.setStartAfter(g),h.setEndAfter(p);const y=h.extractContents();kp(y,!1),De(A(b))||f.insertBefore(b,p),De(A(g))||f.insertBefore(g,p),De(A(y))||f.insertBefore(y,p),f.removeChild(p)}})(m.dom,u.dom)})}),d.each(u=>Sp(t,u.dom))}})})})(t,e,n),o=t,r=e,a=n,J([...a,...Wa(o,r)?[r]:[]],s=>J(Rt(A(s),s.nodeName.toLowerCase()),i=>{uk(o,i.dom)&&_(i)}))},dk=(t,e)=>{if(ku(t,e)){const n=wu(t.getBlockElements());xp(n,e)}},Su=t=>t.hasAttribute(Li),yc=(t,e)=>It(t.getTransparentElements(),e),ku=(t,e)=>it(e)&&yc(t,e.nodeName),Wa=(t,e)=>ku(t,e)&&Su(e),uk=(t,e)=>ku(t,e)&&!Su(e),Ep=(t,e)=>1===e.type&&yc(t,e.name)&&Nt(e.attr(Li)),mk=po().browser,_p=t=>he(t,fn),Np=(t,e)=>t.children&&Lt(t.children,e),Rp=(t,e={})=>{let n=0;const o={},r=A(t),a=yo(r),s=l=>new Promise((c,d)=>{let u;const m=ot._addCacheSuffix(l),p=ce(o,m).getOrThunk(()=>({id:"mce-u"+n++,passed:[],failed:[],count:0}));o[m]=p,p.count++;const g=(C,k)=>{J(C,En),p.status=k,p.passed=[],p.failed=[],u&&(u.onload=null,u.onerror=null,u=null)},h=()=>g(p.passed,2),f=()=>g(p.failed,3);if(c&&p.passed.push(c),d&&p.failed.push(d),1===p.status)return;if(2===p.status)return void h();if(3===p.status)return void f();p.status=1;const b=Be("link",a.dom);var y;Do(b,{rel:"stylesheet",type:"text/css",id:p.id}),e.contentCssCors&&Me(b,"crossOrigin","anonymous"),e.referrerPolicy&&Me(b,"referrerpolicy",e.referrerPolicy),u=b.dom,u.onload=h,u.onerror=f,y=b,Se(Oi(r),y),Me(b,"href",m)}),i=l=>{const c=ot._addCacheSuffix(l);ce(o,c).each(d=>{0==--d.count&&(delete o[c],(u=>{const m=Oi(r);Ua(m,"#"+u).each(w)})(d.id))})};return{load:s,loadAll:l=>Promise.allSettled(te(l,c=>s(c).then(ut(c)))).then(c=>{const d=Ve(c,u=>"fulfilled"===u.status);return d.fail.length>0?Promise.reject(te(d.fail,u=>u.reason)):te(d.pass,u=>u.value)}),unload:i,unloadAll:l=>{J(l,c=>{i(c)})},_setReferrerPolicy:l=>{e.referrerPolicy=l},_setContentCssCors:l=>{e.contentCssCors=l}}},Ap=(()=>{const t=new WeakMap;return{forElement:(e,n)=>{const o=Co(e).dom;return S.from(t.get(o)).getOrThunk(()=>{const r=Rp(o,n);return t.set(o,r),r})}}})(),Tp=(t,e)=>st(t)&&(vc(t,e)||mc(A(t))),Op=t=>"span"===t.nodeName.toLowerCase()&&"bookmark"===t.getAttribute("data-mce-type"),Eu=(t,e,n)=>{var o;const r=n||e;if(it(e)&&Op(e))return e;const a=e.childNodes;for(let s=a.length-1;s>=0;s--)Eu(t,a[s],r);if(it(e)){const s=e.childNodes;1===s.length&&Op(s[0])&&(null===(o=e.parentNode)||void 0===o||o.insertBefore(s[0],e))}return gu(s=e)||Di(s)||vc(e,r)||(s=>!!it(s)&&s.childNodes.length>0)(e)||((s,i)=>tt(s)&&s.data.length>0&&((l,c)=>{const d=new Kt(l,c).prev(!1),u=new Kt(l,c).next(!1),m=dt(d)||Tp(d,c),p=dt(u)||Tp(u,c);return m&&p})(s,i))(e,r)||t.remove(e),e;var s},gk=ot.makeMap,Cc=/[&<>\"\u0060\u007E-\uD7FF\uE000-\uFFEF]|[\uD800-\uDBFF][\uDC00-\uDFFF]/g,wc=/[<>&\u007E-\uD7FF\uE000-\uFFEF]|[\uD800-\uDBFF][\uDC00-\uDFFF]/g,fk=/[<>&\"\']/g,pk=/&#([a-z0-9]+);?|&([a-z0-9]+);/gi,hk={128:"\u20ac",130:"\u201a",131:"\u0192",132:"\u201e",133:"\u2026",134:"\u2020",135:"\u2021",136:"\u02c6",137:"\u2030",138:"\u0160",139:"\u2039",140:"\u0152",142:"\u017d",145:"\u2018",146:"\u2019",147:"\u201c",148:"\u201d",149:"\u2022",150:"\u2013",151:"\u2014",152:"\u02dc",153:"\u2122",154:"\u0161",155:"\u203a",156:"\u0153",158:"\u017e",159:"\u0178"},Ka={'"':"&quot;","'":"&#39;","<":"&lt;",">":"&gt;","&":"&amp;","`":"&#96;"},bk={"&lt;":"<","&gt;":">","&amp;":"&","&quot;":'"',"&apos;":"'"},Bp=(t,e)=>{const n={};if(t){const o=t.split(",");e=e||10;for(let r=0;r<o.length;r+=2){const a=String.fromCharCode(parseInt(o[r],e));if(!Ka[a]){const s="&"+o[r+1]+";";n[a]=s,n[s]=a}}return n}},_u=Bp("50,nbsp,51,iexcl,52,cent,53,pound,54,curren,55,yen,56,brvbar,57,sect,58,uml,59,copy,5a,ordf,5b,laquo,5c,not,5d,shy,5e,reg,5f,macr,5g,deg,5h,plusmn,5i,sup2,5j,sup3,5k,acute,5l,micro,5m,para,5n,middot,5o,cedil,5p,sup1,5q,ordm,5r,raquo,5s,frac14,5t,frac12,5u,frac34,5v,iquest,60,Agrave,61,Aacute,62,Acirc,63,Atilde,64,Auml,65,Aring,66,AElig,67,Ccedil,68,Egrave,69,Eacute,6a,Ecirc,6b,Euml,6c,Igrave,6d,Iacute,6e,Icirc,6f,Iuml,6g,ETH,6h,Ntilde,6i,Ograve,6j,Oacute,6k,Ocirc,6l,Otilde,6m,Ouml,6n,times,6o,Oslash,6p,Ugrave,6q,Uacute,6r,Ucirc,6s,Uuml,6t,Yacute,6u,THORN,6v,szlig,70,agrave,71,aacute,72,acirc,73,atilde,74,auml,75,aring,76,aelig,77,ccedil,78,egrave,79,eacute,7a,ecirc,7b,euml,7c,igrave,7d,iacute,7e,icirc,7f,iuml,7g,eth,7h,ntilde,7i,ograve,7j,oacute,7k,ocirc,7l,otilde,7m,ouml,7n,divide,7o,oslash,7p,ugrave,7q,uacute,7r,ucirc,7s,uuml,7t,yacute,7u,thorn,7v,yuml,ci,fnof,sh,Alpha,si,Beta,sj,Gamma,sk,Delta,sl,Epsilon,sm,Zeta,sn,Eta,so,Theta,sp,Iota,sq,Kappa,sr,Lambda,ss,Mu,st,Nu,su,Xi,sv,Omicron,t0,Pi,t1,Rho,t3,Sigma,t4,Tau,t5,Upsilon,t6,Phi,t7,Chi,t8,Psi,t9,Omega,th,alpha,ti,beta,tj,gamma,tk,delta,tl,epsilon,tm,zeta,tn,eta,to,theta,tp,iota,tq,kappa,tr,lambda,ts,mu,tt,nu,tu,xi,tv,omicron,u0,pi,u1,rho,u2,sigmaf,u3,sigma,u4,tau,u5,upsilon,u6,phi,u7,chi,u8,psi,u9,omega,uh,thetasym,ui,upsih,um,piv,812,bull,816,hellip,81i,prime,81j,Prime,81u,oline,824,frasl,88o,weierp,88h,image,88s,real,892,trade,89l,alefsym,8cg,larr,8ch,uarr,8ci,rarr,8cj,darr,8ck,harr,8dl,crarr,8eg,lArr,8eh,uArr,8ei,rArr,8ej,dArr,8ek,hArr,8g0,forall,8g2,part,8g3,exist,8g5,empty,8g7,nabla,8g8,isin,8g9,notin,8gb,ni,8gf,prod,8gh,sum,8gi,minus,8gn,lowast,8gq,radic,8gt,prop,8gu,infin,8h0,ang,8h7,and,8h8,or,8h9,cap,8ha,cup,8hb,int,8hk,there4,8hs,sim,8i5,cong,8i8,asymp,8j0,ne,8j1,equiv,8j4,le,8j5,ge,8k2,sub,8k3,sup,8k4,nsub,8k6,sube,8k7,supe,8kl,oplus,8kn,otimes,8l5,perp,8m5,sdot,8o8,lceil,8o9,rceil,8oa,lfloor,8ob,rfloor,8p9,lang,8pa,rang,9ea,loz,9j0,spades,9j3,clubs,9j5,hearts,9j6,diams,ai,OElig,aj,oelig,b0,Scaron,b1,scaron,bo,Yuml,m6,circ,ms,tilde,802,ensp,803,emsp,809,thinsp,80c,zwnj,80d,zwj,80e,lrm,80f,rlm,80j,ndash,80k,mdash,80o,lsquo,80p,rsquo,80q,sbquo,80s,ldquo,80t,rdquo,80u,bdquo,810,dagger,811,Dagger,81g,permil,81p,lsaquo,81q,rsaquo,85c,euro",32),Dp=(t,e)=>t.replace(e?Cc:wc,n=>Ka[n]||n),Pp=(t,e)=>t.replace(e?Cc:wc,n=>n.length>1?"&#"+(1024*(n.charCodeAt(0)-55296)+(n.charCodeAt(1)-56320)+65536)+";":Ka[n]||"&#"+n.charCodeAt(0)+";"),Nu=(t,e,n)=>{const o=n||_u;return t.replace(e?Cc:wc,r=>Ka[r]||o[r]||r)},da={encodeRaw:Dp,encodeAllRaw:t=>(""+t).replace(fk,e=>Ka[e]||e),encodeNumeric:Pp,encodeNamed:Nu,getEncodeFunc:(t,e)=>{const n=Bp(e)||_u,o=gk(t.replace(/\+/g,","));return o.named&&o.numeric?(r,a)=>r.replace(a?Cc:wc,s=>void 0!==Ka[s]?Ka[s]:void 0!==n[s]?n[s]:s.length>1?"&#"+(1024*(s.charCodeAt(0)-55296)+(s.charCodeAt(1)-56320)+65536)+";":"&#"+s.charCodeAt(0)+";"):o.named?e?(r,a)=>Nu(r,a,n):Nu:o.numeric?Pp:Dp},decode:t=>t.replace(pk,(e,n)=>n?(n="x"===n.charAt(0).toLowerCase()?parseInt(n.substr(1),16):parseInt(n,10))>65535?(n-=65536,String.fromCharCode(55296+(n>>10),56320+(1023&n))):hk[n]||String.fromCharCode(n):bk[e]||_u[e]||(o=>{const r=Be("div").dom;return r.innerHTML=o,r.textContent||r.innerText||o})(e))},xc={},Sc={},vk={},Us=ot.makeMap,un=ot.each,Ru=ot.extend,Mp=ot.explode,yk=ot.inArray,an=(t,e)=>(t=ot.trim(t))?t.split(e||" "):[],Lp=(t,e={})=>{const n=Us(t," ",Us(t.toUpperCase()," "));return Ru(n,e)},Ip=t=>Lp("td th li dt dd figcaption caption details summary",t.getTextBlockElements()),Au=(t,e)=>{if(t){const n={};return Nt(t)&&(t={"*":t}),un(t,(o,r)=>{n[r]=n[r.toUpperCase()]="map"===e?Us(o,/[, ]/):Mp(o,/[, ]/)}),n}},ua=(t={})=>{var e;const n={},o={};let r=[];const a={},s={},i=(K,at,St)=>{const vt=t[K];if(vt)return Us(vt,/[, ]/,Us(vt.toUpperCase(),/[, ]/));{let Dt=Sc[K];return Dt||(Dt=Lp(at,St),Sc[K]=Dt),Dt}},l=null!==(e=t.schema)&&void 0!==e?e:"html5",c=(K=>{const at={};let St,vt,Dt,Mt;const gt=(Pe,xe="",Ln="")=>{const In=an(Ln),zo=an(Pe);let No=zo.length;for(;No--;){const en=an([St,xe].join(" "));at[zo[No]]={attributes:wr(en,()=>({})),attributesOrder:en,children:wr(In,ut(vk))}}},Vt=(Pe,xe)=>{const Ln=an(Pe),In=an(xe);let zo=Ln.length;for(;zo--;){const No=at[Ln[zo]];for(let en=0,zn=In.length;en<zn;en++)No.attributes[In[en]]={},No.attributesOrder.push(In[en])}};if(xc[K])return xc[K];if(St="id accesskey class dir lang style tabindex title role",vt="address blockquote div dl fieldset form h1 h2 h3 h4 h5 h6 hr menu ol p pre table ul",Dt="a abbr b bdo br button cite code del dfn em embed i iframe img input ins kbd label map noscript object q s samp script select small span strong sub sup textarea u var #text #comment","html4"!==K&&(St+=" contenteditable contextmenu draggable dropzone hidden spellcheck translate",vt+=" article aside details dialog figure main header footer hgroup section nav a ins del canvas map",Dt+=" audio canvas command datalist mark meter output picture progress time wbr video ruby bdi keygen"),"html5-strict"!==K){St+=" xml:lang";const Pe="acronym applet basefont big font strike tt";Dt=[Dt,Pe].join(" "),un(an(Pe),Ln=>{gt(Ln,"",Dt)});const xe="center dir isindex noframes";vt=[vt,xe].join(" "),Mt=[vt,Dt].join(" "),un(an(xe),Ln=>{gt(Ln,"",Mt)})}return Mt=Mt||[vt,Dt].join(" "),gt("html","manifest","head body"),gt("head","","base command link meta noscript script style title"),gt("title hr noscript br"),gt("base","href target"),gt("link","href rel media hreflang type sizes hreflang"),gt("meta","name http-equiv content charset"),gt("style","media type scoped"),gt("script","src async defer type charset"),gt("body","onafterprint onbeforeprint onbeforeunload onblur onerror onfocus onhashchange onload onmessage onoffline ononline onpagehide onpageshow onpopstate onresize onscroll onstorage onunload",Mt),gt("dd div","",Mt),gt("address dt caption","","html4"===K?Dt:Mt),gt("h1 h2 h3 h4 h5 h6 pre p abbr code var samp kbd sub sup i b u bdo span legend em strong small s cite dfn","",Dt),gt("blockquote","cite",Mt),gt("ol","reversed start type","li"),gt("ul","","li"),gt("li","value",Mt),gt("dl","","dt dd"),gt("a","href target rel media hreflang type","html4"===K?Dt:Mt),gt("q","cite",Dt),gt("ins del","cite datetime",Mt),gt("img","src sizes srcset alt usemap ismap width height"),gt("iframe","src name width height",Mt),gt("embed","src type width height"),gt("object","data type typemustmatch name usemap form width height",[Mt,"param"].join(" ")),gt("param","name value"),gt("map","name",[Mt,"area"].join(" ")),gt("area","alt coords shape href target rel media hreflang type"),gt("table","border","caption colgroup thead tfoot tbody tr"+("html4"===K?" col":"")),gt("colgroup","span","col"),gt("col","span"),gt("tbody thead tfoot","","tr"),gt("tr","","td th"),gt("td","colspan rowspan headers",Mt),gt("th","colspan rowspan headers scope abbr",Mt),gt("form","accept-charset action autocomplete enctype method name novalidate target",Mt),gt("fieldset","disabled form name",[Mt,"legend"].join(" ")),gt("label","form for",Dt),gt("input","accept alt autocomplete checked dirname disabled form formaction formenctype formmethod formnovalidate formtarget height list max maxlength min multiple name pattern readonly required size src step type value width"),gt("button","disabled form formaction formenctype formmethod formnovalidate formtarget name type value","html4"===K?Mt:Dt),gt("select","disabled form multiple name required size","option optgroup"),gt("optgroup","disabled label","option"),gt("option","disabled label selected value"),gt("textarea","cols dirname disabled form maxlength name readonly required rows wrap"),gt("menu","type label",[Mt,"li"].join(" ")),gt("noscript","",Mt),"html4"!==K&&(gt("wbr"),gt("ruby","",[Dt,"rt rp"].join(" ")),gt("figcaption","",Mt),gt("mark rt rp summary bdi","",Dt),gt("canvas","width height",Mt),gt("video","src crossorigin poster preload autoplay mediagroup loop muted controls width height buffered",[Mt,"track source"].join(" ")),gt("audio","src crossorigin preload autoplay mediagroup loop muted controls buffered volume",[Mt,"track source"].join(" ")),gt("picture","","img source"),gt("source","src srcset type media sizes"),gt("track","kind src srclang label default"),gt("datalist","",[Dt,"option"].join(" ")),gt("article section nav aside main header footer","",Mt),gt("hgroup","","h1 h2 h3 h4 h5 h6"),gt("figure","",[Mt,"figcaption"].join(" ")),gt("time","datetime",Dt),gt("dialog","open",Mt),gt("command","type label icon disabled checked radiogroup command"),gt("output","for form name",Dt),gt("progress","value max",Dt),gt("meter","value min max low high optimum",Dt),gt("details","open",[Mt,"summary"].join(" ")),gt("keygen","autofocus challenge disabled form keytype name")),"html5-strict"!==K&&(Vt("script","language xml:space"),Vt("style","xml:space"),Vt("object","declare classid code codebase codetype archive standby align border hspace vspace"),Vt("embed","align name hspace vspace"),Vt("param","valuetype type"),Vt("a","charset name rev shape coords"),Vt("br","clear"),Vt("applet","codebase archive code object alt name width height align hspace vspace"),Vt("img","name longdesc align border hspace vspace"),Vt("iframe","longdesc frameborder marginwidth marginheight scrolling align"),Vt("font basefont","size color face"),Vt("input","usemap align"),Vt("select"),Vt("textarea"),Vt("h1 h2 h3 h4 h5 h6 div p legend caption","align"),Vt("ul","type compact"),Vt("li","type"),Vt("ol dl menu dir","compact"),Vt("pre","width xml:space"),Vt("hr","align noshade size width"),Vt("isindex","prompt"),Vt("table","summary width frame rules cellspacing cellpadding align bgcolor"),Vt("col","width align char charoff valign"),Vt("colgroup","width align char charoff valign"),Vt("thead","align char charoff valign"),Vt("tr","align char charoff valign bgcolor"),Vt("th","axis align char charoff valign nowrap bgcolor width height"),Vt("form","accept"),Vt("td","abbr axis scope align char charoff valign nowrap bgcolor width height"),Vt("tfoot","align char charoff valign"),Vt("tbody","align char charoff valign"),Vt("area","nohref"),Vt("body","background bgcolor text link vlink alink")),"html4"!==K&&(Vt("input button select textarea","autofocus"),Vt("input textarea","placeholder"),Vt("a","download"),Vt("link script img","crossorigin"),Vt("img","loading"),Vt("iframe","sandbox seamless allow allowfullscreen loading")),"html4"!==K&&J([at.video,at.audio],Pe=>{delete Pe.children.audio,delete Pe.children.video}),un(an("a form meter progress dfn"),Pe=>{at[Pe]&&delete at[Pe].children[Pe]}),delete at.caption.children.table,delete at.script,xc[K]=at,at})(l);!1===t.verify_html&&(t.valid_elements="*[*]");const d=Au(t.valid_styles),u=Au(t.invalid_styles,"map"),m=Au(t.valid_classes,"map"),p=i("whitespace_elements","pre script noscript style textarea video audio iframe object code"),g=i("self_closing_elements","colgroup dd dt li option p td tfoot th thead tr"),h=i("void_elements","area base basefont br col frame hr img input isindex link meta param embed source wbr track"),f=i("boolean_attributes","checked compact declare defer disabled ismap multiple nohref noresize noshade nowrap readonly selected autoplay loop controls allowfullscreen"),b="td th iframe video audio object script code",y=i("non_empty_elements",b+" pre",h),C=i("move_caret_before_on_enter_elements",b+" table",h),k=i("text_block_elements","h1 h2 h3 h4 h5 h6 p div address pre form blockquote center dir fieldset header footer article section hgroup aside main nav figure"),E=i("block_elements","hr table tbody thead tfoot th tr td li ol ul caption dl dt dd noscript menu isindex option datalist select optgroup figcaption details summary",k),N=i("text_inline_elements","span strong b em i font s strike u var cite dfn code mark q sup sub samp"),R=i("transparent_elements","a ins del canvas map");un("script noscript iframe noframes noembed title style textarea xmp plaintext".split(" "),K=>{s[K]=new RegExp("</"+K+"[^>]*>","gi")});const I=K=>new RegExp("^"+K.replace(/([?+*])/g,".$1")+"$"),q=K=>{const at=/^([#+\-])?([^\[!\/]+)(?:\/([^\[!]+))?(?:(!?)\[([^\]]+)])?$/,St=/^([!\-])?(\w+[\\:]:\w+|[^=~<]+)?(?:([=~<])(.*))?$/,vt=/[*?+]/;if(K){const Dt=an(K,",");let Mt,gt;n["@"]&&(Mt=n["@"].attributes,gt=n["@"].attributesOrder);for(let Vt=0,Pe=Dt.length;Vt<Pe;Vt++){let xe=at.exec(Dt[Vt]);if(xe){const Ln=xe[1],In=xe[2],zo=xe[3],No=xe[5],en={},zn=[],Ne={attributes:en,attributesOrder:zn};if("#"===Ln&&(Ne.paddEmpty=!0),"-"===Ln&&(Ne.removeEmpty=!0),"!"===xe[4]&&(Ne.removeEmptyAttrs=!0),Mt&&(le(Mt,(fr,Ea)=>{en[Ea]=fr}),gt&&zn.push(...gt)),No){const fr=an(No,"|");for(let Ea=0,ou=fr.length;Ea<ou;Ea++)if(xe=St.exec(fr[Ea]),xe){const pr={},Fl=xe[1],qn=xe[2].replace(/[\\:]:/g,":"),_a=xe[3],Vn=xe[4];if("!"===Fl&&(Ne.attributesRequired=Ne.attributesRequired||[],Ne.attributesRequired.push(qn),pr.required=!0),"-"===Fl){delete en[qn],zn.splice(yk(zn,qn),1);continue}if(_a&&("="===_a&&(Ne.attributesDefault=Ne.attributesDefault||[],Ne.attributesDefault.push({name:qn,value:Vn}),pr.defaultValue=Vn),"~"===_a&&(Ne.attributesForced=Ne.attributesForced||[],Ne.attributesForced.push({name:qn,value:Vn}),pr.forcedValue=Vn),"<"===_a&&(pr.validValues=Us(Vn,"?"))),vt.test(qn)){const Cs=pr;Ne.attributePatterns=Ne.attributePatterns||[],Cs.pattern=I(qn),Ne.attributePatterns.push(Cs)}else en[qn]||zn.push(qn),en[qn]=pr}}if(Mt||"@"!==In||(Mt=en,gt=zn),zo&&(Ne.outputName=In,n[zo]=Ne),vt.test(In)){const fr=Ne;fr.pattern=I(In),r.push(fr)}else n[In]=Ne}}}},H=K=>{r=[],J(tn(n),at=>{delete n[at]}),q(K),un(c,(at,St)=>{o[St]=at.children})},T=K=>{const at=/^(~)?(.+)$/;K&&(delete Sc.text_block_elements,delete Sc.block_elements,un(an(K,","),St=>{const vt=at.exec(St);if(vt){const Dt="~"===vt[1],Mt=Dt?"span":"div",gt=vt[2];if(o[gt]=o[Mt],a[gt]=Mt,y[gt.toUpperCase()]={},y[gt]={},Dt||(E[gt.toUpperCase()]={},E[gt]={}),!n[gt]){let Vt=n[Mt];Vt=Ru({},Vt),delete Vt.removeEmptyAttrs,delete Vt.removeEmpty,n[gt]=Vt}un(o,(Vt,Pe)=>{Vt[Mt]&&(o[Pe]=Vt=Ru({},o[Pe]),Vt[gt]=Vt[Mt])})}}))},P=K=>{const at=/^([+\-]?)([A-Za-z0-9_\-.\u00b7\u00c0-\u00d6\u00d8-\u00f6\u00f8-\u037d\u037f-\u1fff\u200c-\u200d\u203f-\u2040\u2070-\u218f\u2c00-\u2fef\u3001-\ud7ff\uf900-\ufdcf\ufdf0-\ufffd]+)\[([^\]]+)]$/;delete xc[l],K&&un(an(K,","),St=>{const vt=at.exec(St);if(vt){const Dt=vt[1];let Mt;Mt=Dt?o[vt[2]]:o[vt[2]]={"#comment":{}},Mt=o[vt[2]],un(an(vt[3],"|"),gt=>{"-"===Dt?delete Mt[gt]:Mt[gt]={}})}})},B=K=>{const at=n[K];if(at)return at;let St=r.length;for(;St--;){const vt=r[St];if(vt.pattern.test(K))return vt}};t.valid_elements?H(t.valid_elements):(un(c,(K,at)=>{n[at]={attributes:K.attributes,attributesOrder:K.attributesOrder},o[at]=K.children}),un(an("strong/b em/i"),K=>{const at=an(K,"/");n[at[1]].outputName=at[0]}),un(N,(K,at)=>{n[at]&&(t.padd_empty_block_inline_children&&(n[at].paddInEmptyBlock=!0),n[at].removeEmpty=!0)}),un(an("ol ul blockquote a table tbody"),K=>{n[K]&&(n[K].removeEmpty=!0)}),un(an("p h1 h2 h3 h4 h5 h6 th td pre div address caption li summary"),K=>{n[K]&&(n[K].paddEmpty=!0)}),un(an("span"),K=>{n[K].removeEmptyAttrs=!0})),T(t.custom_elements),P(t.valid_children),q(t.extended_valid_elements),P("+ol[ul|ol],+ul[ul|ol]"),un({dd:"dl",dt:"dl",li:"ul ol",td:"tr",th:"tr",tr:"tbody thead tfoot",tbody:"table",thead:"table",tfoot:"table",legend:"fieldset",area:"map",param:"video audio object"},(K,at)=>{n[at]&&(n[at].parentsRequired=an(K))}),t.invalid_elements&&un(Mp(t.invalid_elements),K=>{n[K]&&delete n[K]}),B("span")||q("span[!data-mce-type|*]");const j=ut(d),lt=ut(u),Z=ut(m),_t=ut(f),mt=ut(E),Ut=ut(k),Ht=ut(N),ft=ut(Object.seal(h)),kt=ut(g),Bt=ut(y),O=ut(C),D=ut(p),V=ut(R),Y=ut(Object.seal(s)),W=ut(a);return{type:l,children:o,elements:n,getValidStyles:j,getValidClasses:Z,getBlockElements:mt,getInvalidStyles:lt,getVoidElements:ft,getTextBlockElements:Ut,getTextInlineElements:Ht,getBoolAttrs:_t,getElementRule:B,getSelfClosingElements:kt,getNonEmptyElements:Bt,getMoveCaretBeforeOnEnterElements:O,getWhitespaceElements:D,getTransparentElements:V,getSpecialElements:Y,isValidChild:(K,at)=>{const St=o[K.toLowerCase()];return!(!St||!St[at.toLowerCase()])},isValid:(K,at)=>{const St=B(K);if(St){if(!at)return!0;{if(St.attributes[at])return!0;const vt=St.attributePatterns;if(vt){let Dt=vt.length;for(;Dt--;)if(vt[Dt].pattern.test(at))return!0}}}return!1},getCustomElements:W,addValidElements:q,setValidElements:H,addCustomElements:T,addValidChildren:P}},Tu=(t={},e)=>{const n=/(?:url(?:(?:\(\s*\"([^\"]+)\"\s*\))|(?:\(\s*\'([^\']+)\'\s*\))|(?:\(\s*([^)\s]+)\s*\))))|(?:\'([^\']+)\')|(?:\"([^\"]+)\")/gi,o=/\s*([^:]+):\s*([^;]+);?/g,r=/\s+$/,a={};let s,i;e&&(s=e.getValidStyles(),i=e.getInvalidStyles());const c="\\\" \\' \\; \\: ; : \ufeff".split(" ");for(let u=0;u<c.length;u++)a[c[u]]="\ufeff"+u,a["\ufeff"+u]=c[u];const d={parse:u=>{const m={};let p=!1;const g=t.url_converter,h=t.url_converter_scope||d,f=(H,T,P)=>{const B=m[H+"-top"+T];if(!B)return;const j=m[H+"-right"+T];if(!j)return;const lt=m[H+"-bottom"+T];if(!lt)return;const Z=m[H+"-left"+T];if(!Z)return;const _t=[B,j,lt,Z];let mt=_t.length-1;for(;mt--&&_t[mt]===_t[mt+1];);mt>-1&&P||(m[H+T]=-1===mt?_t[0]:_t.join(" "),delete m[H+"-top"+T],delete m[H+"-right"+T],delete m[H+"-bottom"+T],delete m[H+"-left"+T])},b=H=>{const T=m[H];if(!T)return;const P=T.split(" ");let B=P.length;for(;B--;)if(P[B]!==P[0])return!1;return m[H]=P[0],!0},y=H=>(p=!0,a[H]),C=(H,T)=>(p&&(H=H.replace(/\uFEFF[0-9]/g,P=>a[P])),T||(H=H.replace(/\\([\'\";:])/g,"$1")),H),k=H=>String.fromCharCode(parseInt(H.slice(1),16)),E=H=>H.replace(/\\[0-9a-f]+/gi,k),N=(H,T,P,B,j,lt)=>{if(j=j||lt)return"'"+(j=C(j)).replace(/\'/g,"\\'")+"'";if(T=C(T||P||B||""),!t.allow_script_urls){const Z=T.replace(/[\s\r\n]+/g,"");if(/(java|vb)script:/i.test(Z)||!t.allow_svg_data_urls&&/^data:image\/svg/i.test(Z))return""}return g&&(T=g.call(h,T,"style")),"url('"+T.replace(/\'/g,"\\'")+"')"};if(u){let H;for(u=(u=u.replace(/[\u0000-\u001F]/g,"")).replace(/\\[\"\';:\uFEFF]/g,y).replace(/\"[^\"]+\"|\'[^\']+\'/g,T=>T.replace(/[;:]/g,y));H=o.exec(u);){o.lastIndex=H.index+H[0].length;let T=H[1].replace(r,"").toLowerCase(),P=H[2].replace(r,"");if(T&&P){if(T=E(T),P=E(P),-1!==T.indexOf("\ufeff")||-1!==T.indexOf('"')||!t.allow_script_urls&&("behavior"===T||/expression\s*\(|\/\*|\*\//.test(P)))continue;"font-weight"===T&&"700"===P?P="bold":"color"!==T&&"background-color"!==T||(P=P.toLowerCase()),P=P.replace(n,N),m[T]=p?C(P,!0):P}}f("border","",!0),f("border","-width"),f("border","-color"),f("border","-style"),f("padding",""),f("margin",""),I="border-style",q="border-color",b(R="border-width")&&b(I)&&b(q)&&(m.border=m[R]+" "+m[I]+" "+m[q],delete m[R],delete m[I],delete m[q]),"medium none"===m.border&&delete m.border,"none"===m["border-image"]&&delete m["border-image"]}var R,I,q;return m},serialize:(u,m)=>{let p="";const g=(h,f)=>{const b=f[h];if(b)for(let y=0,C=b.length;y<C;y++){const k=b[y],E=u[k];E&&(p+=(p.length>0?" ":"")+k+": "+E+";")}};return m&&s?(g("*",s),g(m,s)):le(u,(h,f)=>{h&&((b,y)=>{if(!i||!y)return!0;let C=i["*"];return!(C&&C[b]||(C=i[y],C&&C[b]))})(f,m)&&(p+=(p.length>0?" ":"")+f+": "+h+";")}),p}};return d},Ck={keyLocation:!0,layerX:!0,layerY:!0,returnValue:!0,webkitMovementX:!0,webkitMovementY:!0,keyIdentifier:!0,mozPressure:!0},Fp=(t,e)=>{const n=e??{};for(const o in t)It(Ck,o)||(n[o]=t[o]);return st(t.composedPath)&&(n.composedPath=()=>t.composedPath()),n},Ou=(t,e,n,o)=>{var r;const a=Fp(e,o);return a.type=t,pe(a.target)&&(a.target=null!==(r=a.srcElement)&&void 0!==r?r:n),(pe((s=e).preventDefault)||(i=s)instanceof Event||Yt(i.initEvent))&&(a.preventDefault=()=>{a.defaultPrevented=!0,a.isDefaultPrevented=ye,Yt(e.preventDefault)&&e.preventDefault()},a.stopPropagation=()=>{a.cancelBubble=!0,a.isPropagationStopped=ye,Yt(e.stopPropagation)&&e.stopPropagation()},a.stopImmediatePropagation=()=>{a.isImmediatePropagationStopped=ye,a.stopPropagation()},(s=>s.isDefaultPrevented===ye||s.isDefaultPrevented===de)(a)||(a.isDefaultPrevented=!0===a.defaultPrevented?ye:de,a.isPropagationStopped=!0===a.cancelBubble?ye:de,a.isImmediatePropagationStopped=de)),a;var s,i},wk=/^(?:mouse|contextmenu)|click/,Bu=(t,e,n,o)=>{t.addEventListener(e,n,o||!1)},kc=(t,e,n,o)=>{t.removeEventListener(e,n,o||!1)},Ii=(t,e)=>{const n=Ou(t.type,t,document,e);if(st(o=t)&&wk.test(o.type)&&dt(t.pageX)&&!dt(t.clientX)){const o=n.target.ownerDocument||document,r=o.documentElement,a=o.body,s=n;s.pageX=t.clientX+(r&&r.scrollLeft||a&&a.scrollLeft||0)-(r&&r.clientLeft||a&&a.clientLeft||0),s.pageY=t.clientY+(r&&r.scrollTop||a&&a.scrollTop||0)-(r&&r.clientTop||a&&a.clientTop||0)}var o;return n},xk=(t,e,n)=>{const o=t.document,r={type:"ready"};if(n.domLoaded)return void e(r);const a=()=>{kc(t,"DOMContentLoaded",a),kc(t,"load",a),n.domLoaded||(n.domLoaded=!0,e(r)),t=null};"complete"===o.readyState||"interactive"===o.readyState&&o.body?a():Bu(t,"DOMContentLoaded",a),n.domLoaded||Bu(t,"load",a)};class Ga{constructor(){this.domLoaded=!1,this.events={},this.count=1,this.expando="mce-data-"+(+new Date).toString(32),this.hasFocusIn="onfocusin"in document.documentElement,this.count=1}bind(e,n,o,r){const a=this;let s;const i=window,l=m=>{a.executeHandlers(Ii(m||i.event),c)};if(!e||tt(e)||sa(e))return o;let c;e[a.expando]?c=e[a.expando]:(c=a.count++,e[a.expando]=c,a.events[c]={}),r=r||e;const d=n.split(" ");let u=d.length;for(;u--;){let m=d[u],p=l,g=!1,h=!1;"DOMContentLoaded"===m&&(m="ready"),a.domLoaded&&"ready"===m&&"complete"===e.readyState?o.call(r,Ii({type:m})):(a.hasFocusIn||"focusin"!==m&&"focusout"!==m||(g=!0,h="focusin"===m?"focus":"blur",p=f=>{const b=Ii(f||i.event);b.type="focus"===b.type?"focusin":"focusout",a.executeHandlers(b,c)}),s=a.events[c][m],s?"ready"===m&&a.domLoaded?o(Ii({type:m})):s.push({func:o,scope:r}):(a.events[c][m]=s=[{func:o,scope:r}],s.fakeName=h,s.capture=g,s.nativeHandler=p,"ready"===m?xk(e,p,a):Bu(e,h||m,p,g)))}return e=s=null,o}unbind(e,n,o){if(!e||tt(e)||sa(e))return this;const r=e[this.expando];if(r){let a=this.events[r];if(n){const s=n.split(" ");let i=s.length;for(;i--;){const l=s[i],c=a[l];if(c){if(o){let d=c.length;for(;d--;)if(c[d].func===o){const u=c.nativeHandler,m=c.fakeName,p=c.capture,g=c.slice(0,d).concat(c.slice(d+1));g.nativeHandler=u,g.fakeName=m,g.capture=p,a[l]=g}}o&&0!==c.length||(delete a[l],kc(e,c.fakeName||l,c.nativeHandler,c.capture))}}}else le(a,(s,i)=>{kc(e,s.fakeName||i,s.nativeHandler,s.capture)}),a={};for(const s in a)if(It(a,s))return this;delete this.events[r];try{delete e[this.expando]}catch{e[this.expando]=null}}return this}fire(e,n,o){return this.dispatch(e,n,o)}dispatch(e,n,o){if(!e||tt(e)||sa(e))return this;const r=Ii({type:n,target:e},o);do{const a=e[this.expando];a&&this.executeHandlers(r,a),e=e.parentNode||e.ownerDocument||e.defaultView||e.parentWindow}while(e&&!r.isPropagationStopped());return this}clean(e){if(!e||tt(e)||sa(e))return this;if(e[this.expando]&&this.unbind(e),e.getElementsByTagName||(e=e.document),e&&e.getElementsByTagName){this.unbind(e);const n=e.getElementsByTagName("*");let o=n.length;for(;o--;)(e=n[o])[this.expando]&&this.unbind(e)}return this}destroy(){this.events={}}cancel(e){return e&&(e.preventDefault(),e.stopImmediatePropagation()),!1}executeHandlers(e,n){const o=this.events[n],r=o&&o[e.type];if(r)for(let a=0,s=r.length;a<s;a++){const i=r[a];if(i&&!1===i.func.call(i.scope,e)&&e.preventDefault(),e.isImmediatePropagationStopped())return}}}Ga.Event=new Ga;const Du=ot.each,Sk=ot.grep,Pu="data-mce-style",kk=ot.makeMap("fill-opacity font-weight line-height opacity orphans widows z-index zoom"," "),Fi=(t,e,n)=>{pe(n)||""===n?Le(t,e):Me(t,e,n)},Up=t=>t.replace(/[A-Z]/g,e=>"-"+e.toLowerCase()),js=(t,e)=>{let n=0;if(t)for(let o=t.nodeType,r=t.previousSibling;r;r=r.previousSibling){const a=r.nodeType;(!e||!tt(r)||a!==o&&r.data.length)&&(n++,o=a)}return n},jp=(t,e)=>{const n=An(e,"style"),o=t.serialize(t.parse(n),ge(e));Fi(e,Pu,o)},zp=(t,e,n)=>{const o=Up(e);var r,a;pe(n)||""===n?cc(t,o):sc(t,o,(a=o,nn(r=n)?It(kk,a)?r+"":r+"px":r))},me=(t,e={})=>{const n={},o=window,r={};let a=0;const s=Ap.forElement(A(t),{contentCssCors:e.contentCssCors,referrerPolicy:e.referrerPolicy}),i=[],l=e.schema?e.schema:ua({}),c=Tu({url_converter:e.url_converter,url_converter_scope:e.url_converter_scope},e.schema),d=e.ownEvents?new Ga:Ga.Event,u=l.getBlockElements(),m=O=>O&&t&&Nt(O)?t.getElementById(O):O,p=O=>{const D=m(O);return st(D)?A(D):null},g=(O,D,V="")=>{let Y;const W=p(O);if(st(W)&&fn(W)){const K=Bt[D];Y=K&&K.get?K.get(W.dom,D):An(W,D)}return st(Y)?Y:V},h=O=>{const D=m(O);return pe(D)?[]:D.attributes},f=(O,D,V)=>{I(O,Y=>{if(it(Y)){const W=A(Y),K=""===V?null:V,at=An(W,D),St=Bt[D];St&&St.set?St.set(W.dom,K,D):Fi(W,D,K),at!==K&&e.onSetAttrib&&e.onSetAttrib({attrElm:W.dom,attrName:D,attrValue:K})}})},b=()=>e.root_element||t.body,y=(O,D)=>((V,Y,W)=>{let K=0,at=0;const St=V.ownerDocument;if(W=W||V,Y){if(W===V&&Y.getBoundingClientRect&&"static"===xo(A(V),"position")){const Dt=Y.getBoundingClientRect();return K=Dt.left+(St.documentElement.scrollLeft||V.scrollLeft)-St.documentElement.clientLeft,at=Dt.top+(St.documentElement.scrollTop||V.scrollTop)-St.documentElement.clientTop,{x:K,y:at}}let vt=Y;for(;vt&&vt!==W&&vt.nodeType&&!Np(vt,W);)K+=vt.offsetLeft||0,at+=vt.offsetTop||0,vt=vt.offsetParent;for(vt=Y.parentNode;vt&&vt!==W&&vt.nodeType&&!Np(vt,W);)K-=vt.scrollLeft||0,at-=vt.scrollTop||0,vt=vt.parentNode;at+=(Dt=A(Y),mk.isFirefox()&&"table"===ge(Dt)?_p(ze(Dt)).filter(Mt=>"caption"===ge(Mt)).bind(Mt=>_p(Ni(Mt)).map(gt=>gt.dom.offsetTop<=Mt.dom.offsetTop?-Mt.dom.offsetHeight:0)).getOr(0):0)}var Dt;return{x:K,y:at}})(t.body,m(O),D),C=(O,D,V)=>{const Y=m(O);if(!pe(Y)&&it(Y))return V?xo(A(Y),Up(D)):("float"===(D=D.replace(/-(\D)/g,(W,K)=>K.toUpperCase()))&&(D="cssFloat"),Y.style?Y.style[D]:void 0)},k=O=>{const D=m(O);if(!D)return{w:0,h:0};let V=C(D,"width"),Y=C(D,"height");return V&&-1!==V.indexOf("px")||(V="0"),Y&&-1!==Y.indexOf("px")||(Y="0"),{w:parseInt(V,10)||D.offsetWidth||D.clientWidth,h:parseInt(Y,10)||D.offsetHeight||D.clientHeight}},E=(O,D)=>{if(!O)return!1;const V=Ee(O)?O:[O];return ie(V,Y=>hn(A(Y),D))},N=(O,D,V,Y)=>{const W=[];let K=m(O);Y=void 0===Y;const at=V||("BODY"!==b().nodeName?b().parentNode:null);if(Nt(D))if("*"===D)D=it;else{const St=D;D=vt=>E(vt,St)}for(;K&&!(K===at||pe(K.nodeType)||Di(K)||gu(K));){if(!D||D(K)){if(!Y)return[K];W.push(K)}K=K.parentNode}return Y?W:null},R=(O,D,V)=>{let Y=D;if(O){Nt(D)&&(Y=W=>E(W,D));for(let W=O[V];W;W=W[V])if(Yt(Y)&&Y(W))return W}return null},I=function(O,D,V){const Y=V??this;if(Ee(O)){const W=[];return Du(O,(K,at)=>{const St=m(K);St&&W.push(D.call(Y,St,at))}),W}{const W=m(O);return!!W&&D.call(Y,W)}},q=(O,D)=>{I(O,V=>{le(D,(Y,W)=>{f(V,W,Y)})})},H=(O,D)=>{I(O,V=>{const Y=A(V);X(Y,D)})},T=(O,D,V,Y,W)=>I(O,K=>{const at=Nt(D)?t.createElement(D):D;return st(V)&&q(at,V),Y&&(!Nt(Y)&&Y.nodeType?at.appendChild(Y):Nt(Y)&&H(at,Y)),W?at:K.appendChild(at)}),P=(O,D,V)=>T(t.createElement(O),O,D,V,!0),B=da.encodeAllRaw,j=(O,D)=>I(O,V=>{const Y=A(V);return D&&J(ze(Y),W=>{pn(W)&&0===W.dom.length?w(W):bn(Y,W)}),w(Y),Y.dom}),lt=(O,D,V)=>{I(O,Y=>{if(it(Y)){const W=A(Y),K=D.split(" ");J(K,at=>{var St,vt,Mt,gt;st(V)?(V?Jr:Br)(W,at):(vt=at,Qr(St=W)?St.dom.classList.toggle(vt):(gt=vt,Lt(Ts(Mt=St),gt)?Jl(Mt,gt):Os(Mt,gt)),Bs(St))})}})},Z=(O,D,V)=>I(D,Y=>{var W;const K=Ee(D)?O.cloneNode(!0):O;return V&&Du(Sk(Y.childNodes),at=>{K.appendChild(at)}),null===(W=Y.parentNode)||void 0===W||W.replaceChild(K,Y),Y}),_t=O=>{if(it(O)){const D="a"===O.nodeName.toLowerCase()&&!g(O,"href")&&g(O,"id");if(g(O,"name")||g(O,"data-mce-bookmark")||D)return!0}return!1},mt=()=>t.createRange(),Ut=(O,D,V,Y)=>{if(Ee(O)){let W=O.length;const K=[];for(;W--;)K[W]=Ut(O[W],D,V,Y);return K}return!e.collect||O!==t&&O!==o||i.push([O,D,V,Y]),d.bind(O,D,V,Y||kt)},Ht=(O,D,V)=>{if(Ee(O)){let Y=O.length;const W=[];for(;Y--;)W[Y]=Ht(O[Y],D,V);return W}if(i.length>0&&(O===t||O===o)){let Y=i.length;for(;Y--;){const[W,K,at]=i[Y];O!==W||D&&D!==K||V&&V!==at||d.unbind(W,K,at)}}return d.unbind(O,D,V)},ft=O=>{if(O&&it(O)){const D=O.getAttribute("data-mce-contenteditable");return D&&"inherit"!==D?D:"inherit"!==O.contentEditable?O.contentEditable:null}return null},kt={doc:t,settings:e,win:o,files:r,stdMode:!0,boxModel:!0,styleSheetLoader:s,boundEvents:i,styles:c,schema:l,events:d,isBlock:O=>Nt(O)?It(u,O):it(O)&&(It(u,O.nodeName)||Wa(l,O)),root:null,clone:(O,D)=>O.cloneNode(D),getRoot:b,getViewPort:O=>{const D=Et(O);return{x:D.x,y:D.y,w:D.width,h:D.height}},getRect:O=>{const D=m(O),V=y(D),Y=k(D);return{x:V.x,y:V.y,w:Y.w,h:Y.h}},getSize:k,getParent:(O,D,V)=>{const Y=N(O,D,V,!1);return Y&&Y.length>0?Y[0]:null},getParents:N,get:m,getNext:(O,D)=>R(O,D,"nextSibling"),getPrev:(O,D)=>R(O,D,"previousSibling"),select:(O,D)=>{var V,Y;const W=null!==(Y=null!==(V=m(D))&&void 0!==V?V:e.root_element)&&void 0!==Y?Y:t;return Yt(W.querySelectorAll)?Ae(W.querySelectorAll(O)):[]},is:E,add:T,create:P,createHTML:(O,D,V="")=>{let Y="<"+O;for(const W in D)Hn(D,W)&&(Y+=" "+W+'="'+B(D[W])+'"');return Xt(V)&&It(l.getVoidElements(),O)?Y+" />":Y+">"+V+"</"+O+">"},createFragment:O=>{const D=t.createElement("div"),V=t.createDocumentFragment();let Y;for(V.appendChild(D),O&&(D.innerHTML=O);Y=D.firstChild;)V.appendChild(Y);return V.removeChild(D),V},remove:j,setStyle:(O,D,V)=>{I(O,Y=>{const W=A(Y);zp(W,D,V),e.update_styles&&jp(c,W)})},getStyle:C,setStyles:(O,D)=>{I(O,V=>{const Y=A(V);le(D,(W,K)=>{zp(Y,K,W)}),e.update_styles&&jp(c,Y)})},removeAllAttribs:O=>I(O,D=>{const V=D.attributes;for(let Y=V.length-1;Y>=0;Y--)D.removeAttributeNode(V.item(Y))}),setAttrib:f,setAttribs:q,getAttrib:g,getPos:y,parseStyle:O=>c.parse(O),serializeStyle:(O,D)=>c.serialize(O,D),addStyle:O=>{if(kt!==me.DOM&&t===document){if(n[O])return;n[O]=!0}let D=t.getElementById("mceDefaultStyles");if(!D){D=t.createElement("style"),D.id="mceDefaultStyles",D.type="text/css";const V=t.head;V.firstChild?V.insertBefore(D,V.firstChild):V.appendChild(D)}D.styleSheet?D.styleSheet.cssText+=O:D.appendChild(t.createTextNode(O))},loadCSS:O=>{O||(O=""),J(O.split(","),D=>{r[D]=!0,s.load(D).catch(jt)})},addClass:(O,D)=>{lt(O,D,!0)},removeClass:(O,D)=>{lt(O,D,!1)},hasClass:(O,D)=>{const V=p(O),Y=D.split(" ");return st(V)&&_n(Y,W=>Ds(V,W))},toggleClass:lt,show:O=>{I(O,D=>cc(A(D),"display"))},hide:O=>{I(O,D=>sc(A(D),"display","none"))},isHidden:O=>{const D=p(O);return st(D)&&tr(za(D,"display"),"none")},uniqueId:O=>(O||"mce_")+a++,setHTML:H,getOuterHTML:O=>{const D=p(O);return st(D)?it(D.dom)?D.dom.outerHTML:(V=>{const Y=Be("div"),W=A(V.dom.cloneNode(!0));return Se(Y,W),F(Y)})(D):""},setOuterHTML:(O,D)=>{I(O,V=>{it(V)&&(V.outerHTML=D)})},decode:da.decode,encode:B,insertAfter:(O,D)=>{const V=m(D);return I(O,Y=>{const W=V?.parentNode,K=V?.nextSibling;return W&&(K?W.insertBefore(Y,K):W.appendChild(Y)),Y})},replace:Z,rename:(O,D)=>{if(O.nodeName!==D.toUpperCase()){const V=P(D);return Du(h(O),Y=>{f(V,Y.nodeName,g(O,Y.nodeName))}),Z(V,O,!0),V}return O},findCommonAncestor:(O,D)=>{let V=O;for(;V;){let Y=D;for(;Y&&V!==Y;)Y=Y.parentNode;if(V===Y)break;V=V.parentNode}return!V&&O.ownerDocument?O.ownerDocument.documentElement:V},run:I,getAttribs:h,isEmpty:(O,D,V)=>{let Y=0;if(_t(O))return!1;const W=O.firstChild;if(W){const K=new Kt(W,O),at=l?l.getWhitespaceElements():{},St=D||(l?l.getNonEmptyElements():null);let vt=W;do{if(it(vt)){const Dt=vt.getAttribute("data-mce-bogus");if(Dt){vt=K.next("all"===Dt);continue}const Mt=vt.nodeName.toLowerCase();if(St&&St[Mt]){if("br"===Mt){Y++,vt=K.next();continue}return!1}if(_t(vt))return!1}if(sa(vt)||tt(vt)&&!Va(vt.data)&&(!V?.includeZwsp||!ak(vt.data))||tt(vt)&&vt.parentNode&&at[vt.parentNode.nodeName]&&Va(vt.data))return!1;vt=K.next()}while(vt)}return Y<=1},createRng:mt,nodeIndex:js,split:(O,D,V)=>{let Y,W,K=mt();if(O&&D&&O.parentNode&&D.parentNode){const at=O.parentNode;return K.setStart(at,js(O)),K.setEnd(D.parentNode,js(D)),Y=K.extractContents(),K=mt(),K.setStart(D.parentNode,js(D)+1),K.setEnd(at,js(O)+1),W=K.extractContents(),at.insertBefore(Eu(kt,Y),O),at.insertBefore(V||D,O),at.insertBefore(Eu(kt,W),O),j(O),V||D}},bind:Ut,unbind:Ht,fire:(O,D,V)=>d.dispatch(O,D,V),dispatch:(O,D,V)=>d.dispatch(O,D,V),getContentEditable:ft,getContentEditableParent:O=>{const D=b();let V=null;for(let Y=O;Y&&Y!==D&&(V=ft(Y),null===V);Y=Y.parentNode);return V},isEditable:O=>{if(st(O)){const D=it(O)?O:O.parentElement;return st(D)&&aa(A(D))}return!1},destroy:()=>{if(i.length>0){let O=i.length;for(;O--;){const[D,V,Y]=i[O];d.unbind(D,V,Y)}}le(r,(O,D)=>{s.unload(D),delete r[D]})},isChildOf:(O,D)=>O===D||D.contains(O),dumpRng:O=>"startContainer: "+O.startContainer.nodeName+", startOffset: "+O.startOffset+", endContainer: "+O.endContainer.nodeName+", endOffset: "+O.endOffset},Bt=((O,D,V)=>{const Y=D.keep_values,K={style:{set:(at,St)=>{const vt=A(at);Y&&Fi(vt,Pu,St),Le(vt,"style"),Nt(St)&&Ps(vt,O.parse(St))},get:at=>{const St=A(at),vt=An(St,Pu)||An(St,"style");return O.serialize(O.parse(vt),ge(St))}}};return Y&&(K.href=K.src={set:(at,St,vt)=>{const Dt=A(at);Yt(D.url_converter)&&st(St)&&(St=D.url_converter.call(D.url_converter_scope||V(),String(St),vt,at)),Fi(Dt,"data-mce-"+vt,St),Fi(Dt,vt,St)},get:(at,St)=>{const vt=A(at);return An(vt,"data-mce-"+St)||An(vt,St)}}),K})(c,e,ut(kt));return kt};me.DOM=me(document),me.nodeIndex=js;const Ek=me.DOM;class Ur{constructor(e={}){this.states={},this.queue=[],this.scriptLoadedCallbacks={},this.queueLoadedCallbacks=[],this.loading=!1,this.settings=e}_setReferrerPolicy(e){this.settings.referrerPolicy=e}loadScript(e){return new Promise((n,o)=>{const r=Ek;let a;const s=()=>{r.remove(i),a&&(a.onerror=a.onload=a=null)},i=r.uniqueId();a=document.createElement("script"),a.id=i,a.type="text/javascript",a.src=ot._addCacheSuffix(e),this.settings.referrerPolicy&&r.setAttrib(a,"referrerpolicy",this.settings.referrerPolicy),a.onload=()=>{s(),n()},a.onerror=()=>{s(),o("Failed to load script: "+e)},(document.getElementsByTagName("head")[0]||document.body).appendChild(a)})}isDone(e){return 2===this.states[e]}markDone(e){this.states[e]=2}add(e){const n=this;return n.queue.push(e),void 0===n.states[e]&&(n.states[e]=0),new Promise((o,r)=>{n.scriptLoadedCallbacks[e]||(n.scriptLoadedCallbacks[e]=[]),n.scriptLoadedCallbacks[e].push({resolve:o,reject:r})})}load(e){return this.add(e)}remove(e){delete this.states[e],delete this.scriptLoadedCallbacks[e]}loadQueue(){const e=this.queue;return this.queue=[],this.loadScripts(e)}loadScripts(e){const n=this,o=(l,c)=>{ce(n.scriptLoadedCallbacks,c).each(d=>{J(d,u=>u[l](c))}),delete n.scriptLoadedCallbacks[c]},r=l=>{const c=Ot(l,d=>"rejected"===d.status);return c.length>0?Promise.reject(gn(c,({reason:d})=>Ee(d)?d:[d])):Promise.resolve()},a=l=>Promise.allSettled(te(l,c=>2===n.states[c]?(o("resolve",c),Promise.resolve()):3===n.states[c]?(o("reject",c),Promise.reject(c)):(n.states[c]=1,n.loadScript(c).then(()=>{n.states[c]=2,o("resolve",c);const d=n.queue;return d.length>0?(n.queue=[],a(d).then(r)):Promise.resolve()},()=>(n.states[c]=3,o("reject",c),Promise.reject(c)))))),s=l=>(n.loading=!0,a(l).then(c=>{n.loading=!1;const d=n.queueLoadedCallbacks.shift();return S.from(d).each(En),r(c)})),i=Oa(e);return n.loading?new Promise((l,c)=>{n.queueLoadedCallbacks.push(()=>{s(i).then(l,c)})}):s(i)}}Ur.ScriptLoader=new Ur;const Ye=t=>{let e=t;return{get:()=>e,set:n=>{e=n}}},Ui={},Mu=Ye("en"),Hp=()=>ce(Ui,Mu.get()),Mo={getData:()=>Yr(Ui,t=>({...t})),setCode:t=>{t&&Mu.set(t)},getCode:()=>Mu.get(),add:(t,e)=>{let n=Ui[t];n||(Ui[t]=n={}),le(e,(o,r)=>{n[r.toLowerCase()]=o})},translate:t=>{const e=Hp().getOr({}),n=i=>Yt(i)?Object.prototype.toString.call(i):o(i)?"":""+i,o=i=>""===i||null==i,r=i=>{const l=n(i);return ce(e,l.toLowerCase()).map(n).getOr(l)},a=i=>i.replace(/{context:\w+}$/,"");if(o(t))return"";if(At(s=t)&&It(s,"raw"))return n(t.raw);var s,i;if(Ee(i=t)&&i.length>1){const i=t.slice(1);return a(r(t[0]).replace(/\{([0-9]+)\}/g,(l,c)=>It(i,c)?n(i[c]):l))}return a(r(t))},isRtl:()=>Hp().bind(t=>ce(t,"_dir")).exists(t=>"rtl"===t),hasCode:t=>It(Ui,t)},vn=()=>{const t=[],e={},n={},o=[],r=(l,c)=>{const d=Ot(o,u=>u.name===l&&u.state===c);J(d,u=>u.resolve())},a=l=>It(e,l),s=(l,c)=>{const d=Mo.getCode();!d||c&&-1===(","+(c||"")+",").indexOf(","+d+",")||Ur.ScriptLoader.add(e[l]+"/langs/"+d+".js")},i=(l,c="added")=>"added"===c&&It(n,l)||"loaded"===c&&a(l)?Promise.resolve():new Promise(d=>{o.push({name:l,state:c,resolve:d})});return{items:t,urls:e,lookup:n,get:l=>{if(n[l])return n[l].instance},requireLangPack:(l,c)=>{!1!==vn.languageLoad&&(a(l)?s(l,c):i(l,"loaded").then(()=>s(l,c)))},add:(l,c)=>(t.push(c),n[l]={instance:c},r(l,"added"),c),remove:l=>{delete e[l],delete n[l]},createUrl:(l,c)=>Nt(c)?Nt(l)?{prefix:"",resource:c,suffix:""}:{prefix:l.prefix,resource:c,suffix:l.suffix}:c,load:(l,c)=>{if(e[l])return Promise.resolve();let d=Nt(c)?c:c.prefix+c.resource+c.suffix;0!==d.indexOf("/")&&-1===d.indexOf("://")&&(d=vn.baseURL+"/"+d),e[l]=d.substring(0,d.lastIndexOf("/"));const u=()=>(r(l,"loaded"),Promise.resolve());return n[l]?u():Ur.ScriptLoader.add(d).then(u)},waitFor:i}};vn.languageLoad=!0,vn.baseURL="",vn.PluginManager=vn(),vn.ThemeManager=vn(),vn.ModelManager=vn();const _k=t=>{const e=Ye(S.none()),n=()=>e.get().each(o=>clearInterval(o));return{clear:()=>{n(),e.set(S.none())},isSet:()=>e.get().isSome(),get:()=>e.get(),set:o=>{n(),e.set(S.some(setInterval(o,t)))}}},ma=()=>{const t=(e=>{const n=Ye(S.none()),o=()=>n.get().each(e);return{clear:()=>{o(),n.set(S.none())},isSet:()=>n.get().isSome(),get:()=>n.get(),set:r=>{o(),n.set(S.some(r))}}})(jt);return{...t,on:e=>t.get().each(e)}},Ec=(t,e)=>{let n=null;return{cancel:()=>{Oe(n)||(clearTimeout(n),n=null)},throttle:(...o)=>{Oe(n)&&(n=setTimeout(()=>{n=null,t.apply(null,o)},e))}}},Lu=(t,e)=>{let n=null;const o=()=>{Oe(n)||(clearTimeout(n),n=null)};return{cancel:o,throttle:(...r)=>{o(),n=setTimeout(()=>{n=null,t.apply(null,r)},e)}}},ji=ut("mce-annotation"),Ya=ut("data-mce-annotation"),zs=ut("data-mce-annotation-uid"),zi=ut("data-mce-annotation-active"),Hi=ut("data-mce-annotation-classes"),$i=ut("data-mce-annotation-attrs"),$p=t=>e=>re(e,t),qp=(t,e)=>{const n=t.selection.getRng(),o=A(n.startContainer),r=A(t.getBody()),a=e.fold(()=>"."+ji(),i=>`[${Ya()}="${i}"]`),s=na(o,n.startOffset).getOr(o);return er(s,a,$p(r)).bind(i=>Zn(i,`${zs()}`).bind(l=>Zn(i,`${Ya()}`).map(c=>{const d=Wp(t,l);return{uid:l,name:c,elements:d}})))},Vp=(t,e)=>As(t,"data-mce-bogus")||Zt(t,'[data-mce-bogus="all"]',$p(e)),Wp=(t,e)=>{const n=A(t.getBody()),o=Rt(n,`[${zs()}="${e}"]`);return Ot(o,r=>!Vp(r,n))},Kp=(t,e)=>{const n=A(t.getBody()),o=Rt(n,`[${Ya()}="${e}"]`),r={};return J(o,a=>{if(!Vp(a,n)){const s=An(a,zs()),i=ce(r,s).getOr([]);r[s]=i.concat([a])}}),r};let Gp=0;const qi=t=>{const e=(new Date).getTime(),n=Math.floor(1e9*Math.random());return Gp++,t+"_"+n+Gp+String(e)},Yp=(t,e)=>A(t.dom.cloneNode(e)),Xa=t=>Yp(t,!1),Xp=t=>Yp(t,!0),Qp=(t,e)=>{const n=((r,a)=>{const s=Be(a),i=Ql(r);return Do(s,i),s})(t,e);to(t,n);const o=ze(t);return v(n,o),w(t),n},Jp=(t,e,n=de)=>{const o=new Kt(t,e),r=a=>{let s;do{s=o[a]()}while(s&&!tt(s)&&!n(s));return S.from(s).filter(tt)};return{current:()=>S.from(o.current()).filter(tt),next:()=>r("next"),prev:()=>r("prev"),prev2:()=>r("prev2")}},Qa=(t,e)=>{const n=e||(r=>t.isBlock(r)||_e(r)||ve(r)),o=(r,a,s,i)=>{if(tt(r)){const l=i(r,a,r.data);if(-1!==l)return S.some({container:r,offset:l})}return s().bind(l=>o(l.container,l.offset,s,i))};return{backwards:(r,a,s,i)=>{const l=Jp(r,i??t.getRoot(),n);return o(r,a,()=>l.prev().map(c=>({container:c,offset:c.length})),s).getOrNull()},forwards:(r,a,s,i)=>{const l=Jp(r,i??t.getRoot(),n);return o(r,a,()=>l.next().map(c=>({container:c,offset:0})),s).getOrNull()}}},Hs=Math.round,Ja=t=>t?{left:Hs(t.left),top:Hs(t.top),bottom:Hs(t.bottom),right:Hs(t.right),width:Hs(t.width),height:Hs(t.height)}:{left:0,top:0,bottom:0,right:0,width:0,height:0},Zp=(t,e)=>(t=Ja(t),e||(t.left=t.left+t.width),t.right=t.left,t.width=0,t),th=(t,e,n)=>t>=0&&t<=Math.min(e.height,n.height)/2,Vi=(t,e)=>{const n=Math.min(e.height/2,t.height/2);return t.bottom-n<e.top||!(t.top>e.bottom)&&th(e.top-t.bottom,t,e)},Wi=(t,e)=>t.top>e.bottom||!(t.bottom<e.top)&&th(e.bottom-t.top,t,e),eh=(t,e,n)=>{const o=Math.max(Math.min(e,t.left+t.width),t.left),r=Math.max(Math.min(n,t.top+t.height),t.top);return Math.sqrt((e-o)*(e-o)+(n-r)*(n-r))},_c=t=>{const e=t.startContainer,n=t.startOffset;return e===t.endContainer&&e.hasChildNodes()&&t.endOffset===n+1?e.childNodes[n]:null},ga=(t,e)=>{if(it(t)&&t.hasChildNodes()){const n=t.childNodes;return n[(s=n.length-1,Math.min(Math.max(e,0),s))]}var s;return t},Nk=new RegExp("[\u0300-\u036f\u0483-\u0487\u0488-\u0489\u0591-\u05bd\u05bf\u05c1-\u05c2\u05c4-\u05c5\u05c7\u0610-\u061a\u064b-\u065f\u0670\u06d6-\u06dc\u06df-\u06e4\u06e7-\u06e8\u06ea-\u06ed\u0711\u0730-\u074a\u07a6-\u07b0\u07eb-\u07f3\u0816-\u0819\u081b-\u0823\u0825-\u0827\u0829-\u082d\u0859-\u085b\u08e3-\u0902\u093a\u093c\u0941-\u0948\u094d\u0951-\u0957\u0962-\u0963\u0981\u09bc\u09be\u09c1-\u09c4\u09cd\u09d7\u09e2-\u09e3\u0a01-\u0a02\u0a3c\u0a41-\u0a42\u0a47-\u0a48\u0a4b-\u0a4d\u0a51\u0a70-\u0a71\u0a75\u0a81-\u0a82\u0abc\u0ac1-\u0ac5\u0ac7-\u0ac8\u0acd\u0ae2-\u0ae3\u0b01\u0b3c\u0b3e\u0b3f\u0b41-\u0b44\u0b4d\u0b56\u0b57\u0b62-\u0b63\u0b82\u0bbe\u0bc0\u0bcd\u0bd7\u0c00\u0c3e-\u0c40\u0c46-\u0c48\u0c4a-\u0c4d\u0c55-\u0c56\u0c62-\u0c63\u0c81\u0cbc\u0cbf\u0cc2\u0cc6\u0ccc-\u0ccd\u0cd5-\u0cd6\u0ce2-\u0ce3\u0d01\u0d3e\u0d41-\u0d44\u0d4d\u0d57\u0d62-\u0d63\u0dca\u0dcf\u0dd2-\u0dd4\u0dd6\u0ddf\u0e31\u0e34-\u0e3a\u0e47-\u0e4e\u0eb1\u0eb4-\u0eb9\u0ebb-\u0ebc\u0ec8-\u0ecd\u0f18-\u0f19\u0f35\u0f37\u0f39\u0f71-\u0f7e\u0f80-\u0f84\u0f86-\u0f87\u0f8d-\u0f97\u0f99-\u0fbc\u0fc6\u102d-\u1030\u1032-\u1037\u1039-\u103a\u103d-\u103e\u1058-\u1059\u105e-\u1060\u1071-\u1074\u1082\u1085-\u1086\u108d\u109d\u135d-\u135f\u1712-\u1714\u1732-\u1734\u1752-\u1753\u1772-\u1773\u17b4-\u17b5\u17b7-\u17bd\u17c6\u17c9-\u17d3\u17dd\u180b-\u180d\u18a9\u1920-\u1922\u1927-\u1928\u1932\u1939-\u193b\u1a17-\u1a18\u1a1b\u1a56\u1a58-\u1a5e\u1a60\u1a62\u1a65-\u1a6c\u1a73-\u1a7c\u1a7f\u1ab0-\u1abd\u1abe\u1b00-\u1b03\u1b34\u1b36-\u1b3a\u1b3c\u1b42\u1b6b-\u1b73\u1b80-\u1b81\u1ba2-\u1ba5\u1ba8-\u1ba9\u1bab-\u1bad\u1be6\u1be8-\u1be9\u1bed\u1bef-\u1bf1\u1c2c-\u1c33\u1c36-\u1c37\u1cd0-\u1cd2\u1cd4-\u1ce0\u1ce2-\u1ce8\u1ced\u1cf4\u1cf8-\u1cf9\u1dc0-\u1df5\u1dfc-\u1dff\u200c-\u200d\u20d0-\u20dc\u20dd-\u20e0\u20e1\u20e2-\u20e4\u20e5-\u20f0\u2cef-\u2cf1\u2d7f\u2de0-\u2dff\u302a-\u302d\u302e-\u302f\u3099-\u309a\ua66f\ua670-\ua672\ua674-\ua67d\ua69e-\ua69f\ua6f0-\ua6f1\ua802\ua806\ua80b\ua825-\ua826\ua8c4\ua8e0-\ua8f1\ua926-\ua92d\ua947-\ua951\ua980-\ua982\ua9b3\ua9b6-\ua9b9\ua9bc\ua9e5\uaa29-\uaa2e\uaa31-\uaa32\uaa35-\uaa36\uaa43\uaa4c\uaa7c\uaab0\uaab2-\uaab4\uaab7-\uaab8\uaabe-\uaabf\uaac1\uaaec-\uaaed\uaaf6\uabe5\uabe8\uabed\ufb1e\ufe00-\ufe0f\ufe20-\ufe2f\uff9e-\uff9f]"),nh=t=>Nt(t)&&t.charCodeAt(0)>=768&&Nk.test(t),oh=it,Rk=ko,rh=So("display","block table"),Ak=So("float","left right"),Ki=((...t)=>e=>{for(let n=0;n<t.length;n++)if(!t[n](e))return!1;return!0})(oh,Rk,$o(Ak)),Tk=$o(So("white-space","pre pre-line pre-wrap")),Gi=tt,Iu=_e,ah=me.nodeIndex,Nc=(t,e)=>e<0&&it(t)&&t.hasChildNodes()?void 0:ga(t,e),Fu=t=>t?t.createRange():me.DOM.createRng(),Uu=t=>Nt(t)&&/[\r\n\t ]/.test(t),sh=t=>!!t.setStart&&!!t.setEnd,ju=t=>{const e=t.startContainer,n=t.startOffset;if(Uu(t.toString())&&Tk(e.parentNode)&&tt(e)){const o=e.data;if(Uu(o[n-1])||Uu(o[n+1]))return!0}return!1},ih=t=>0===t.left&&0===t.right&&0===t.top&&0===t.bottom,fa=t=>{var e;let n;const o=t.getClientRects();return n=Ja(o.length>0?o[0]:t.getBoundingClientRect()),!sh(t)&&Iu(t)&&ih(n)?(r=>{const a=r.ownerDocument,s=Fu(a),i=a.createTextNode(Ge),l=r.parentNode;l.insertBefore(i,r),s.setStart(i,0),s.setEnd(i,1);const c=Ja(s.getBoundingClientRect());return l.removeChild(i),c})(t):ih(n)&&sh(t)&&null!==(e=(r=>{const s=r.endContainer,i=r.startOffset,l=r.endOffset;if(r.startContainer===s&&tt(s)&&0===i&&1===l){const c=r.cloneRange();return c.setEndAfter(s),fa(c)}return null})(t))&&void 0!==e?e:n},Za=(t,e)=>{const n=Zp(t,e);return n.width=1,n.right=n.left+1,n},$=(t,e,n)=>{const o=()=>(n||(n=(r=>{const a=[],s=d=>{var u,m;0!==d.height&&(a.length>0&&(u=d).left===(m=a[a.length-1]).left&&u.top===m.top&&u.bottom===m.bottom&&u.right===m.right||a.push(d))},i=(d,u)=>{const m=Fu(d.ownerDocument);if(u<d.data.length){if(nh(d.data[u]))return;if(nh(d.data[u-1])&&(m.setStart(d,u),m.setEnd(d,u+1),!ju(m)))return void s(Za(fa(m),!1))}u>0&&(m.setStart(d,u-1),m.setEnd(d,u),ju(m)||s(Za(fa(m),!1))),u<d.data.length&&(m.setStart(d,u),m.setEnd(d,u+1),ju(m)||s(Za(fa(m),!0)))},l=r.container(),c=r.offset();if(Gi(l))return i(l,c),a;if(oh(l))if(r.isAtEnd()){const d=Nc(l,c);Gi(d)&&i(d,d.data.length),Ki(d)&&!Iu(d)&&s(Za(fa(d),!1))}else{const d=Nc(l,c);if(Gi(d)&&i(d,0),Ki(d)&&r.isAtEnd())return s(Za(fa(d),!1)),a;const u=Nc(r.container(),r.offset()-1);Ki(u)&&!Iu(u)&&(rh(u)||rh(d)||!Ki(d))&&s(Za(fa(u),!1)),Ki(d)&&s(Za(fa(d),!0))}return a})($(t,e))),n);return{container:ut(t),offset:ut(e),toRange:()=>{const r=Fu(t.ownerDocument);return r.setStart(t,e),r.setEnd(t,e),r},getClientRects:o,isVisible:()=>o().length>0,isAtStart:()=>(Gi(t),0===e),isAtEnd:()=>Gi(t)?e>=t.data.length:e>=t.childNodes.length,isEqual:r=>r&&t===r.container()&&e===r.offset(),getNode:r=>Nc(t,r?e-1:e)}};$.fromRangeStart=t=>$(t.startContainer,t.startOffset),$.fromRangeEnd=t=>$(t.endContainer,t.endOffset),$.after=t=>$(t.parentNode,ah(t)+1),$.before=t=>$(t.parentNode,ah(t)),$.isAbove=(t,e)=>on(We(e.getClientRects()),Nn(t.getClientRects()),Vi).getOr(!1),$.isBelow=(t,e)=>on(Nn(e.getClientRects()),We(t.getClientRects()),Wi).getOr(!1),$.isAtStart=t=>!!t&&t.isAtStart(),$.isAtEnd=t=>!!t&&t.isAtEnd(),$.isTextPosition=t=>!!t&&tt(t.container()),$.isElementPosition=t=>!$.isTextPosition(t);const Rc=(t,e)=>{tt(e)&&0===e.data.length&&t.remove(e)},zu=(t,e,n)=>{var o,a;gu(n)?((o,r,a)=>{const s=S.from(a.firstChild),i=S.from(a.lastChild);r.insertNode(a),s.each(l=>Rc(o,l.previousSibling)),i.each(l=>Rc(o,l.nextSibling))})(t,e,n):(o=t,e.insertNode(a=n),Rc(o,a.previousSibling),Rc(o,a.nextSibling))},Lo=tt,lh=$a,ch=me.nodeIndex,dh=t=>{const e=t.parentNode;return lh(e)?dh(e):e},Hu=t=>t?Bo(t.childNodes,(e,n)=>(lh(n)&&"BR"!==n.nodeName?e=e.concat(Hu(n)):e.push(n),e),[]):[],uh=t=>e=>t===e,mh=t=>(Lo(t)?"text()":t.nodeName.toLowerCase())+"["+(e=>{let n,o;n=Hu(dh(e)),o=Xo(n,uh(e),e),n=n.slice(0,o+1);const r=Bo(n,(a,s,i)=>(Lo(s)&&Lo(n[i-1])&&a++,a),0);return n=Oo(n,ke([e.nodeName])),o=Xo(n,uh(e),e),o-r})(t)+"]",gh=(t,e)=>{let n,o=[],r=e.container(),a=e.offset();if(Lo(r))n=((i,l)=>{let c=i;for(;(c=c.previousSibling)&&Lo(c);)l+=c.data.length;return l})(r,a);else{const i=r.childNodes;a>=i.length?(n="after",a=i.length-1):n="before",r=i[a]}o.push(mh(r));let s=((i,l)=>{const d=[];for(let u=l.parentNode;u&&u!==i;u=u.parentNode)d.push(u);return d})(t,r);return s=Oo(s,$o($a)),o=o.concat(Nr(s,i=>mh(i))),o.reverse().join("/")+","+n},fh=(t,e)=>{if(!e)return null;const n=e.split(","),o=n[0].split("/"),r=n.length>1?n[1]:"before",a=Bo(o,(s,i)=>{const l=/([\w\-\(\)]+)\[([0-9]+)\]/.exec(i);return l?("text()"===l[1]&&(l[1]="#text"),((c,d,u)=>{let m=Hu(c);return m=Oo(m,(p,g)=>!Lo(p)||!Lo(m[g-1])),m=Oo(m,ke([d])),m[u]})(s,l[1],parseInt(l[2],10))):null},t);if(!a)return null;if(!Lo(a)&&a.parentNode){let s;return s="after"===r?ch(a)+1:ch(a),$(a.parentNode,s)}return((s,i)=>{let l=s,c=0;for(;Lo(l);){const d=l.data.length;if(i>=c&&i<=c+d){s=l,i-=c;break}if(!Lo(l.nextSibling)){s=l,i=d;break}c+=d,l=l.nextSibling}return Lo(s)&&i>s.data.length&&(i=s.data.length),$(s,i)})(a,parseInt(r,10))},Ac=ve,ph=(t,e,n,o,r)=>{const a=r?o.startContainer:o.endContainer;let s=r?o.startOffset:o.endOffset;const i=[],l=t.getRoot();if(tt(a))i.push(n?((c,d,u)=>{let m=c(d.data.slice(0,u)).length;for(let p=d.previousSibling;p&&tt(p);p=p.previousSibling)m+=c(p.data).length;return m})(e,a,s):s);else{let c=0;const d=a.childNodes;s>=d.length&&d.length&&(c=1,s=Math.max(0,d.length-1)),i.push(t.nodeIndex(d[s],n)+c)}for(let c=a;c&&c!==l;c=c.parentNode)i.push(t.nodeIndex(c,n));return i},$u=(t,e,n)=>{let o=0;return ot.each(t.select(e),r=>"all"===r.getAttribute("data-mce-bogus")?void 0:r!==n&&void o++),o},hh=(t,e)=>{let n=e?t.startContainer:t.endContainer,o=e?t.startOffset:t.endOffset;if(it(n)&&"TR"===n.nodeName){const r=n.childNodes;n=r[Math.min(e?o:o-1,r.length-1)],n&&(o=e?0:n.childNodes.length,e?t.setStart(n,o):t.setEnd(n,o))}},bh=t=>(hh(t,!0),hh(t,!1),t),vh=(t,e)=>{if(it(t)&&(t=ga(t,e),Ac(t)))return t;if(no(t)){tt(t)&&Fs(t)&&(t=t.parentNode);let n=t.previousSibling;if(Ac(n)||(n=t.nextSibling,Ac(n)))return n}},yh=(t,e,n)=>{const o=n.getNode(),r=n.getRng();if("IMG"===o.nodeName||Ac(o)){const s=o.nodeName;return{name:s,index:$u(n.dom,s,o)}}const a=vh((s=r).startContainer,s.startOffset)||vh(s.endContainer,s.endOffset);var s;if(a){const s=a.tagName;return{name:s,index:$u(n.dom,s,a)}}return((s,i,l,c)=>{const d=i.dom,u=ph(d,s,l,c,!0),m=i.isForward(),p=hp(c)?{isFakeCaret:!0}:{};return i.isCollapsed()?{start:u,forward:m,...p}:{start:u,end:ph(d,s,l,c,!1),forward:m,...p}})(t,n,e,r)},Ch=(t,e,n)=>{const o={"data-mce-type":"bookmark",id:e,style:"overflow:hidden;line-height:0px"};return n?t.create("span",o,"&#xFEFF;"):t.create("span",o)},wh=(t,e)=>{const n=t.dom;let o=t.getRng();const r=n.uniqueId(),a=t.isCollapsed(),s=t.getNode(),i=s.nodeName,l=t.isForward();if("IMG"===i)return{name:i,index:$u(n,i,s)};const c=bh(o.cloneRange());if(!a){c.collapse(!1);const u=Ch(n,r+"_end",e);zu(n,c,u)}o=bh(o),o.collapse(!0);const d=Ch(n,r+"_start",e);return zu(n,o,d),t.moveToBookmark({id:r,keep:!0,forward:l}),{id:r,forward:l}},qu=Ct(yh,qe,!0),xh=t=>{const e=a=>a(t),n=ut(t),o=()=>r,r={tag:!0,inner:t,fold:(a,s)=>s(t),isValue:ye,isError:de,map:a=>On.value(a(t)),mapError:o,bind:e,exists:e,forall:e,getOr:n,or:o,getOrThunk:n,orThunk:o,getOrDie:n,each:a=>{a(t)},toOptional:()=>S.some(t)};return r},Sh=t=>{const e=()=>n,n={tag:!1,inner:t,fold:(o,r)=>o(t),isValue:de,isError:ye,map:e,mapError:o=>On.error(o(t)),bind:e,exists:de,forall:ye,getOr:qe,or:qe,getOrThunk:je,orThunk:je,getOrDie:Ue(String(t)),each:jt,toOptional:S.none};return n},On={value:xh,error:Sh,fromOption:(t,e)=>t.fold(()=>Sh(e),xh)},or=t=>{if(!Ee(t))throw new Error("cases must be an array");if(0===t.length)throw new Error("there must be at least one case");const e=[],n={};return J(t,(o,r)=>{const a=tn(o);if(1!==a.length)throw new Error("one and only one name per case");const s=a[0],i=o[s];if(void 0!==n[s])throw new Error("duplicate key detected:"+s);if("cata"===s)throw new Error("cannot have a case named cata (sorry)");if(!Ee(i))throw new Error("case arguments must be an array");e.push(s),n[s]=(...l)=>{const c=l.length;if(c!==i.length)throw new Error("Wrong number of arguments to case "+s+". Expected "+i.length+" ("+i+"), got "+c);return{fold:(...d)=>{if(d.length!==t.length)throw new Error("Wrong number of arguments to fold. Expected "+t.length+", got "+d.length);return d[r].apply(null,l)},match:d=>{const u=tn(d);if(e.length!==u.length)throw new Error("Wrong number of arguments to match. Expected: "+e.join(",")+"\nActual: "+u.join(","));if(!_n(e,m=>Lt(u,m)))throw new Error("Not all branches were specified when using match. Specified: "+u.join(", ")+"\nRequired: "+e.join(", "));return d[s].apply(null,l)},log:d=>{console.log(d,{constructors:e,constructor:s,params:l})}}}}),n};or([{bothErrors:["error1","error2"]},{firstError:["error1","value2"]},{secondError:["value1","error2"]},{bothValues:["value1","value2"]}]);const Ok=t=>"inline-command"===t.type||"inline-format"===t.type,Bk=t=>"block-command"===t.type||"block-format"===t.type,Dk=t=>{const e=o=>On.error({message:o,pattern:t}),n=(o,r,a)=>{if(void 0!==t.format){let s;if(Ee(t.format)){if(!_n(t.format,Nt))return e(o+" pattern has non-string items in the `format` array");s=t.format}else{if(!Nt(t.format))return e(o+" pattern has non-string `format` parameter");s=[t.format]}return On.value(r(s))}return void 0!==t.cmd?Nt(t.cmd)?On.value(a(t.cmd,t.value)):e(o+" pattern has non-string `cmd` parameter"):e(o+" pattern is missing both `format` and `cmd` parameters")};if(!At(t))return e("Raw pattern is not an object");if(!Nt(t.start))return e("Raw pattern is missing `start` parameter");if(void 0!==t.end){if(!Nt(t.end))return e("Inline pattern has non-string `end` parameter");if(0===t.start.length&&0===t.end.length)return e("Inline pattern has empty `start` and `end` parameters");let o=t.start,r=t.end;return 0===r.length&&(r=o,o=""),n("Inline",a=>({type:"inline-format",start:o,end:r,format:a}),(a,s)=>({type:"inline-command",start:o,end:r,cmd:a,value:s}))}return void 0!==t.replacement?Nt(t.replacement)?0===t.start.length?e("Replacement pattern has empty `start` parameter"):On.value({type:"inline-command",start:"",end:t.start,cmd:"mceInsertContent",value:t.replacement}):e("Replacement pattern has non-string `replacement` parameter"):0===t.start.length?e("Block pattern has empty `start` parameter"):n("Block",o=>({type:"block-format",start:t.start,format:o[0]}),(o,r)=>({type:"block-command",start:t.start,cmd:o,value:r}))},kh=t=>Ot(t,Bk),Eh=t=>Ot(t,Ok),_h=t=>{const e=(n=>{const o=[],r=[];return J(n,a=>{a.fold(s=>{o.push(s)},s=>{r.push(s)})}),{errors:o,values:r}})(te(t,Dk));return J(e.errors,n=>console.error(n.message,n.pattern)),e.values},Vu=po().deviceType,Pk=Vu.isTouch(),Mk=me.DOM,Nh=t=>Ce(t,RegExp),yt=t=>e=>e.options.get(t),Wu=t=>Nt(t)||At(t),Rh=(t,e="")=>n=>{const o=Nt(n);if(o){if(-1!==n.indexOf("=")){const r=(a=>{const s=a.indexOf("=")>0?a.split(/[;,](?![^=;,]*(?:[;,]|$))/):a.split(",");return Re(s,(i,l)=>{const c=l.split("="),d=c[0],u=c.length>1?c[1]:d;return i[Wt(d)]=Wt(u),i},{})})(n);return{value:ce(r,t.id).getOr(e),valid:o}}return{value:n,valid:o}}return{valid:!1,message:"Must be a string."}},Lk=yt("iframe_attrs"),Ik=yt("doctype"),Ah=yt("document_base_url"),Fk=yt("body_id"),Uk=yt("body_class"),Th=yt("content_security_policy"),jk=yt("br_in_pre"),Bn=yt("forced_root_block"),Yi=yt("forced_root_block_attrs"),zk=yt("newline_behavior"),Hk=yt("br_newline_selector"),$k=yt("no_newline_selector"),qk=yt("keep_styles"),Vk=yt("end_container_on_empty_block"),Oh=yt("automatic_uploads"),Bh=yt("images_reuse_filename"),Wk=yt("images_replace_blob_uris"),Dh=yt("icons"),Kk=yt("icons_url"),Gk=yt("images_upload_url"),Yk=yt("images_upload_base_path"),Xk=yt("images_upload_credentials"),Qk=yt("images_upload_handler"),Jk=yt("content_css_cors"),Ku=yt("referrer_policy"),Ph=yt("language"),Zk=yt("language_url"),Mh=yt("indent_use_margin"),tE=yt("indentation"),eE=yt("content_css"),nE=yt("content_style"),Lh=yt("font_css"),oE=yt("directionality"),rE=yt("inline_boundaries_selector"),Ih=yt("object_resizing"),aE=yt("resize_img_proportional"),sE=yt("placeholder"),Fh=yt("event_root"),iE=yt("service_message"),$s=yt("theme"),lE=yt("theme_url"),Gu=yt("model"),cE=yt("model_url"),Xi=yt("inline_boundaries"),dE=yt("formats"),uE=yt("preview_styles"),mE=yt("format_empty_lines"),gE=yt("format_noneditable_selector"),fE=yt("custom_ui_selector"),Yu=yt("inline"),pE=yt("hidden_input"),hE=yt("submit_patch"),bE=yt("add_form_submit_trigger"),vE=yt("add_unload_trigger"),yE=yt("custom_undo_redo_levels"),CE=yt("disable_nodechange"),Uh=yt("readonly"),wE=yt("editable_root"),jh=yt("content_css_cors"),Tc=yt("plugins"),xE=yt("external_plugins"),SE=yt("block_unsupported_drop"),kE=yt("visual"),EE=yt("visual_table_class"),zh=yt("visual_anchor_class"),_E=yt("iframe_aria_text"),NE=yt("setup"),RE=yt("init_instance_callback"),AE=yt("urlconverter_callback"),TE=yt("auto_focus"),OE=yt("browser_spellcheck"),BE=yt("protect"),DE=yt("paste_block_drop"),Oc=yt("paste_data_images"),PE=yt("paste_preprocess"),ME=yt("paste_postprocess"),LE=yt("newdocument_content"),IE=yt("paste_webkit_styles"),FE=yt("paste_remove_styles_if_webkit"),UE=yt("paste_merge_formats"),jE=yt("smart_paste"),zE=yt("paste_as_text"),HE=yt("paste_tab_spaces"),$E=yt("allow_html_data_urls"),qE=yt("text_patterns"),VE=yt("text_patterns_lookup"),Hh=yt("noneditable_class"),WE=yt("editable_class"),KE=yt("noneditable_regexp"),GE=yt("preserve_cdata"),YE=yt("highlight_on_focus"),Xu=yt("xss_sanitization"),XE=yt("init_content_sync"),$h=t=>ot.explode(t.options.get("images_file_types")),QE=yt("table_tab_navigation"),JE=yt("details_initial_state"),ZE=yt("details_serialized_state"),t_=it,qh=tt,Vh=t=>{const e=t.parentNode;e&&e.removeChild(t)},Wh=t=>{const e=Fr(t);return{count:t.length-e.length,text:e}},Kh=t=>{let e;for(;-1!==(e=t.data.lastIndexOf(dn));)t.deleteData(e,1)},Gh=(t,e)=>(ts(t),e),ts=t=>{t_(t)&&no(t)&&(pp(t)?t.removeAttribute("data-mce-caret"):Vh(t)),qh(t)&&(Kh(t),0===t.data.length&&Vh(t))},n_=ve,o_=nr,r_=dc,Yh=(t,e,n)=>{const o=Zp(e.getBoundingClientRect(),n);let r,a;if("BODY"===t.tagName){const i=t.ownerDocument.documentElement;r=t.scrollLeft||i.scrollLeft,a=t.scrollTop||i.scrollTop}else{const i=t.getBoundingClientRect();r=t.scrollLeft-i.left,a=t.scrollTop-i.top}o.left+=r,o.right+=r,o.top+=a,o.bottom+=a,o.width=1;let s=e.offsetWidth-e.clientWidth;return s>0&&(n&&(s*=-1),o.left+=s,o.right+=s),o},Xh=()=>Jt.browser.isFirefox(),Qi=t=>n_(t)||o_(t),Ji=t=>(Qi(t)||Ir(t)&&Xh())&&_i(A(t)).exists(aa),s_=Po,Qu=ve,i_=nr,Qh=So("display","block table table-cell table-caption list-item"),Jh=no,Zh=Fs,l_=it,c_=tt,d_=ko,qs=t=>t>0,es=t=>t<0,Bc=(t,e)=>{let n;for(;n=t(e);)if(!Zh(n))return n;return null},Zi=(t,e,n,o,r)=>{const a=new Kt(t,o),s=Qu(t)||Zh(t);let i;if(es(e)){if(s&&(i=Bc(a.prev.bind(a),!0),n(i)))return i;for(;i=Bc(a.prev.bind(a),r);)if(n(i))return i}if(qs(e)){if(s&&(i=Bc(a.next.bind(a),!0),n(i)))return i;for(;i=Bc(a.next.bind(a),r);)if(n(i))return i}return null},pa=(t,e)=>{for(;t&&t!==e;){if(Qh(t))return t;t=t.parentNode}return null},rr=(t,e,n)=>pa(t.container(),n)===pa(e.container(),n),Ju=(t,e)=>{if(!e)return S.none();const n=e.container(),o=e.offset();return l_(n)?S.from(n.childNodes[o+t]):S.none()},tb=(t,e)=>{var n;const o=(null!==(n=e.ownerDocument)&&void 0!==n?n:document).createRange();return t?(o.setStartBefore(e),o.setEndBefore(e)):(o.setStartAfter(e),o.setEndAfter(e)),o},u_=(t,e,n)=>pa(e,t)===pa(n,t),eb=(t,e,n)=>{const o=t?"previousSibling":"nextSibling";let r=n;for(;r&&r!==e;){let a=r[o];if(a&&Jh(a)&&(a=a[o]),Qu(a)||i_(a)){if(u_(e,a,r))return a;break}if(d_(a))break;r=r.parentNode}return null},tl=Ct(tb,!0),el=Ct(tb,!1),Zu=(t,e,n)=>{let o;const r=Ct(eb,!0,e),a=Ct(eb,!1,e),s=n.startContainer,i=n.startOffset;if(Fs(s)){const l=c_(s)?s.parentNode:s,c=l.getAttribute("data-mce-caret");if("before"===c&&(o=l.nextSibling,Ji(o)))return tl(o);if("after"===c&&(o=l.previousSibling,Ji(o)))return el(o)}if(!n.collapsed)return n;if(tt(s)){if(Jh(s)){if(1===t){if(o=a(s),o)return tl(o);if(o=r(s),o)return el(o)}if(-1===t){if(o=r(s),o)return el(o);if(o=a(s),o)return tl(o)}return n}if(bc(s)&&i>=s.data.length-1)return 1===t&&(o=a(s),o)?tl(o):n;if(hc(s)&&i<=1)return-1===t&&(o=r(s),o)?el(o):n;if(i===s.data.length)return o=a(s),o?tl(o):n;if(0===i)return o=r(s),o?el(o):n}return n},nb=(t,e)=>Ju(t?0:-1,e).filter(Qu),nl=(t,e,n)=>{const o=Zu(t,e,n);return-1===t?$.fromRangeStart(o):$.fromRangeEnd(o)},Dc=t=>S.from(t.getNode()).map(A),tm=(t,e)=>{let n=e;for(;n=t(n);)if(n.isVisible())return n;return n},Pc=(t,e)=>{const n=rr(t,e);return!(n||!_e(t.getNode()))||n};var Xe,t;(t=Xe||(Xe={}))[t.Backwards=-1]="Backwards",t[t.Forwards=1]="Forwards";const m_=ve,ar=tt,ob=it,em=_e,Vs=ko,rb=t=>{return vp(t)||!!Cu(e=t)&&!Re(Ae(e.getElementsByTagName("*")),(n,o)=>n||bp(o),!1);var e},Mc=yp,ab=(t,e)=>t.hasChildNodes()&&e<t.childNodes.length?t.childNodes[e]:null,sb=(t,e)=>{if(qs(t)){if(Vs(e.previousSibling)&&!ar(e.previousSibling))return $.before(e);if(ar(e))return $(e,0)}if(es(t)){if(Vs(e.nextSibling)&&!ar(e.nextSibling))return $.after(e);if(ar(e))return $(e,e.data.length)}return es(t)?em(e)?$.before(e):$.after(e):$.before(e)},nm=(t,e,n)=>{let o,r,a,s;if(!ob(n)||!e)return null;if(e.isEqual($.after(n))&&n.lastChild){if(s=$.after(n.lastChild),es(t)&&Vs(n.lastChild)&&ob(n.lastChild))return em(n.lastChild)?$.before(n.lastChild):s}else s=e;const i=s.container();let l=s.offset();if(ar(i)){if(es(t)&&l>0)return $(i,--l);if(qs(t)&&l<i.length)return $(i,++l);o=i}else{if(es(t)&&l>0&&(r=ab(i,l-1),Vs(r)))return!rb(r)&&(a=Zi(r,t,Mc,r),a)?ar(a)?$(a,a.data.length):$.after(a):ar(r)?$(r,r.data.length):$.before(r);if(qs(t)&&l<i.childNodes.length&&(r=ab(i,l),Vs(r)))return em(r)?((d,u)=>{const m=u.nextSibling;return m&&Vs(m)?ar(m)?$(m,0):$.before(m):nm(Xe.Forwards,$.after(u),d)})(n,r):!rb(r)&&(a=Zi(r,t,Mc,r),a)?ar(a)?$(a,0):$.before(a):ar(r)?$(r,0):$.after(r);o=r||s.getNode()}if(o&&(qs(t)&&s.isAtEnd()||es(t)&&s.isAtStart())&&(o=Zi(o,t,ye,n,!0),Mc(o,n)))return sb(t,o);r=o&&Zi(o,t,Mc,n);const c=Fn(Ot(((d,u)=>{const m=[];let p=d;for(;p&&p!==u;)m.push(p),p=p.parentNode;return m})(i,n),m_));return!c||r&&c.contains(r)?r?sb(t,r):null:(s=qs(t)?$.after(c):$.before(c),s)},sr=t=>({next:e=>nm(Xe.Forwards,e,t),prev:e=>nm(Xe.Backwards,e,t)}),ib=t=>$.isTextPosition(t)?0===t.offset():ko(t.getNode()),lb=t=>{if($.isTextPosition(t)){const e=t.container();return t.offset()===e.data.length}return ko(t.getNode(!0))},cb=(t,e)=>!$.isTextPosition(t)&&!$.isTextPosition(e)&&t.getNode()===e.getNode(!0),oo=(t,e,n)=>{const o=sr(e);return S.from(t?o.next(n):o.prev(n))},ol=(t,e,n)=>oo(t,e,n).bind(o=>{return rr(n,o,e)&&(a=n,s=o,t?!cb(a,s)&&($.isTextPosition(i=a)||!_e(i.getNode()))&&lb(a)&&ib(s):!cb(s,a)&&ib(a)&&lb(s))?oo(t,e,o):S.some(o);var a,s,i}),db=(t,e,n,o)=>ol(t,e,n).bind(r=>o(r)?db(t,e,r,o):S.some(r)),Ws=(t,e)=>{const n=t?e.firstChild:e.lastChild;return tt(n)?S.some($(n,t?0:n.data.length)):n?ko(n)?S.some(t?$.before(n):_e(o=n)?$.before(o):$.after(o)):((r,a,s)=>{const i=r?$.before(s):$.after(s);return oo(r,a,i)})(t,e,n):S.none();var o},yn=Ct(oo,!0),Dn=Ct(oo,!1),Cn=Ct(Ws,!0),Eo=Ct(Ws,!1),om="_mce_caret",_o=t=>it(t)&&t.id===om,ns=(t,e)=>{let n=e;for(;n&&n!==t;){if(_o(n))return n;n=n.parentNode}return null},ub=t=>It(t,"name"),mb=t=>ot.isArray(t.start),Lc=t=>!(!ub(t)&&xn(t.forward))||t.forward,gb=(t,e)=>(it(e)&&t.isBlock(e)&&!e.innerHTML&&(e.innerHTML='<br data-mce-bogus="1" />'),e),g_=(t,e)=>Eo(t).fold(de,n=>(e.setStart(n.container(),n.offset()),e.setEnd(n.container(),n.offset()),!0)),fb=(t,e,n)=>!(e.hasChildNodes()||!ns(t,e)||(((o,r)=>{var a;const s=(null!==(a=o.ownerDocument)&&void 0!==a?a:document).createTextNode(dn);o.appendChild(s),r.setStart(s,0),r.setEnd(s,0)})(e,n),0)),pb=(t,e,n,o)=>{const r=n[e?"start":"end"],a=t.getRoot();if(r){let s=a,i=r[0];for(let l=r.length-1;s&&l>=1;l--){const c=s.childNodes;if(fb(a,s,o))return!0;if(r[l]>c.length-1)return!!fb(a,s,o)||g_(s,o);s=c[r[l]]}tt(s)&&(i=Math.min(r[0],s.data.length)),it(s)&&(i=Math.min(r[0],s.childNodes.length)),e?o.setStart(s,i):o.setEnd(s,i)}return!0},rm=t=>tt(t)&&t.data.length>0,hb=(t,e,n)=>{const o=t.get(n.id+"_"+e),r=o?.parentNode,a=n.keep;if(o&&r){let s,i;if("start"===e?a?o.hasChildNodes()?(s=o.firstChild,i=1):rm(o.nextSibling)?(s=o.nextSibling,i=0):rm(o.previousSibling)?(s=o.previousSibling,i=o.previousSibling.data.length):(s=r,i=t.nodeIndex(o)+1):(s=r,i=t.nodeIndex(o)):a?o.hasChildNodes()?(s=o.firstChild,i=1):rm(o.previousSibling)?(s=o.previousSibling,i=o.previousSibling.data.length):(s=r,i=t.nodeIndex(o)):(s=r,i=t.nodeIndex(o)),!a){const l=o.previousSibling,c=o.nextSibling;let d;for(ot.each(ot.grep(o.childNodes),u=>{tt(u)&&(u.data=u.data.replace(/\uFEFF/g,""))});d=t.get(n.id+"_"+e);)t.remove(d,!0);if(tt(c)&&tt(l)&&!Jt.browser.isOpera()){const u=l.data.length;l.appendData(c.data),t.remove(c),s=l,i=u}}return S.some($(s,i))}return S.none()},f_=(t,e,n)=>((o,r,a=!1)=>{return 2===r?yh(Fr,a,o):3===r?(s=>{const i=s.getRng();return{start:gh(s.dom.getRoot(),$.fromRangeStart(i)),end:gh(s.dom.getRoot(),$.fromRangeEnd(i)),forward:s.isForward()}})(o):r?{rng:(s=o).getRng(),forward:s.isForward()}:wh(o,!1);var s})(t,e,n),p_=(t,e)=>{((n,o)=>{const r=n.dom;if(o){if(mb(o))return((a,s)=>{const i=a.createRng();return pb(a,!0,s,i)&&pb(a,!1,s,i)?S.some({range:i,forward:Lc(s)}):S.none()})(r,o);if(Nt(o.start))return((a,s)=>{const i=S.from(fh(a.getRoot(),s.start)),l=S.from(fh(a.getRoot(),s.end));return on(i,l,(c,d)=>{const u=a.createRng();return u.setStart(c.container(),c.offset()),u.setEnd(d.container(),d.offset()),{range:u,forward:Lc(s)}})})(r,o);if(It(o,"id"))return((a,s)=>{const i=hb(a,"start",s),l=hb(a,"end",s);return on(i,l.or(i),(c,d)=>{const u=a.createRng();return u.setStart(gb(a,c.container()),c.offset()),u.setEnd(gb(a,d.container()),d.offset()),{range:u,forward:Lc(s)}})})(r,o);if(ub(o))return((a,s)=>S.from(a.select(s.name)[s.index]).map(i=>{const l=a.createRng();return l.selectNode(i),{range:l,forward:!0}}))(r,o);if((a=>It(a,"rng"))(o))return S.some({range:o.rng,forward:Lc(o)})}return S.none()})(t,e).each(({range:n,forward:o})=>{t.setRng(n,o)})},ro=t=>it(t)&&"SPAN"===t.tagName&&"bookmark"===t.getAttribute("data-mce-type"),Ic=(bb=Ge,t=>bb===t);var bb;const Ks=t=>""!==t&&-1!==" \f\n\r\t\v".indexOf(t),am=t=>!Ks(t)&&!Ic(t)&&!fu(t),sm=t=>{const e=t.toString(16);return(1===e.length?"0"+e:e).toUpperCase()},h_=t=>{return{value:(n=sm(t.red)+sm(t.green)+sm(t.blue),Q(n,"#").toUpperCase())};var n},b_=/^\s*rgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)\s*$/i,v_=/^\s*rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d?(?:\.\d+)?)\s*\)\s*$/i,vb=(t,e,n,o)=>({red:t,green:e,blue:n,alpha:o}),yb=(t,e,n,o)=>{const r=parseInt(t,10),a=parseInt(e,10),s=parseInt(n,10),i=parseFloat(o);return vb(r,a,s,i)},Fc=t=>(e=>{if("transparent"===e)return S.some(vb(0,0,0,0));const n=b_.exec(e);if(null!==n)return S.some(yb(n[1],n[2],n[3],"1"));const o=v_.exec(e);return null!==o?S.some(yb(o[1],o[2],o[3],o[4])):S.none()})(t).map(h_).map(e=>"#"+e.value).getOr(t),im=t=>{const e=[];if(t)for(let n=0;n<t.rangeCount;n++)e.push(t.getRangeAt(n));return e},Cb=(t,e)=>{const n=Rt(e,"td[data-mce-selected],th[data-mce-selected]");return n.length>0?n:Ot(gn(t,a=>{const s=_c(a);return s?[A(s)]:[]}),Mi)},Gs=t=>Cb(im(t.selection.getSel()),A(t.getBody())),rl=(t,e)=>Fa(t,"table",e),wb=t=>Ri(t).fold(ut([t]),e=>[t].concat(wb(e))),lm=t=>Ai(t).fold(ut([t]),e=>"br"===ge(e)?Mr(e).map(n=>[t].concat(lm(n))).getOr([]):[t].concat(lm(e))),cm=(t,e)=>on((n=>{const o=n.startContainer,r=n.startOffset;return tt(o)?0===r?S.some(A(o)):S.none():S.from(o.childNodes[r]).map(A)})(e),(n=>{const o=n.endContainer,r=n.endOffset;return tt(o)?r===o.data.length?S.some(A(o)):S.none():S.from(o.childNodes[r-1]).map(A)})(e),(n,o)=>{const r=he(wb(t),Ct(re,n)),a=he(lm(t),Ct(re,o));return r.isSome()&&a.isSome()}).getOr(!1),dm=(t,e,n,o)=>{const r=n,a=new Kt(n,r),s=Go(t.schema.getMoveCaretBeforeOnEnterElements(),(l,c)=>!Lt(["td","th","table"],c.toLowerCase()));let i=n;do{if(tt(i)&&0!==ot.trim(i.data).length)return void(o?e.setStart(i,0):e.setEnd(i,i.data.length));if(s[i.nodeName])return void(o?e.setStartBefore(i):"BR"===i.nodeName?e.setEndBefore(i):e.setEndAfter(i))}while(i=o?a.next():a.prev());"BODY"===r.nodeName&&(o?e.setStart(r,0):e.setEnd(r,r.childNodes.length))},Uc=t=>{const e=t.selection.getSel();return st(e)&&e.rangeCount>0},um=(t,e)=>{const n=Gs(t);n.length>0?J(n,o=>{const r=o.dom,a=t.dom.createRng();a.setStartBefore(r),a.setEndAfter(r),e(a,!0)}):e(t.selection.getRng(),!1)},xb=(t,e,n)=>{const o=wh(t,e);n(o),t.moveToBookmark(o)},al=t=>nn(t?.nodeType),mm=t=>it(t)&&!ro(t)&&!_o(t)&&!$a(t),os=t=>!0===t.isContentEditable,Sb=(t,e,n)=>{const{selection:o,dom:r}=t,a=o.getNode(),s=ve(a);xb(o,!0,()=>{e()}),s&&ve(a)&&r.isChildOf(a,t.getBody())?t.selection.select(a):n(o.getStart())&&y_(r,o)},y_=(t,e)=>{var n,o;const r=e.getRng(),{startContainer:a,startOffset:s}=r;if(!((i,l)=>{if(mm(l)&&!/^(TD|TH)$/.test(l.nodeName)){const c=i.getAttrib(l,"data-mce-selected"),d=parseInt(c,10);return!isNaN(d)&&d>0}return!1})(t,e.getNode())&&it(a)){const i=a.childNodes,l=t.getRoot();let c;if(s<i.length){const d=i[s];c=new Kt(d,null!==(n=t.getParent(d,t.isBlock))&&void 0!==n?n:l)}else{const d=i[i.length-1];c=new Kt(d,null!==(o=t.getParent(d,t.isBlock))&&void 0!==o?o:l),c.next(!0)}for(let d=c.current();d;d=c.next()){if("false"===t.getContentEditable(d))return;if(tt(d)&&!fm(d))return r.setStart(d,0),void e.setRng(r)}}},kb=(t,e,n)=>{if(t){const o=e?"nextSibling":"previousSibling";for(t=n?t:t[o];t;t=t[o])if(it(t)||!fm(t))return t}},gm=(t,e)=>!!t.getTextBlockElements()[e.nodeName.toLowerCase()]||Wa(t,e),Ys=(t,e,n)=>t.schema.isValidChild(e,n),fm=(t,e=!1)=>{if(st(t)&&tt(t)){const n=e?t.data.replace(/ /g,"\xa0"):t.data;return Va(n)}return!1},Eb=(t,e)=>{const n=t.dom;return mm(e)&&"false"===n.getContentEditable(e)&&((o,r)=>{const a="[data-mce-cef-wrappable]",s=gE(o),i=Xt(s)?a:`${a},${s}`;return hn(A(r),i)})(t,e)&&0===n.select('[contenteditable="true"]',e).length},ha=(t,e)=>Yt(t)?t(e):(st(e)&&(t=t.replace(/%(\w+)/g,(n,o)=>e[o]||n)),t),pm=(t,e)=>(t=""+((t=t||"").nodeName||t),e=""+((e=e||"").nodeName||e),t.toLowerCase()===e.toLowerCase()),hm=(t,e)=>{if(pe(t))return null;{let n=String(t);return"color"!==e&&"backgroundColor"!==e||(n=Fc(n)),"fontWeight"===e&&700===t&&(n="bold"),"fontFamily"===e&&(n=n.replace(/[\'\"]/g,"").replace(/,\s+/g,",")),n}},jc=(t,e,n)=>{const o=t.getStyle(e,n);return hm(o,n)},_b=(t,e)=>{let n;return t.getParent(e,o=>!!it(o)&&(n=t.getStyle(o,"text-decoration"),!!n&&"none"!==n)),n},zc=(t,e,n)=>t.getParents(e,n,t.getRoot()),bm=(t,e,n)=>{const o=t.formatter.get(e);return st(o)&&ie(o,n)},ir=t=>Hn(t,"block"),ao=t=>Hn(t,"selector"),$e=t=>Hn(t,"inline"),vm=t=>ao(t)&&!1!==t.expand&&!$e(t),Xs=ro,Nb=zc,Rb=fm,C_=gm,Ab=(t,e)=>{let n=e;for(;n;){if(it(n)&&t.getContentEditable(n))return"false"===t.getContentEditable(n)?n:e;n=n.parentNode}return e},Tb=(t,e,n,o)=>{const r=e.data;if(t){for(let a=n;a>0;a--)if(o(r.charAt(a-1)))return a}else for(let a=n;a<r.length;a++)if(o(r.charAt(a)))return a;return-1},w_=(t,e,n)=>Tb(t,e,n,o=>Ic(o)||Ks(o)),x_=(t,e,n)=>Tb(t,e,n,am),Ob=(t,e,n,o,r,a)=>{let s;const i=t.getParent(n,t.isBlock)||e,l=(c,d,u)=>{const m=Qa(t);return S.from((r?m.backwards:m.forwards)(c,d,(g,h)=>Xs(g.parentNode)?-1:(s=g,u(r,g,h)),i))};return l(n,o,w_).bind(c=>a?l(c.container,c.offset+(r?-1:0),x_):S.some(c)).orThunk(()=>s?S.some({container:s,offset:r?0:s.length}):S.none())},Bb=(t,e,n,o,r)=>{const a=o[r];tt(o)&&Xt(o.data)&&a&&(o=a);const s=Nb(t,o);for(let i=0;i<s.length;i++)for(let l=0;l<e.length;l++){const c=e[l];if((!st(c.collapsed)||c.collapsed===n.collapsed)&&ao(c)&&t.is(s[i],c.selector))return s[i]}return o},Db=(t,e,n,o)=>{var r;let a=n;const s=t.getRoot(),i=e[0];if(ir(i)&&(a=i.wrapper?null:t.getParent(n,i.block,s)),!a){const l=null!==(r=t.getParent(n,"LI,TD,TH"))&&void 0!==r?r:s;a=t.getParent(tt(n)?n.parentNode:n,c=>c!==s&&C_(t.schema,c),l)}if(a&&ir(i)&&i.wrapper&&(a=Nb(t,a,"ul,ol").reverse()[0]||a),!a)for(a=n;a&&a[o]&&!t.isBlock(a[o])&&(a=a[o],!pm(a,"br")););return a||n},Pb=(t,e,n,o)=>{const r=n.parentNode;return!st(n[o])&&(!(r!==e&&!pe(r)&&!t.isBlock(r))||Pb(t,e,r,o))},Hc=(t,e,n,o,r)=>{let a=n;const s=r?"previousSibling":"nextSibling",i=t.getRoot();if(tt(n)&&!Rb(n)&&(r?o>0:o<n.data.length))return n;for(;a;){if(!e[0].block_expand&&t.isBlock(a))return a;for(let c=a[s];c;c=c[s]){const d=tt(c)&&!Pb(t,i,c,s);if(!Xs(c)&&(!_e(l=c)||!l.getAttribute("data-mce-bogus")||l.nextSibling)&&!Rb(c,d))return a}if(a===i||a.parentNode===i){n=a;break}a=a.parentNode}var l;return n},Mb=t=>Xs(t.parentNode)||Xs(t),rs=(t,e,n,o=!1)=>{let{startContainer:r,startOffset:a,endContainer:s,endOffset:i}=e;const l=n[0];return it(r)&&r.hasChildNodes()&&(r=ga(r,a),tt(r)&&(a=0)),it(s)&&s.hasChildNodes()&&(s=ga(s,e.collapsed?i:i-1),tt(s)&&(i=s.data.length)),r=Ab(t,r),s=Ab(t,s),Mb(r)&&(r=Xs(r)?r:r.parentNode,r=e.collapsed?r.previousSibling||r:r.nextSibling||r,tt(r)&&(a=e.collapsed?r.length:0)),Mb(s)&&(s=Xs(s)?s:s.parentNode,s=e.collapsed?s.nextSibling||s:s.previousSibling||s,tt(s)&&(i=e.collapsed?0:s.length)),e.collapsed&&(Ob(t,t.getRoot(),r,a,!0,o).each(({container:c,offset:d})=>{r=c,a=d}),Ob(t,t.getRoot(),s,i,!1,o).each(({container:c,offset:d})=>{s=c,i=d})),($e(l)||l.block_expand)&&($e(l)&&tt(r)&&0!==a||(r=Hc(t,n,r,a,!0)),$e(l)&&tt(s)&&i!==s.data.length||(s=Hc(t,n,s,i,!1))),vm(l)&&(r=Bb(t,n,e,r,"previousSibling"),s=Bb(t,n,e,s,"nextSibling")),(ir(l)||ao(l))&&(r=Db(t,n,r,"previousSibling"),s=Db(t,n,s,"nextSibling"),ir(l)&&(t.isBlock(r)||(r=Hc(t,n,r,a,!0)),t.isBlock(s)||(s=Hc(t,n,s,i,!1)))),it(r)&&r.parentNode&&(a=t.nodeIndex(r),r=r.parentNode),it(s)&&s.parentNode&&(i=t.nodeIndex(s)+1,s=s.parentNode),{startContainer:r,startOffset:a,endContainer:s,endOffset:i}},sl=(t,e,n)=>{var o;const r=e.startOffset,a=ga(e.startContainer,r),s=e.endOffset,i=ga(e.endContainer,s-1),l=f=>{const b=f[0];tt(b)&&b===a&&r>=b.data.length&&f.splice(0,1);const y=f[f.length-1];return 0===s&&f.length>0&&y===i&&tt(y)&&f.splice(f.length-1,1),f},c=(f,b,y)=>{const C=[];for(;f&&f!==y;f=f[b])C.push(f);return C},d=(f,b)=>t.getParent(f,y=>y.parentNode===b,b),u=(f,b,y)=>{const C=y?"nextSibling":"previousSibling";for(let k=f,E=k.parentNode;k&&k!==b;k=E){E=k.parentNode;const N=c(k===f?k:k[C],C);N.length&&(y||N.reverse(),n(l(N)))}};if(a===i)return n(l([a]));const m=null!==(o=t.findCommonAncestor(a,i))&&void 0!==o?o:t.getRoot();if(t.isChildOf(a,i))return u(a,m,!0);if(t.isChildOf(i,a))return u(i,m);const p=d(a,m)||a,g=d(i,m)||i;u(a,p,!0);const h=c(p===a?p:p.nextSibling,"nextSibling",g===i?g.nextSibling:g);h.length&&n(l(h)),u(i,g)},S_=['pre[class*=language-][contenteditable="false"]',"figure.image","div[data-ephox-embed-iri]","div.tiny-pageembed","div.mce-toc","div[data-mce-toc]"],Lb=(t,e,n,o,r,a)=>{const{uid:s=e,...i}=n;Jr(t,ji()),Me(t,`${zs()}`,s),Me(t,`${Ya()}`,o);const{attributes:l={},classes:c=[]}=r(s,i);if(Do(t,l),d=t,J(c,m=>{Jr(d,m)}),a){c.length>0&&Me(t,`${Hi()}`,c.join(","));const d=tn(l);d.length>0&&Me(t,`${$i()}`,d.join(","))}var d},Ib=(t,e,n,o,r)=>{const a=Be("span",t);return Lb(a,e,n,o,r,!1),a},Fb=t=>{const e=(()=>{const a={};return{register:(s,i)=>{a[s]={name:s,settings:i}},lookup:s=>ce(a,s).map(i=>i.settings),getNames:()=>tn(a)}})();((a,s)=>{const i=Ya(),l=d=>S.from(d.attr(i)).bind(s.lookup),c=d=>{var u,m;d.attr(zs(),null),d.attr(Ya(),null),d.attr(zi(),null);const p=S.from(d.attr($i())).map(b=>b.split(",")).getOr([]),g=S.from(d.attr(Hi())).map(b=>b.split(",")).getOr([]);J(p,b=>d.attr(b,null));const h=null!==(m=null===(u=d.attr("class"))||void 0===u?void 0:u.split(" "))&&void 0!==m?m:[],f=Gn(h,[ji()].concat(g));d.attr("class",f.length>0?f.join(" "):null),d.attr(Hi(),null),d.attr($i(),null)};a.serializer.addTempAttr(zi()),a.serializer.addAttributeFilter(i,d=>{for(const u of d)l(u).each(m=>{!1===m.persistent&&("span"===u.name?u.unwrap():c(u))})})})(t,e);const n=((a,s)=>{const i=Ye({}),l=()=>({listeners:[],previous:ma()}),c=(p,g)=>{d(p,h=>(g(h),h))},d=(p,g)=>{const h=i.get(),f=g(ce(h,p).getOrThunk(l));h[p]=f,i.set(h)},u=(p,g)=>{J(Wp(a,p),h=>{g?Me(h,zi(),"true"):Le(h,zi())})},m=Lu(()=>{const p=To(s.getNames());J(p,g=>{d(g,h=>{const f=h.previous.get();return qp(a,S.some(g)).fold(()=>{f.each(b=>{var y;c(y=g,C=>{J(C.listeners,k=>k(!1,y))}),h.previous.clear(),u(b,!1)})},({uid:b,name:y,elements:C})=>{var k,E,N;tr(f,b)||(f.each(k=>u(k,!1)),E=b,N=C,c(k=y,R=>{J(R.listeners,I=>I(!0,k,{uid:E,nodes:te(N,q=>q.dom)}))}),h.previous.set(b),u(b,!0))}),{previous:h.previous,listeners:h.listeners}})})},30);return a.on("remove",()=>{m.cancel()}),a.on("NodeChange",()=>{m.throttle()}),{addListener:(p,g)=>{d(p,h=>({previous:h.previous,listeners:h.listeners.concat([g])}))}}})(t,e),o=Pa("span"),r=a=>{J(a,s=>{o(s)?_(s):(i=>{Br(i,ji()),Le(i,`${zs()}`),Le(i,`${Ya()}`),Le(i,`${zi()}`);const l=Zn(i,`${$i()}`).map(u=>u.split(",")).getOr([]),c=Zn(i,`${Hi()}`).map(u=>u.split(",")).getOr([]);var d;J(l,u=>Le(i,u)),d=i,J(c,u=>{Br(d,u)}),Le(i,`${Hi()}`),Le(i,`${$i()}`)})(s)})};return{register:(a,s)=>{e.register(a,s)},annotate:(a,s)=>{e.lookup(a).each(i=>{var l,c,d,u;c=a,d=i,u=s,(l=t).undoManager.transact(()=>{const m=l.selection,p=m.getRng(),g=Gs(l).length>0,h=qi("mce-annotation");if(p.collapsed&&!g&&((f,b)=>{const y=rs(f.dom,b,[{inline:"span"}]);b.setStart(y.startContainer,y.startOffset),b.setEnd(y.endContainer,y.endOffset),f.selection.setRng(b)})(l,p),m.getRng().collapsed&&!g){const f=Ib(l.getDoc(),h,u,c,d.decorate);X(f,Ge),m.getRng().insertNode(f.dom),m.select(f.dom)}else xb(m,!1,()=>{um(l,f=>{((t,e,n,o,r,a)=>{const s=[],i=Ib(t.getDoc(),n,a,o,r),l=ma(),c=()=>{l.clear()},d=m=>{J(m,u)},u=m=>{switch(p=t,g=m,h="span",f=ge(m),Tn(g).fold(()=>"skipping",b=>{return"br"===f||pn(y=g)&&uc(y)===dn?"valid":(y=>fn(y)&&Ds(y,ji()))(g)?"existing":_o(g.dom)?"caret":ie(S_,y=>hn(g,y))?"valid-block":Ys(p,h,f)&&Ys(p,ge(b),h)?"valid":"invalid-child";var y})){case"invalid-child":{c();const p=ze(m);d(p),c();break}case"valid-block":c(),Lb(m,n,a,o,r,!0);break;case"valid":{const p=l.get().getOrThunk(()=>{const g=Xa(i);return s.push(g),l.set(g),g});Ms(m,p);break}}var p,g,h,f};sl(t.dom,e,m=>{c(),(p=>{const g=te(p,A);d(g)})(m)})})(l,f,h,c,d.decorate,u)})})})})},annotationChanged:(a,s)=>{n.addListener(a,s)},remove:a=>{qp(t,S.some(a)).each(({elements:s})=>{const i=t.selection.getBookmark();r(s),t.selection.moveToBookmark(i)})},removeAll:a=>{const s=t.selection.getBookmark();le(Kp(t,a),(i,l)=>{r(i)}),t.selection.moveToBookmark(s)},getAll:a=>{const s=Kp(t,a);return Yr(s,i=>te(i,l=>l.dom))}}},il=t=>({getBookmark:Ct(f_,t),moveToBookmark:Ct(p_,t)});il.isBookmarkNode=ro;const Ub=(t,e,n)=>!n.collapsed&&ie(n.getClientRects(),o=>{return s=e,(a=t)>=(r=o).left&&a<=r.right&&s>=r.top&&s<=r.bottom;var r,a,s}),jb=(t,e,n)=>{t.dispatch(e,n)},zb=(t,e,n,o)=>{t.dispatch("FormatApply",{format:e,node:n,vars:o})},Hb=(t,e,n,o)=>{t.dispatch("FormatRemove",{format:e,node:n,vars:o})},$b=(t,e)=>t.dispatch("SetContent",e),qb=(t,e)=>t.dispatch("GetContent",e),Vb=(t,e)=>t.dispatch("PastePlainTextToggle",{state:e}),wt={BACKSPACE:8,DELETE:46,DOWN:40,ENTER:13,ESC:27,LEFT:37,RIGHT:39,SPACEBAR:32,TAB:9,UP:38,PAGE_UP:33,PAGE_DOWN:34,END:35,HOME:36,modifierPressed:t=>t.shiftKey||t.ctrlKey||t.altKey||wt.metaKeyPressed(t),metaKeyPressed:t=>Jt.os.isMacOS()||Jt.os.isiOS()?t.metaKey:t.ctrlKey&&!t.altKey},as="data-mce-selected",Wb=Math.abs,$c=Math.round,ym={nw:[0,0,-1,-1],ne:[1,0,1,-1],se:[1,1,1,1],sw:[0,1,-1,1]},Kb=(t,e)=>{const n=e.dom,o=e.getDoc(),r=document,a=e.getBody();let s,i,l,c,d,u,m,p,g,h,f,b,y,C,k;const E=ft=>st(ft)&&(up(ft)||n.is(ft,"figure.image")),N=ft=>nr(ft)||n.hasClass(ft,"mce-preview-object"),R=ft=>{const kt=ft.target;((Bt,O)=>{if("longpress"===(D=Bt).type||0===D.type.indexOf("touch")){const D=Bt.touches[0];return E(Bt.target)&&!Ub(D.clientX,D.clientY,O)}var D;return E(Bt.target)&&!Ub(Bt.clientX,Bt.clientY,O)})(ft,e.selection.getRng())&&!ft.isDefaultPrevented()&&e.selection.select(kt)},I=ft=>n.hasClass(ft,"mce-preview-object")&&st(ft.firstElementChild)?[ft,ft.firstElementChild]:n.is(ft,"figure.image")?[ft.querySelector("img")]:[ft],q=ft=>{const kt=Ih(e);return!!kt&&"false"!==ft.getAttribute("data-mce-resize")&&ft!==e.getBody()&&(n.hasClass(ft,"mce-preview-object")&&st(ft.firstElementChild)?hn(A(ft.firstElementChild),kt):hn(A(ft),kt))},H=(ft,kt,Bt)=>{if(st(Bt)){const O=I(ft);J(O,D=>{D.style[kt]||!e.schema.isValid(D.nodeName.toLowerCase(),kt)?n.setStyle(D,kt,Bt):n.setAttrib(D,kt,""+Bt)})}},T=(ft,kt,Bt)=>{H(ft,"width",kt),H(ft,"height",Bt)},P=ft=>{let kt,Bt,O,D,V;kt=ft.screenX-u,Bt=ft.screenY-m,b=kt*c[2]+p,y=Bt*c[3]+g,b=b<5?5:b,y=y<5?5:y,O=(E(s)||N(s))&&!1!==aE(e)?!wt.modifierPressed(ft):wt.modifierPressed(ft),O&&(Wb(kt)>Wb(Bt)?(y=$c(b*h),b=$c(y/h)):(b=$c(y/h),y=$c(b*h))),T(i,b,y),D=c.startPos.x+kt,V=c.startPos.y+Bt,D=D>0?D:0,V=V>0?V:0,n.setStyles(l,{left:D,top:V,display:"block"}),l.innerHTML=b+" &times; "+y,c[2]<0&&i.clientWidth<=b&&n.setStyle(i,"left",void 0+(p-b)),c[3]<0&&i.clientHeight<=y&&n.setStyle(i,"top",void 0+(g-y)),kt=a.scrollWidth-C,Bt=a.scrollHeight-k,kt+Bt!==0&&n.setStyles(l,{left:D-kt,top:V-Bt}),f||(e.dispatch("ObjectResizeStart",{target:s,width:p,height:g,origin:"corner-"+c.name}),f=!0)},B=()=>{const ft=f;f=!1,ft&&(H(s,"width",b),H(s,"height",y)),n.unbind(o,"mousemove",P),n.unbind(o,"mouseup",B),r!==o&&(n.unbind(r,"mousemove",P),n.unbind(r,"mouseup",B)),n.remove(i),n.remove(l),n.remove(d),j(s),ft&&(e.dispatch("ObjectResized",{target:s,width:b,height:y,origin:"corner-"+c.name}),n.setAttrib(s,"style",n.getAttrib(s,"style"))),e.nodeChanged()},j=ft=>{Ut();const kt=n.getPos(ft,a),Bt=kt.x,O=kt.y,D=ft.getBoundingClientRect(),V=D.width||D.right-D.left,Y=D.height||D.bottom-D.top;s!==ft&&(Z(),s=ft,b=y=0);const W=e.dispatch("ObjectSelected",{target:ft});q(ft)&&!W.isDefaultPrevented()?le(ym,(K,at)=>{let St=n.get("mceResizeHandle"+at);St&&n.remove(St),St=n.add(a,"div",{id:"mceResizeHandle"+at,"data-mce-bogus":"all",class:"mce-resizehandle",unselectable:!0,style:"cursor:"+at+"-resize; margin:0; padding:0"}),n.bind(St,"mousedown",vt=>{vt.stopImmediatePropagation(),vt.preventDefault(),(Dt=>{const Mt=I(s)[0];var gt;u=Dt.screenX,m=Dt.screenY,p=Mt.clientWidth,g=Mt.clientHeight,h=g/p,c=K,c.name=at,c.startPos={x:V*K[0]+Bt,y:Y*K[1]+O},C=a.scrollWidth,k=a.scrollHeight,d=n.add(a,"div",{class:"mce-resize-backdrop","data-mce-bogus":"all"}),n.setStyles(d,{position:"fixed",left:"0",top:"0",width:"100%",height:"100%"}),i=N(gt=s)?n.create("img",{src:Jt.transparentSrc}):gt.cloneNode(!0),n.addClass(i,"mce-clonedresizable"),n.setAttrib(i,"data-mce-bogus","all"),i.contentEditable="false",n.setStyles(i,{left:Bt,top:O,margin:0}),T(i,V,Y),i.removeAttribute(as),a.appendChild(i),n.bind(o,"mousemove",P),n.bind(o,"mouseup",B),r!==o&&(n.bind(r,"mousemove",P),n.bind(r,"mouseup",B)),l=n.add(a,"div",{class:"mce-resize-helper","data-mce-bogus":"all"},p+" &times; "+g)})(vt)}),K.elm=St,n.setStyles(St,{left:V*K[0]+Bt-St.offsetWidth/2,top:Y*K[1]+O-St.offsetHeight/2})}):Z(!1)},lt=Ec(j,0),Z=(ft=!0)=>{lt.cancel(),Ut(),s&&ft&&s.removeAttribute(as),le(ym,(kt,Bt)=>{const O=n.get("mceResizeHandle"+Bt);O&&(n.unbind(O),n.remove(O))})},_t=(ft,kt)=>n.isChildOf(ft,kt),mt=ft=>{if(f||e.removed||e.composing)return;const kt="mousedown"===ft.type?ft.target:t.getNode(),Bt=er(A(kt),"table,img,figure.image,hr,video,span.mce-preview-object,details").map(D=>D.dom).filter(D=>n.isEditable(D.parentElement)).getOrUndefined(),O=st(Bt)?n.getAttrib(Bt,as,"1"):"1";if(J(n.select(`img[${as}],hr[${as}]`),D=>{D.removeAttribute(as)}),st(Bt)&&_t(Bt,a)&&e.hasFocus()){Ht();const D=t.getStart(!0);if(_t(D,Bt)&&_t(t.getEnd(!0),Bt))return n.setAttrib(Bt,as,O),void lt.throttle(Bt)}Z()},Ut=()=>{le(ym,ft=>{ft.elm&&(n.unbind(ft.elm),delete ft.elm)})},Ht=()=>{try{e.getDoc().execCommand("enableObjectResizing",!1,"false")}catch{}};return e.on("init",()=>{Ht(),e.on("NodeChange ResizeEditor ResizeWindow ResizeContent drop",mt),e.on("keyup compositionend",ft=>{s&&"TABLE"===s.nodeName&&mt(ft)}),e.on("hide blur",Z),e.on("contextmenu longpress",R,!0)}),e.on("remove",Ut),{isResizable:q,showResizeRect:j,hideResizeRect:Z,updateResizeRect:mt,destroy:()=>{lt.cancel(),s=i=d=null}}},Gb=(t,e,n)=>{const o=t.document.createRange();var r,a;return r=o,e.fold(a=>{r.setStartBefore(a.dom)},(a,s)=>{r.setStart(a.dom,s)},a=>{r.setStartAfter(a.dom)}),a=o,n.fold(i=>{a.setEndBefore(i.dom)},(i,l)=>{a.setEnd(i.dom,l)},i=>{a.setEndAfter(i.dom)}),o},Cm=(t,e,n,o,r)=>{const a=t.document.createRange();return a.setStart(e.dom,n),a.setEnd(o.dom,r),a},ll=or([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),Yb=(t,e,n)=>e(A(n.startContainer),n.startOffset,A(n.endContainer),n.endOffset),Xb=(t,e,n,o)=>({start:t,soffset:e,finish:n,foffset:o}),E_=document.caretPositionFromPoint?(t,e,n)=>{var o,r;return S.from(null===(r=(o=t.dom).caretPositionFromPoint)||void 0===r?void 0:r.call(o,e,n)).bind(a=>{if(null===a.offsetNode)return S.none();const s=t.dom.createRange();return s.setStart(a.offsetNode,a.offset),s.collapse(),S.some(s)})}:document.caretRangeFromPoint?(t,e,n)=>{var o,r;return S.from(null===(r=(o=t.dom).caretRangeFromPoint)||void 0===r?void 0:r.call(o,e,n))}:S.none,wm=or([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),jr={before:wm.before,on:wm.on,after:wm.after,cata:(t,e,n,o)=>t.fold(e,n,o),getStart:t=>t.fold(qe,qe,qe)},qc=or([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),Qs={domRange:qc.domRange,relative:qc.relative,exact:qc.exact,exactFromRange:t=>qc.exact(t.start,t.soffset,t.finish,t.foffset),getWin:t=>{const e=t.match({domRange:o=>A(o.startContainer),relative:(o,r)=>jr.getStart(o),exact:(o,r,a,s)=>o});return Pr(e)},range:Xb},Vc=(t,e)=>{const n=ge(t);return"input"===n?jr.after(t):Lt(["br","img"],n)?0===e?jr.before(t):jr.after(t):jr.on(t,e)},__=(t,e)=>{const n=t.fold(jr.before,Vc,jr.after),o=e.fold(jr.before,Vc,jr.after);return Qs.relative(n,o)},Qb=(t,e,n,o)=>{const r=Vc(t,e),a=Vc(n,o);return Qs.relative(r,a)},xm=(t,e)=>{const n=(e||document).createDocumentFragment();return J(t,o=>{n.appendChild(o.dom)}),A(n)},Jb=(t,e,n)=>{return(o=Pr(A(n)).dom,r=t,a=e,((s,i,l)=>{const c=A(s.document);return E_(c,i,l).map(d=>Xb(A(d.startContainer),d.startOffset,A(d.endContainer),d.endOffset))})(o,r,a)).map(o=>{const r=n.createRange();return r.setStart(o.start.dom,o.soffset),r.setEnd(o.finish.dom,o.foffset),r}).getOrUndefined();var o,r,a},Sm=(t,e)=>st(t)&&st(e)&&t.startContainer===e.startContainer&&t.startOffset===e.startOffset&&t.endContainer===e.endContainer&&t.endOffset===e.endOffset,Zb=(t,e,n)=>null!==((o,r,a)=>{let s=o;for(;s&&s!==r;){if(a(s))return s;s=s.parentNode}return null})(t,e,n),R_=(t,e,n)=>Zb(t,e,o=>o.nodeName===n),A_=(t,e)=>no(t)&&!Zb(t,e,_o),tv=(t,e,n)=>{const o=e.parentNode;if(o){const r=new Kt(e,t.getParent(o,t.isBlock)||t.getRoot());let a;for(;a=r[n?"prev":"next"]();)if(_e(a))return!0}return!1},km=(t,e,n,o,r)=>{const a=t.getRoot(),s=t.schema.getNonEmptyElements(),i=r.parentNode;let l,c;if(!i)return S.none();const d=t.getParent(i,t.isBlock)||a;if(o&&_e(r)&&e&&t.isEmpty(d))return S.some($(i,t.nodeIndex(r)));const u=new Kt(r,d);for(;c=u[o?"prev":"next"]();){if("false"===t.getContentEditableParent(c)||A_(c,a))return S.none();if(tt(c)&&c.data.length>0)return R_(c,a,"A")?S.none():S.some($(c,o?c.data.length:0));if(t.isBlock(c)||s[c.nodeName.toLowerCase()])return S.none();l=c}return sa(l)?S.none():n&&l?S.some($(l,0)):S.none()},ev=(t,e,n,o)=>{const r=t.getRoot();let a,s=!1,i=n?o.startContainer:o.endContainer,l=n?o.startOffset:o.endOffset;const c=it(i)&&l===i.childNodes.length,d=t.schema.getNonEmptyElements();let u=n;if(no(i))return S.none();if(it(i)&&l>i.childNodes.length-1&&(u=!1),Di(i)&&(i=r,l=0),i===r){if(u&&(a=i.childNodes[l>0?l-1:0],a)&&(no(a)||d[a.nodeName]||Ir(a)))return S.none();if(i.hasChildNodes()){if(l=Math.min(!u&&l>0?l-1:l,i.childNodes.length-1),i=i.childNodes[l],l=tt(i)&&c?i.data.length:0,!e&&i===r.lastChild&&Ir(i)||((m,p)=>{let g=p;for(;g&&g!==m;){if(ve(g))return!0;g=g.parentNode}return!1})(r,i)||no(i))return S.none();if(i.hasChildNodes()&&!Ir(i)){a=i;const m=new Kt(i,r);do{if(ve(a)||no(a)){s=!1;break}if(tt(a)&&a.data.length>0){l=u?0:a.data.length,i=a,s=!0;break}if(d[a.nodeName.toLowerCase()]&&!$S(a)){l=t.nodeIndex(a),i=a.parentNode,u||l++,s=!0;break}}while(a=u?m.next():m.prev())}}}return e&&(tt(i)&&0===l&&km(t,c,e,!0,i).each(m=>{i=m.container(),l=m.offset(),s=!0}),it(i)&&(a=i.childNodes[l],a||(a=i.childNodes[l-1]),!a||!_e(a)||"A"===(null===(g=a.previousSibling)||void 0===g?void 0:g.nodeName)||tv(t,a,!1)||tv(t,a,!0)||km(t,c,e,!0,a).each(m=>{i=m.container(),l=m.offset(),s=!0}))),u&&!e&&tt(i)&&l===i.data.length&&km(t,c,e,!1,i).each(m=>{i=m.container(),l=m.offset(),s=!0}),s&&i?S.some($(i,l)):S.none();var g},Wc=(t,e)=>{const n=e.collapsed,o=e.cloneRange(),r=$.fromRangeStart(e);return ev(t,n,!0,o).each(a=>{n&&$.isAbove(r,a)||o.setStart(a.container(),a.offset())}),n||ev(t,n,!1,o).each(a=>{o.setEnd(a.container(),a.offset())}),n&&o.collapse(!0),Sm(e,o)?S.none():S.some(o)},Kc=(t,e)=>t.splitText(e),Gc=t=>{let e=t.startContainer,n=t.startOffset,o=t.endContainer,r=t.endOffset;if(e===o&&tt(e)){if(n>0&&n<e.data.length)if(o=Kc(e,n),e=o.previousSibling,r>n){r-=n;const a=Kc(o,r).previousSibling;e=o=a,r=a.data.length,n=0}else r=0}else if(tt(e)&&n>0&&n<e.data.length&&(e=Kc(e,n),n=0),tt(o)&&r>0&&r<o.data.length){const a=Kc(o,r).previousSibling;o=a,r=a.data.length}return{startContainer:e,startOffset:n,endContainer:o,endOffset:r}},ss=t=>({walk:(e,n)=>sl(t,e,n),split:Gc,expand:(e,n={type:"word"})=>{if("word"===n.type){const o=rs(t,e,[{inline:"span"}]),r=t.createRng();return r.setStart(o.startContainer,o.startOffset),r.setEnd(o.endContainer,o.endOffset),r}return e},normalize:e=>Wc(t,e).fold(de,n=>(e.setStart(n.startContainer,n.startOffset),e.setEnd(n.endContainer,n.endOffset),!0))});ss.compareRanges=Sm,ss.getCaretRangeFromPoint=Jb,ss.getSelectedNode=_c,ss.getNode=ga;const T_=(t=>{const n=r=>{const a=(s=>{const i=s.dom;return ra(s)?i.getBoundingClientRect().height:i.offsetHeight})(r);if(a<=0||null===a){const s=xo(r,t);return parseFloat(s)||0}return a},o=(r,a)=>Re(a,(s,i)=>{const l=xo(r,i),c=void 0===l?0:parseInt(l,10);return isNaN(c)?s:s+c},0);return{set:(r,a)=>{if(!nn(a)&&!a.match(/^[0-9]+$/))throw new Error(t+".set accepts only positive integer values. Value was "+a);const s=r.dom;ja(s)&&(s.style[t]=a+"px")},get:n,getOuter:n,aggregate:o,max:(r,a,s)=>{const i=o(r,s);return a>i?a-i:0}}})("height"),nv=()=>A(document),ov=(t,e)=>t.view(e).fold(ut([]),n=>{const o=t.owner(n),r=ov(t,o);return[n].concat(r)});var O_=Object.freeze({__proto__:null,view:t=>{var e;return(t.dom===document?S.none():S.from(null===(e=t.dom.defaultView)||void 0===e?void 0:e.frameElement)).map(A)},owner:t=>yo(t)});const Em=t=>"textarea"===ge(t),rv=(t,e)=>{const n=(r=>{const a=r.dom.ownerDocument,s=a.body,i=a.defaultView,l=a.documentElement;if(s===r.dom)return bt(s.offsetLeft,s.offsetTop);const c=xt(i?.pageYOffset,l.scrollTop),d=xt(i?.pageXOffset,l.scrollLeft),u=xt(l.clientTop,s.clientTop),m=xt(l.clientLeft,s.clientLeft);return ae(r).translate(d-m,c-u)})(t),o=T_.get(t);return{element:t,bottom:n.top+o,height:o,pos:n,cleanup:e}},av=(t,e,n,o)=>{D_(t,(r,a)=>B_(t,e,n,o),n)},sv=(t,e,n,o,r)=>{const a={elm:o.element.dom,alignToTop:r};t.dispatch("ScrollIntoView",a).isDefaultPrevented()||(n(e,Pt(e).top,o,r),t.dispatch("AfterScrollIntoView",a))},B_=(t,e,n,o)=>{A(t.getBody());const a=A(t.getDoc()),s=((i,l)=>{const c=((u,m)=>{const p=ze(u);if(0===p.length||Em(u))return{element:u,offset:m};if(m<p.length&&!Em(p[m]))return{element:p[m],offset:0};{const g=p[p.length-1];return Em(g)?{element:u,offset:m}:"img"===ge(g)?{element:g,offset:1}:pn(g)?{element:g,offset:uc(g).length}:{element:g,offset:ze(g).length}}})(i,l),d=Ma('<span data-mce-bogus="all" style="display: inline-block;">\ufeff</span>');return bn(c.element,d),rv(d,()=>w(d))})(A(n.startContainer),n.startOffset);sv(t,a,e,s,o),s.cleanup()},iv=(t,e,n,o)=>{const r=A(t.getDoc());sv(t,r,n,rv(A(e),jt),o)},D_=(t,e,n)=>{const o=n.startContainer,r=n.startOffset,a=n.endContainer,s=n.endOffset;e(A(o),A(a));const i=t.dom.createRng();i.setStart(o,r),i.setEnd(a,s),t.selection.setRng(n)},_m=(t,e)=>t.element.dom.scrollIntoView({block:e?"start":"end"}),lv=(t,e,n,o)=>{const r=e+t,a=n.pos.top,s=n.bottom,i=s-a>=e;a<t?_m(n,!1!==o):a>r?_m(n,i?!1!==o:!0===o):s>r&&!i&&_m(n,!0===o)},cv=(t,e,n,o)=>{const r=Pr(t).dom.innerHeight;lv(e,r,n,o)},dv=(t,e,n,o)=>{const r=Pr(t).dom.innerHeight;lv(e,r,n,o);const a=(i=>{const l=nv(),c=Pt(l),d=((p,g)=>{const h=g.owner(p);return ov(g,h)})(i,O_),u=ae(i),m=Kn(d,(p,g)=>{const h=ae(g);return{left:p.left+h.left,top:p.top+h.top}},{left:0,top:0});return bt(m.left+u.left+c.left,m.top+u.top+c.top)})(n.element),s=Et(window);a.top<s.y?Gt(n.element,!1!==o):a.top>s.bottom&&Gt(n.element,!0===o)},P_=(t,e,n)=>av(t,cv,e,n),M_=(t,e,n)=>iv(t,e,cv,n),L_=(t,e,n)=>av(t,dv,e,n),I_=(t,e,n)=>iv(t,e,dv,n),cl=(t,e,n)=>{(t.inline?P_:L_)(t,e,n)},uv=t=>t.dom.focus(),mv=t=>{const e=Co(t).dom;return t.dom===e.activeElement},Nm=(t=nv())=>S.from(t.dom.activeElement).map(A),gv=(t,e)=>{const n=pn(e)?uc(e).length:ze(e).length+1;return t>n?n:t<0?0:t},F_=t=>Qs.range(t.start,gv(t.soffset,t.start),t.finish,gv(t.foffset,t.finish)),fv=(t,e)=>!He(e.dom)&&(vo(t,e)||re(t,e)),pv=t=>e=>fv(t,e.start)&&fv(t,e.finish),U_=t=>Qs.range(A(t.startContainer),t.startOffset,A(t.endContainer),t.endOffset),j_=t=>{const e=document.createRange();try{return e.setStart(t.start.dom,t.soffset),e.setEnd(t.finish.dom,t.foffset),S.some(e)}catch{return S.none()}},Rm=t=>{const e=t.inline||Jt.browser.isFirefox()?(n=A(t.getBody()),(o=>{const r=o.getSelection();return(r&&0!==r.rangeCount?S.from(r.getRangeAt(0)):S.none()).map(U_)})(Pr(n).dom).filter(pv(n))):S.none();var n;t.bookmark=e.isSome()?e:t.bookmark},Am=t=>(t.bookmark?t.bookmark:S.none()).bind(e=>{return n=A(t.getBody()),S.from(e).filter(pv(n)).map(F_);var n}).bind(j_),hv={isEditorUIElement:t=>{const e=t.className.toString();return-1!==e.indexOf("tox-")||-1!==e.indexOf("mce-")}},so={setEditorTimeout:(t,e,n)=>{return nn(r=n)||(r=0),setTimeout(()=>{t.removed||e()},r);var r},setEditorInterval:(t,e,n)=>{const o=(nn(a=n)||(a=0),setInterval(()=>{t.removed?clearInterval(o):e()},a));var a;return o}};let Js;const Tm=me.DOM,Om=t=>{const e=t.classList;return void 0!==e&&(e.contains("tox-edit-area")||e.contains("tox-edit-area__iframe")||e.contains("mce-content-body"))},Yc=(t,e)=>{const n=fE(t);return null!==Tm.getParent(e,r=>{return it(a=r)&&hv.isEditorUIElement(a)||!!n&&t.dom.is(r,n);var a})},Bm=t=>{try{const e=Co(A(t.getElement()));return Nm(e).fold(()=>document.body,n=>n.dom)}catch{return document.body}},z_=(t,e)=>{const n=e.editor;(r=>{const a=Ec(()=>{Rm(r)},0);r.on("init",()=>{var s,c;r.inline&&((s,i)=>{const l=()=>{i.throttle()};me.DOM.bind(document,"mouseup",l),s.on("remove",()=>{me.DOM.unbind(document,"mouseup",l)})})(r,a),c=a,(s=r).on("mouseup touchend",d=>{c.throttle()}),s.on("keyup NodeChange AfterSetSelectionRange",l=>{(c=>"nodechange"===c.type&&c.selectionChange)(l)||Rm(s)})}),r.on("remove",()=>{a.cancel()})})(n);const o=(r,a)=>{YE(r)&&!0!==r.inline&&a(A(r.getContainer()),"tox-edit-focus")};n.on("focusin",()=>{const r=t.focusedEditor;Om(Bm(n))&&o(n,Jr),r!==n&&(r&&r.dispatch("blur",{focusedEditor:n}),t.setActive(n),t.focusedEditor=n,n.dispatch("focus",{blurredEditor:r}),n.focus(!0))}),n.on("focusout",()=>{so.setEditorTimeout(n,()=>{const r=t.focusedEditor;Om(Bm(n))&&r===n||o(n,Br),Yc(n,Bm(n))||r!==n||(n.dispatch("blur",{focusedEditor:null}),t.focusedEditor=null)})}),Js||(Js=r=>{const a=t.activeEditor;a&&Bi(r).each(s=>{s.ownerDocument===document&&(s===document.body||Yc(a,s)||t.focusedEditor!==a||(a.dispatch("blur",{focusedEditor:null}),t.focusedEditor=null))})},Tm.bind(document,"focusin",Js))},H_=(t,e)=>{t.focusedEditor===e.editor&&(t.focusedEditor=null),!t.activeEditor&&Js&&(Tm.unbind(document,"focusin",Js),Js=null)},bv=(t,e)=>{var n,o,r;(n=A(t.getBody()),o=e,(r=o,r.collapsed?S.from(ga(r.startContainer,r.startOffset)).map(A):S.none()).bind(r=>KS(r)?S.some(r):vo(n,r)?S.none():S.some(n))).bind(n=>Cn(n.dom)).fold(()=>{t.selection.normalize()},n=>t.selection.setRng(n.toRange()))},Dm=t=>{if(t.setActive)try{t.setActive()}catch{t.focus()}else t.focus()},is=t=>{return t.inline?(e=>{const n=e.getBody();return n&&(o=A(n),mv(o)||(r=o,Nm(Co(r)).filter(a=>r.dom.contains(a.dom))).isSome());var o,r})(t):st((e=t).iframeElement)&&mv(A(e.iframeElement));var e},Pm=t=>t.editorManager.setActive(t),vv=(t,e,n,o,r)=>{const s=n?e.startOffset:e.endOffset;return S.from(n?e.startContainer:e.endContainer).map(A).map(i=>o&&e.collapsed?i:na(i,r(i,s)).getOr(i)).bind(i=>fn(i)?S.some(i):Tn(i).filter(fn)).map(i=>i.dom).getOr(t)},yv=(t,e,n=!1)=>vv(t,e,!0,n,(o,r)=>Math.min(oa(o),r)),Cv=(t,e,n=!1)=>vv(t,e,!1,n,(o,r)=>r>0?r-1:r),wv=(t,e)=>{const n=t;for(;t&&tt(t)&&0===t.length;)t=e?t.nextSibling:t.previousSibling;return t||n},Mm=(t,e)=>te(e,n=>{const o=t.dispatch("GetSelectionRange",{range:n});return o.range!==n?o.range:n}),$_=["img","br"],xv=t=>{return gp(t).filter(n=>0!==n.trim().length||n.indexOf(Ge)>-1).isSome()||Lt($_,ge(t))||xi(n=t)&&"false"===An(n,"contenteditable");var n},Sv="[data-mce-autocompleter]",kv=t=>Ua(t,Sv),V_={"#text":3,"#comment":8,"#cdata":4,"#pi":7,"#doctype":10,"#document-fragment":11},Xc=(t,e,n)=>{const o=n?"lastChild":"firstChild",r=n?"prev":"next";if(t[o])return t[o];if(t!==e){let a=t[r];if(a)return a;for(let s=t.parent;s&&s!==e;s=s.parent)if(a=s[r],a)return a}},W_=t=>{var e;const n=null!==(e=t.value)&&void 0!==e?e:"";if(!Va(n))return!1;const o=t.parent;return!o||"span"===o.name&&!o.attr("style")||!/^[ ]+$/.test(n)},Ev=t=>{const e="a"===t.name&&!t.attr("href")&&t.attr("id");return t.attr("name")||t.attr("id")&&!t.firstChild||t.attr("data-mce-bookmark")||e};class Pn{static create(e,n){const o=new Pn(e,V_[e]||1);return n&&le(n,(r,a)=>{o.attr(a,r)}),o}constructor(e,n){this.name=e,this.type=n,1===n&&(this.attributes=[],this.attributes.map={})}replace(e){const n=this;return e.parent&&e.remove(),n.insert(e,n),n.remove(),n}attr(e,n){const o=this;if(!Nt(e))return st(e)&&le(e,(a,s)=>{o.attr(s,a)}),o;const r=o.attributes;if(r){if(void 0!==n){if(null===n){if(e in r.map){delete r.map[e];let a=r.length;for(;a--;)if(r[a].name===e)return r.splice(a,1),o}return o}if(e in r.map){let a=r.length;for(;a--;)if(r[a].name===e){r[a].value=n;break}}else r.push({name:e,value:n});return r.map[e]=n,o}return r.map[e]}}clone(){const e=this,n=new Pn(e.name,e.type),o=e.attributes;if(o){const r=[];r.map={};for(let a=0,s=o.length;a<s;a++){const i=o[a];"id"!==i.name&&(r[r.length]={name:i.name,value:i.value},r.map[i.name]=i.value)}n.attributes=r}return n.value=e.value,n}wrap(e){const n=this;return n.parent&&(n.parent.insert(e,n),e.append(n)),n}unwrap(){const e=this;for(let n=e.firstChild;n;){const o=n.next;e.insert(n,e,!0),n=o}e.remove()}remove(){const e=this,n=e.parent,o=e.next,r=e.prev;return n&&(n.firstChild===e?(n.firstChild=o,o&&(o.prev=null)):r&&(r.next=o),n.lastChild===e?(n.lastChild=r,r&&(r.next=null)):o&&(o.prev=r),e.parent=e.next=e.prev=null),e}append(e){const n=this;e.parent&&e.remove();const o=n.lastChild;return o?(o.next=e,e.prev=o,n.lastChild=e):n.lastChild=n.firstChild=e,e.parent=n,e}insert(e,n,o){e.parent&&e.remove();const r=n.parent||this;return o?(n===r.firstChild?r.firstChild=e:n.prev&&(n.prev.next=e),e.prev=n.prev,e.next=n,n.prev=e):(n===r.lastChild?r.lastChild=e:n.next&&(n.next.prev=e),e.next=n.next,e.prev=n,n.next=e),e.parent=r,e}getAll(e){const n=this,o=[];for(let r=n.firstChild;r;r=Xc(r,n))r.name===e&&o.push(r);return o}children(){const e=[];for(let n=this.firstChild;n;n=n.next)e.push(n);return e}empty(){const e=this;if(e.firstChild){const n=[];for(let r=e.firstChild;r;r=Xc(r,e))n.push(r);let o=n.length;for(;o--;){const r=n[o];r.parent=r.firstChild=r.lastChild=r.next=r.prev=null}}return e.firstChild=e.lastChild=null,e}isEmpty(e,n={},o){var r;const a=this;let s=a.firstChild;if(Ev(a))return!1;if(s)do{if(1===s.type){if(s.attr("data-mce-bogus"))continue;if(e[s.name]||Ev(s))return!1}if(8===s.type||3===s.type&&!W_(s)||3===s.type&&s.parent&&n[s.parent.name]&&Va(null!==(r=s.value)&&void 0!==r?r:"")||o&&o(s))return!1}while(s=Xc(s,a));return!0}walk(e){return Xc(this,null,e)}}const _v=(t,e,n=0)=>{const o=t.toLowerCase();if(-1!==o.indexOf("[if ",n)&&/^\s*\[if [\w\W]+\]>.*<!\[endif\](--!?)?>/.test(o.substr(n))){const r=o.indexOf("[endif]",n);return o.indexOf(">",r)}if(e){const r=o.indexOf(">",n);return-1!==r?r:o.length}{const r=/--!?>/g;r.lastIndex=n;const a=r.exec(t);return a?a.index+a[0].length:o.length}},K_=(t,e,n)=>{const o=/<([!?\/])?([A-Za-z0-9\-_:.]+)/g,r=/(?:\s(?:[^'">]+(?:"[^"]*"|'[^']*'))*[^"'>]*(?:"[^">]*|'[^'>]*)?|\s*|\/)>/g,a=t.getVoidElements();let s=1,i=n;for(;0!==s;)for(o.lastIndex=i;;){const l=o.exec(e);if(null===l)return i;if("!"===l[1]){i=ht(l[2],"--")?_v(e,!1,l.index+3):_v(e,!0,l.index+1);break}{r.lastIndex=o.lastIndex;const c=r.exec(e);if(Oe(c)||c.index!==o.lastIndex)continue;"/"===l[1]?s-=1:It(a,l[2])||(s+=1),i=o.lastIndex+c[0].length;break}}return i},Nv=(t,e)=>{const n=/<(\w+) [^>]*data-mce-bogus="all"[^>]*>/g,o=t.schema;let r=((i,l)=>{const c=new RegExp(["\\s?("+i.join("|")+')="[^"]+"'].join("|"),"gi");return l.replace(c,"")})(t.getTempAttrs(),e);const a=o.getVoidElements();let s;for(;s=n.exec(r);){const i=n.lastIndex,l=s[0].length;let c;c=a[s[1]]?i:K_(o,r,i),r=r.substring(0,i-l)+r.substring(c),n.lastIndex=i-l}return Fr(r)},G_=Nv,Rv=t=>{const e=Rt(t,"[data-mce-bogus]");J(e,n=>{"all"===An(n,"data-mce-bogus")?w(n):qa(n)?(bn(n,bo("\ufeff")),w(n)):_(n)})},Av=t=>{const e=Rt(t,"input");J(e,n=>{Le(n,"name")})},Tv=ot.makeMap,Ov=t=>{const e=[],n=(t=t||{}).indent,o=Tv(t.indent_before||""),r=Tv(t.indent_after||""),a=da.getEncodeFunc(t.entity_encoding||"raw",t.entities),s="xhtml"!==t.element_format;return{start:(i,l,c)=>{if(n&&o[i]&&e.length>0){const d=e[e.length-1];d.length>0&&"\n"!==d&&e.push("\n")}if(e.push("<",i),l)for(let d=0,u=l.length;d<u;d++){const m=l[d];e.push(" ",m.name,'="',a(m.value,!0),'"')}if(e[e.length]=!c||s?">":" />",c&&n&&r[i]&&e.length>0){const d=e[e.length-1];d.length>0&&"\n"!==d&&e.push("\n")}},end:i=>{let l;e.push("</",i,">"),n&&r[i]&&e.length>0&&(l=e[e.length-1],l.length>0&&"\n"!==l&&e.push("\n"))},text:(i,l)=>{i.length>0&&(e[e.length]=l?i:a(i))},cdata:i=>{e.push("<![CDATA[",i,"]]>")},comment:i=>{e.push("\x3c!--",i,"--\x3e")},pi:(i,l)=>{l?e.push("<?",i," ",a(l),"?>"):e.push("<?",i,"?>"),n&&e.push("\n")},doctype:i=>{e.push("<!DOCTYPE",i,">",n?"\n":"")},reset:()=>{e.length=0},getContent:()=>e.join("").replace(/\n$/,"")}},ba=(t={},e=ua())=>{const n=Ov(t);return t.validate=!("validate"in t)||t.validate,{serialize:o=>{const r=t.validate,a={3:i=>{var l;n.text(null!==(l=i.value)&&void 0!==l?l:"",i.raw)},8:i=>{var l;n.comment(null!==(l=i.value)&&void 0!==l?l:"")},7:i=>{n.pi(i.name,i.value)},10:i=>{var l;n.doctype(null!==(l=i.value)&&void 0!==l?l:"")},4:i=>{var l;n.cdata(null!==(l=i.value)&&void 0!==l?l:"")},11:i=>{let l=i;if(l=l.firstChild)do{s(l)}while(l=l.next)}};n.reset();const s=i=>{var l;const c=a[i.type];if(c)c(i);else{const d=i.name,u=d in e.getVoidElements();let m=i.attributes;if(r&&m&&m.length>1){const p=[];p.map={};const g=e.getElementRule(i.name);if(g){for(let h=0,f=g.attributesOrder.length;h<f;h++){const b=g.attributesOrder[h];if(b in m.map){const y=m.map[b];p.map[b]=y,p.push({name:b,value:y})}}for(let h=0,f=m.length;h<f;h++){const b=m[h].name;if(!(b in p.map)){const y=m.map[b];p.map[b]=y,p.push({name:b,value:y})}}m=p}}if(n.start(d,m,u),!u){let p=i.firstChild;if(p){"pre"!==d&&"textarea"!==d||3!==p.type||"\n"!==(null===(l=p.value)||void 0===l?void 0:l[0])||n.text("\n",!0);do{s(p)}while(p=p.next)}n.end(d)}}};return 1!==o.type||t.inner?3===o.type?a[3](o):a[11](o):s(o),n.getContent()}}},Bv=new Set;J(["margin","margin-left","margin-right","margin-top","margin-bottom","padding","padding-left","padding-right","padding-top","padding-bottom","border","border-width","border-style","border-color","background","background-attachment","background-clip","background-color","background-image","background-origin","background-position","background-repeat","background-size","float","position","left","right","top","bottom","z-index","display","transform","width","max-width","min-width","height","max-height","min-height","overflow","overflow-x","overflow-y","text-overflow","vertical-align","transition","transition-delay","transition-duration","transition-property","transition-timing-function"],t=>{Bv.add(t)});const Dv=["font","text-decoration","text-emphasis"],Lm=(t,e)=>tn(t.parseStyle(t.getAttrib(e,"style"))),X_=(t,e,n)=>{const o=Lm(t,e),r=Lm(t,n),a=s=>{var i,l;const c=null!==(i=t.getStyle(e,s))&&void 0!==i?i:"",d=null!==(l=t.getStyle(n,s))&&void 0!==l?l:"";return Qt(c)&&Qt(d)&&c!==d};return ie(o,s=>{const i=l=>ie(l,c=>c===s);if(!i(r)&&i(Dv)){const l=Ot(r,c=>ie(Dv,d=>ht(c,d)));return ie(l,a)}return a(s)})},Pv=(t,e,n)=>S.from(n.container()).filter(tt).exists(o=>{const r=t?0:-1;return e(o.data.charAt(n.offset()+r))}),Im=Ct(Pv,!0,Ks),Fm=Ct(Pv,!1,Ks),Q_=t=>{const e=t.container();return tt(e)&&(0===e.data.length||pc(e.data)&&il.isBookmarkNode(e.parentNode))},va=(t,e)=>n=>Ju(t?0:-1,n).filter(e).isSome(),Mv=t=>up(t)&&"block"===xo(A(t),"display"),Lv=t=>{return ve(t)&&!(it(e=t)&&"all"===e.getAttribute("data-mce-bogus"));var e},J_=va(!0,Mv),Z_=va(!1,Mv),dl=va(!0,nr),ul=va(!1,nr),Iv=va(!0,Ir),Fv=va(!1,Ir),ya=va(!0,Lv),Ca=va(!1,Lv),Uv=(t,e)=>{return r=de,vo(o=e,n=t)?tc(n,a=>r(a)||re(a,o)).slice(0,-1):[];var n,o,r},zr=(t,e)=>[t].concat(Uv(t,e)),Um=(t,e,n)=>db(t,e,n,Q_),jv=(t,e)=>he(zr(A(e.container()),t),eo),zv=(t,e,n)=>Um(t,e.dom,n).forall(o=>jv(e,n).fold(()=>!rr(o,n,e.dom),r=>!rr(o,n,e.dom)&&vo(r,A(o.container())))),Hv=(t,e,n)=>jv(e,n).fold(()=>Um(t,e.dom,n).forall(o=>!rr(o,n,e.dom)),o=>Um(t,o.dom,n).isNone()),jm=Ct(Hv,!1),$v=Ct(Hv,!0),tN=Ct(zv,!1),eN=Ct(zv,!0),nN=t=>Dc(t).exists(qa),Qc=(t,e,n)=>{const o=Ot(zr(A(n.container()),e),eo),r=We(o).getOr(e);return oo(t,r.dom,n).filter(nN)},zm=(t,e)=>Dc(e).exists(qa)||Qc(!0,t,e).isSome(),Hm=(t,e)=>{return(n=e,S.from(n.getNode(!0)).map(A)).exists(qa)||Qc(!1,t,e).isSome();var n},oN=Ct(Qc,!1),rN=Ct(Qc,!0),qv=t=>$.isTextPosition(t)&&!t.isAtStart()&&!t.isAtEnd(),Vv=(t,e)=>{const n=Ot(zr(A(e.container()),t),eo);return We(n).getOr(t)},Wv=(t,e)=>qv(e)?Fm(e):Fm(e)||Dn(Vv(t,e).dom,e).exists(Fm),Kv=(t,e)=>qv(e)?Im(e):Im(e)||yn(Vv(t,e).dom,e).exists(Im),$m=t=>Dc(t).bind(e=>Lr(e,fn)).exists(e=>{return n=xo(e,"white-space"),Lt(["pre","pre-wrap"],n);var n}),Gv=(t,e)=>n=>{return o=new Kt(n,t)[e](),st(o)&&ve(o)&&Qh(o);var o},Yv=(t,e)=>{return!$m(e)&&(Dn((n=t).dom,o=e).isNone()||yn(n.dom,o).isNone()||jm(n,o)||$v(n,o)||Hm(n,o)||zm(n,o)||Wv(t,e)||Kv(t,e));var n,o},ml=(t,e)=>!$m(e)&&(jm(t,e)||tN(t,e)||Hm(t,e)||Wv(t,e)||((n,o)=>{const r=Dn(n.dom,o).getOr(o),a=Gv(n.dom,"prev");return o.isAtStart()&&(a(o.container())||a(r.container()))})(t,e)),gl=(t,e)=>!$m(e)&&($v(t,e)||eN(t,e)||zm(t,e)||Kv(t,e)||((n,o)=>{const r=yn(n.dom,o).getOr(o),a=Gv(n.dom,"next");return o.isAtEnd()&&(a(o.container())||a(r.container()))})(t,e)),qm=(t,e)=>ml(t,e)||gl(t,(n=>{const o=n.container(),r=n.offset();return tt(o)&&r<o.data.length?$(o,r+1):n})(e)),Xv=(t,e)=>Ic(t.charAt(e)),Qv=(t,e)=>Ks(t.charAt(e)),Jv=(t,e,n)=>{const o=e.data,r=$(e,0);return n||!Xv(o,0)||qm(t,r)?!!(n&&Qv(o,0)&&ml(t,r))&&(e.data=Ge+o.slice(1),!0):(e.data=" "+o.slice(1),!0)},Zv=(t,e,n)=>{const o=e.data,r=$(e,o.length-1);return n||!Xv(o,o.length-1)||qm(t,r)?!!(n&&Qv(o,o.length-1)&&gl(t,r))&&(e.data=o.slice(0,-1)+Ge,!0):(e.data=o.slice(0,-1)+" ",!0)},t0=(t,e,n)=>{if(0===n)return;const o=A(t),r=wo(o,eo).getOr(o),a=t.data.slice(e,e+n),s=e+n>=t.data.length&&gl(r,$(t,t.data.length)),i=0===e&&ml(r,$(t,0));t.replaceData(e,n,wp(a,4,i,s))},Jc=(t,e)=>{const n=t.data.slice(e),o=n.length-ne(n).length;t0(t,e,o)},Vm=(t,e)=>{const n=t.data.slice(0,e),o=n.length-oe(n).length;t0(t,e-o,o)},Wm=(t,e,n,o=!0)=>{const r=oe(t.data).length,a=o?t:e,s=o?e:t;return o?a.appendData(s.data):a.insertData(0,s.data),w(A(s)),n&&Jc(a,r),a},sN=(t,e)=>((n,o)=>{const r=n.container(),a=n.offset();return!$.isTextPosition(n)&&r===o.parentNode&&a>$.before(o).offset()})(e,t)?$(e.container(),e.offset()-1):e,e0=t=>{return ko(t.previousSibling)?S.some(tt(e=t.previousSibling)?$(e,e.data.length):$.after(e)):t.previousSibling?Eo(t.previousSibling):S.none();var e},n0=t=>{return ko(t.nextSibling)?S.some(tt(e=t.nextSibling)?$(e,0):$.before(e)):t.nextSibling?Cn(t.nextSibling):S.none();var e},o0=(t,e,n)=>{n.fold(()=>{t.focus()},o=>{t.selection.setRng(o.toRange(),e)})},lN=(t,e)=>e&&It(t.schema.getBlockElements(),ge(e)),cN=t=>{if(De(t)){const e=Ma('<br data-mce-bogus="1">');return x(t),Se(t,e),S.some($.before(e.dom))}return S.none()},ls=(t,e,n,o=!0)=>{const r=((t,e,n)=>{return(o=t,r=e,a=n,o?(s=r,n0(i=a).orThunk(()=>e0(i)).orThunk(()=>{return yn(l=s,$.after(c=i)).orThunk(()=>Dn(l,$.before(c)));var l,c})):((s,i)=>e0(i).orThunk(()=>n0(i)).orThunk(()=>{return l=s,S.from((c=i).previousSibling?c.previousSibling:c.parentNode).bind(d=>Dn(l,$.before(d))).orThunk(()=>yn(l,$.after(c)));var l,c}))(r,a)).map(Ct(sN,n));var o,r,a,s,i})(e,t.getBody(),n.dom),a=wo(n,Ct(lN,t),(s=t.getBody(),l=>l.dom===s));var s;const i=((l,c,d)=>{const u=Mr(l).filter(pn),m=ea(l).filter(pn);return w(l),(p=u,g=m,h=c,f=(b,y,C)=>{const k=b.dom,E=y.dom,N=k.data.length;return Wm(k,E,d),C.container()===E?$(k,N):C},p.isSome()&&g.isSome()&&h.isSome()?S.some(f(p.getOrDie(),g.getOrDie(),h.getOrDie())):S.none()).orThunk(()=>(d&&(u.each(b=>Vm(b.dom,b.dom.length)),m.each(b=>Jc(b.dom,0))),c));var p,g,h,f})(n,r,(c=n,It(t.schema.getTextInlineElements(),ge(c))));var c;t.dom.isEmpty(t.getBody())?(t.setContent(""),t.selection.setCursorLocation()):a.bind(cN).fold(()=>{o&&o0(t,e,i)},l=>{o&&o0(t,e,S.some(l))})},dN=/[\u0591-\u07FF\uFB1D-\uFDFF\uFE70-\uFEFC]/,cs=(t,e)=>hn(A(e),rE(t))&&!Wa(t.schema,e)&&t.dom.isEditable(e),r0=t=>{var e,n;return"rtl"===me.DOM.getStyle(t,"direction",!0)||(n=null!==(e=t.textContent)&&void 0!==e?e:"",dN.test(n))},wa=(t,e,n)=>{const o=(r=t,a=e,Ot(me.DOM.getParents(n.container(),"*",a),r));var r,a;return S.from(o[o.length-1])},io=(t,e)=>{const n=e.container(),o=e.offset();return t?ca(n)?tt(n.nextSibling)?$(n.nextSibling,0):$.after(n):bu(e)?$(n,o+1):e:ca(n)?tt(n.previousSibling)?$(n.previousSibling,n.previousSibling.data.length):$.before(n):vu(e)?$(n,o-1):e},a0=Ct(io,!0),s0=Ct(io,!1),i0=(t,e)=>{const n=o=>o.stopImmediatePropagation();t.on("beforeinput input",n,!0),t.getDoc().execCommand(e),t.off("beforeinput input",n)},Km=t=>i0(t,"Delete"),uN=t=>fp(t)||Pi(t),Zc=(t,e)=>vo(t,e)?Lr(e,uN,(n=>o=>tr(Tn(o),n,re))(t)):S.none(),Gm=(t,e=!0)=>{t.dom.isEmpty(t.getBody())&&t.setContent("",{no_selection:!e})},l0=(t,e,n)=>on(Cn(n),Eo(n),(o,r)=>{const a=io(!0,o),s=io(!1,r),i=io(!1,e);return t?yn(n,i).exists(l=>l.isEqual(s)&&e.isEqual(a)):Dn(n,i).exists(l=>l.isEqual(a)&&e.isEqual(s))}).getOr(!0),c0=t=>{var e;return(8===Xl(e=t)||"#comment"===ge(e)?Mr(t):Ai(t)).bind(c0).orThunk(()=>S.some(t))},d0=(t,e,n,o=!0)=>{var r;e.deleteContents();const a=c0(n).getOr(n),s=A(null!==(r=t.dom.getParent(a.dom,t.dom.isBlock))&&void 0!==r?r:n.dom);if(s.dom===t.getBody()?Gm(t,o):De(s)&&(la(s),o&&t.selection.setCursorLocation(s.dom,0)),!re(n,s)){const l=tr(Tn(s),n)?[]:Tn(i=s).map(ze).map(c=>Ot(c,d=>!re(i,d))).getOr([]);J(l.concat(ze(n)),c=>{re(c,s)||vo(c,s)||!De(c)||w(c)})}var i},Ym=t=>Rt(t,"td,th"),Xm=(t,e)=>({start:t,end:e}),fl=or([{singleCellTable:["rng","cell"]},{fullTable:["table"]},{partialTable:["cells","outsideDetails"]},{multiTable:["startTableCells","endTableCells","betweenRng"]}]),td=(t,e)=>er(A(t),"td,th",e),u0=t=>!re(t.start,t.end),m0=(t,e)=>rl(t.start,e).bind(n=>rl(t.end,e).bind(o=>ho(re(n,o),n))),Qm=t=>e=>m0(e,t).map(n=>((o,r,a)=>({rng:o,table:r,cells:a}))(e,n,Ym(n))),g0=(t,e,n,o)=>{if(n.collapsed||!t.forall(u0))return S.none();if(e.isSameTable){const r=t.bind(Qm(o));return S.some({start:r,end:r})}{const r=td(n.startContainer,o),a=td(n.endContainer,o),s=r.bind((l=>c=>rl(c,l).bind(d=>Nn(Ym(d)).map(u=>Xm(c,u))))(o)).bind(Qm(o)),i=a.bind((l=>c=>rl(c,l).bind(d=>We(Ym(d)).map(u=>Xm(u,c))))(o)).bind(Qm(o));return S.some({start:s,end:i})}},f0=(t,e)=>go(t,n=>re(n,e)),Jm=t=>on(f0(t.cells,t.rng.start),f0(t.cells,t.rng.end),(e,n)=>t.cells.slice(e,n+1)),p0=(t,e)=>{const{startTable:n,endTable:o}=e,r=t.cloneRange();return n.each(a=>r.setStartAfter(a.dom)),o.each(a=>r.setEndBefore(a.dom)),r},h0=t=>J(t,e=>{Le(e,"contenteditable"),la(e)}),Zm=(t,e,n,o)=>{const r=n.cloneRange();o?(r.setStart(n.startContainer,n.startOffset),r.setEndAfter(e.dom.lastChild)):(r.setStartBefore(e.dom.firstChild),r.setEnd(n.endContainer,n.endOffset)),tg(t,r,e,!1).each(a=>a())},b0=t=>{const e=Gs(t),n=A(t.selection.getNode());dc(n.dom)&&De(n)?t.selection.setCursorLocation(n.dom,0):t.selection.collapse(!0),e.length>1&&ie(e,o=>re(o,n))&&Me(n,"data-mce-selected","1")},v0=(t,e,n)=>S.some(()=>{const o=t.selection.getRng(),r=n.bind(({rng:a,isStartInTable:s})=>{const i=S.from((c=t).dom.getParent(s?a.endContainer:a.startContainer,c.dom.isBlock)).map(A);var c;a.deleteContents(),((c,d,u)=>{u.each(m=>{d?w(m):(la(m),c.selection.setCursorLocation(m.dom,0))})})(t,s,i.filter(De));const l=s?e[0]:e[e.length-1];return Zm(t,l,o,s),De(l)?S.none():S.some(s?e.slice(1):e.slice(0,-1))}).getOr(e);h0(r),b0(t)}),gN=(t,e,n,o)=>S.some(()=>{const r=t.selection.getRng(),a=e[0],s=n[n.length-1];Zm(t,a,r,!0),Zm(t,s,r,!1);const i=De(a)?e:e.slice(1),l=De(s)?n:n.slice(0,-1);h0(i.concat(l)),o.deleteContents(),b0(t)}),tg=(t,e,n,o=!0)=>S.some(()=>{d0(t,e,n,o)}),fN=(t,e)=>S.some(()=>ls(t,!1,e)),y0=(t,e)=>he(zr(e,t),Mi),eg=(t,e)=>he(zr(e,t),Pa("caption")),ed=(t,e)=>S.some(()=>{la(e),t.selection.setCursorLocation(e.dom,0)}),C0=(t,e)=>t?Iv(e):Fv(e),ng=(t,e)=>{const n=A(t.selection.getStart(!0)),o=Gs(t);return t.selection.isCollapsed()&&0===o.length?((t,e,n)=>{const o=A(t.getBody());return eg(o,n).fold(()=>((r,a,s,i)=>{const l=$.fromRangeStart(r.selection.getRng());return y0(s,i).bind(c=>{return De(c)?ed(r,c):(u=s,p=c,g=l,ol(a,r.getBody(),g).bind(h=>y0(u,A(h.getNode())).bind(f=>re(f,p)?S.none():S.some(jt))));var u,p,g})})(t,e,o,n).orThunk(()=>ho(((r,a)=>{const s=$.fromRangeStart(r.selection.getRng());return C0(a,s)||oo(a,r.getBody(),s).exists(i=>C0(a,i))})(t,e),jt)),r=>((a,s,i,l)=>{const c=$.fromRangeStart(a.selection.getRng());return De(l)?ed(a,l):(u=i,p=l,g=c,ol(m=s,(d=a).getBody(),g).fold(()=>S.some(jt),h=>{return b=m,y=g,C=h,Cn((f=p).dom).bind(k=>Eo(f.dom).map(E=>b?y.isEqual(k)&&C.isEqual(E):y.isEqual(E)&&C.isEqual(k))).getOr(!0)?((f,b)=>ed(f,b))(d,p):((f,b,y)=>eg(f,A(y.getNode())).fold(()=>S.some(jt),C=>ho(!re(C,b),jt)))(u,p,h);var f,b,y,C}));var d,u,m,p,g})(t,e,o,r))})(t,e,n):((r,a,s)=>{const i=A(r.getBody()),l=r.selection.getRng();return 0!==s.length?v0(r,s,S.none()):(c=r,u=l,eg(d=i,a).fold(()=>{return p=c,((t,e)=>{const n=(a=>s=>re(a,s))(t),o=((a,s)=>{const i=td(a.startContainer,s),l=td(a.endContainer,s);return on(i,l,Xm)})(e,n),r=((a,s)=>{const i=p=>rl(A(p),s),l=i(a.startContainer),c=i(a.endContainer),d=l.isSome(),u=c.isSome(),m=on(l,c,re).getOr(!1);return{startTable:l,endTable:c,isStartInTable:d,isEndInTable:u,isSameTable:m,isMultiTable:!m&&d&&u}})(e,n);return s=e,i=n,o.exists(l=>{return d=i,!u0(c=l)&&m0(c,d).exists(u=>{const m=u.dom.rows;return 1===m.length&&1===m[0].cells.length})&&cm(l.start,s);var c,d})?o.map(a=>fl.singleCellTable(e,a.start)):r.isMultiTable?((a,s,i,l)=>g0(a,s,i,l).bind(({start:c,end:d})=>{const u=c.bind(Jm).getOr([]),m=d.bind(Jm).getOr([]);if(u.length>0&&m.length>0){const p=p0(i,s);return S.some(fl.multiTable(u,m,p))}return S.none()}))(o,r,e,n):((a,s,i,l)=>g0(a,s,i,l).bind(({start:c,end:d})=>c.or(d)).bind(c=>{const{isSameTable:d}=s,u=Jm(c).getOr([]);if(d&&c.cells.length===u.length)return S.some(fl.fullTable(c.table));if(u.length>0){if(d)return S.some(fl.partialTable(u,S.none()));{const m=p0(i,s);return S.some(fl.partialTable(u,S.some({...s,rng:m})))}}return S.none()}))(o,r,e,n);var s,i})(d,u).bind(f=>f.fold(Ct(tg,p),Ct(fN,p),Ct(v0,p),Ct(gN,p)));var p},p=>ed(c,p)));var c,d,u})(t,n,o)},ds=(t,e)=>{let n=e;for(;n&&n!==t;){if(Po(n)||ve(n))return n;n=n.parentNode}return null},hN=["data-ephox-","data-mce-","data-alloy-","data-snooker-","_"],bN=ot.each,og=t=>{const e=t.dom,n=new Set(t.serializer.getTempAttrs()),o=r=>ie(hN,a=>ht(r,a))||n.has(r);return{compare:(r,a)=>{if(r.nodeName!==a.nodeName||r.nodeType!==a.nodeType)return!1;const s=l=>{const c={};return bN(e.getAttribs(l),d=>{const u=d.nodeName.toLowerCase();"style"===u||o(u)||(c[u]=e.getAttrib(l,u))}),c},i=(l,c)=>{for(const d in l)if(It(l,d)){const u=c[d];if(dt(u)||l[d]!==u)return!1;delete c[d]}for(const d in c)if(It(c,d))return!1;return!0};return!(it(r)&&it(a)&&(!i(s(r),s(a))||!i(e.parseStyle(e.getAttrib(r,"style")),e.parseStyle(e.getAttrib(a,"style"))))||ro(r)||ro(a))},isAttributeInternal:o}},w0=(t,e,n,o)=>{const r=n.name;for(let a=0,s=t.length;a<s;a++){const i=t[a];if(i.name===r){const l=o.nodes[r];l?l.nodes.push(n):o.nodes[r]={filter:i,nodes:[n]}}}if(n.attributes)for(let a=0,s=e.length;a<s;a++){const i=e[a],l=i.name;if(l in n.attributes.map){const c=o.attributes[l];c?c.nodes.push(n):o.attributes[l]={filter:i,nodes:[n]}}}},x0=(t,e)=>{const n=(o,r)=>{le(o,a=>{const s=Ae(a.nodes);J(a.filter.callbacks,i=>{for(let l=s.length-1;l>=0;l--){const c=s[l];(r?void 0!==c.attr(a.filter.name):c.name===a.filter.name)&&!pe(c.parent)||s.splice(l,1)}s.length>0&&i(s,a.filter.name,e)})})};n(t.nodes,!1),n(t.attributes,!0)},S0=(t,e,n,o={})=>{const r=((a,s,i)=>{const l={nodes:{},attributes:{}};return i.firstChild&&(c=>{let u=c;for(;u=u.walk();)w0(a,s,u,l)})(i),l})(t,e,n);x0(r,o)},rg=(t,e,n)=>{if(t.insert&&e(n)){const o=new Pn("br",1);o.attr("data-mce-bogus","1"),n.empty().append(o)}else n.empty().append(new Pn("#text",3)).value=Ge},k0=(t,e)=>{const n=t?.firstChild;return st(n)&&n===t.lastChild&&n.name===e},pl=(t,e,n,o)=>o.isEmpty(e,n,r=>((a,s)=>{const i=a.getElementRule(s.name);return!0===i?.paddEmpty})(t,r)),vN=t=>{let e;for(let n=t;n;n=n.parent){const o=n.attr("contenteditable");if("false"===o)break;"true"===o&&(e=n)}return S.from(e)},ag=(t,e,n=t.parent)=>{if(e.getSpecialElements()[t.name])t.empty().remove();else{const o=t.children();for(const r of o)n&&!e.isValidChild(n.name,r.name)&&ag(r,e,n);t.unwrap()}},sg=(t,e,n,o=jt)=>{const r=e.getTextBlockElements(),a=e.getNonEmptyElements(),s=e.getWhitespaceElements(),i=ot.makeMap("tr,td,th,tbody,thead,tfoot,table"),l=new Set,c=d=>d!==n&&!i[d.name];for(let d=0;d<t.length;d++){const u=t[d];let m,p,g;if(!u.parent||l.has(u))continue;if(r[u.name]&&"li"===u.parent.name){let f=u.next;for(;f&&r[f.name];)f.name="li",l.add(f),u.parent.insert(f,u.parent),f=f.next;u.unwrap();continue}const h=[u];for(m=u.parent;m&&!e.isValidChild(m.name,u.name)&&c(m);m=m.parent)h.push(m);if(m&&h.length>1)if(e.isValidChild(m.name,u.name)){h.reverse(),p=h[0].clone(),o(p);let f=p;for(let b=0;b<h.length-1;b++){e.isValidChild(f.name,h[b].name)&&b>0?(g=h[b].clone(),o(g),f.append(g)):g=f;for(let y=h[b].firstChild;y&&y!==h[b+1];){const C=y.next;g.append(y),y=C}f=g}pl(e,a,s,p)?m.insert(u,h[0],!0):(m.insert(p,h[0],!0),m.insert(u,p)),m=h[0],(pl(e,a,s,m)||k0(m,"br"))&&m.empty().remove()}else ag(u,e);else if(u.parent){if("li"===u.name){let f=u.prev;if(f&&("ul"===f.name||"ol"===f.name)){f.append(u);continue}if(f=u.next,f&&("ul"===f.name||"ol"===f.name)&&f.firstChild){f.insert(u,f.firstChild,!0);continue}const b=new Pn("ul",1);o(b),u.wrap(b);continue}if(e.isValidChild(u.parent.name,"div")&&e.isValidChild("div",u.name)){const f=new Pn("div",1);o(f),u.wrap(f)}else ag(u,e)}}},E0=(t,e,n=e.parent)=>!(!n||!t.children[e.name]||t.isValidChild(n.name,e.name))||!(!n||"a"!==e.name||!(o=>{let a=o;for(;a;){if("a"===a.name)return!0;a=a.parent}return!1})(n)),ig=t=>t.collapsed?t:(e=>{const n=$.fromRangeStart(e),o=$.fromRangeEnd(e),r=e.commonAncestorContainer;return oo(!1,r,o).map(a=>!rr(n,o,r)&&rr(n,a,r)?((s,i,l,c)=>{const d=document.createRange();return d.setStart(s,i),d.setEnd(l,c),d})(n.container(),n.offset(),a.container(),a.offset()):e).getOr(e)})(t),lg=(t,e)=>{const n=t.getParent(e,t.isBlock);return n&&"LI"===n.nodeName?n:null},_0=(t,e)=>{const n=$.after(t),o=sr(e).prev(n);return o?o.toRange():null},xN=["pre"],cg=dc,SN=(t,e,n)=>{var o,r;const a=t.selection,s=t.dom,i=t.parser,l=n.merge,c=ba({validate:!0},t.schema),d='<span id="mce_marker" data-mce-type="bookmark">&#xFEFF;</span>';-1===e.indexOf("{$caret}")&&(e+="{$caret}"),e=e.replace(/\{\$caret\}/,d);let u=a.getRng();const m=u.startContainer,p=t.getBody();var C,k;m===p&&a.isCollapsed()&&s.isBlock(p.firstChild)&&(C=t,st(k=p.firstChild)&&!C.schema.getVoidElements()[k.nodeName])&&s.isEmpty(p.firstChild)&&(u=s.createRng(),u.setStart(p.firstChild,0),u.setEnd(p.firstChild,0),a.setRng(u)),a.isCollapsed()||(C=>{const k=C.dom,E=ig(C.selection.getRng());C.selection.setRng(E);const N=k.getParent(E.startContainer,cg);var R,I,q;R=k,I=E,st(q=N)&&q===R.getParent(I.endContainer,cg)&&cm(A(q),I)?tg(C,E,A(N)):E.startContainer===E.endContainer&&E.endOffset-E.startOffset==1&&tt(E.startContainer.childNodes[E.startOffset])?E.deleteContents():C.getDoc().execCommand("Delete",!1)})(t);const g=a.getNode(),h={context:g.nodeName.toLowerCase(),data:n.data,insert:!0},f=i.parse(e,h);if(!0===n.paste&&((t,e)=>{let n=e.firstChild,o=e.lastChild;return n&&"meta"===n.name&&(n=n.next),o&&"mce_marker"===o.attr("id")&&(o=o.prev),((r,a)=>{const s=r.getNonEmptyElements();return st(a)&&(a.isEmpty(s)||(l=a,r.getBlockElements()[l.name]&&st((c=l).firstChild)&&c.firstChild===c.lastChild&&(c=>"br"===c.name||c.value===Ge)(l.firstChild)));var l,c})(t,o)&&(o=o?.prev),!(!n||n!==o||"ul"!==n.name&&"ol"!==n.name)})(t.schema,f)&&((C,k)=>!!lg(C,k))(s,g))return u=((t,e,n,o)=>{const r=((u,m,p)=>{const g=m.serialize(p);return(h=>{var f,b;const y=h.firstChild,C=h.lastChild;return y&&"META"===y.nodeName&&(null===(f=y.parentNode)||void 0===f||f.removeChild(y)),C&&"mce_marker"===C.id&&(null===(b=C.parentNode)||void 0===b||b.removeChild(C)),h})(u.createFragment(g))})(e,t,o),a=lg(e,n.startContainer),s=(t=>{return t.length>0&&(!(n=t[t.length-1]).firstChild||st(null==(e=n)?void 0:e.firstChild)&&e.firstChild===e.lastChild&&((o=e.firstChild).data===Ge||_e(o)))?t.slice(0,-1):t;var o,e,n})((i=r.firstChild,Ot(null!==(l=i?.childNodes)&&void 0!==l?l:[],u=>"LI"===u.nodeName)));var i,l;const c=e.getRoot(),d=u=>{const m=$.fromRangeStart(n),p=sr(e.getRoot()),g=1===u?p.prev(m):p.next(m),h=g?.getNode();return!h||lg(e,h)!==a};return a?d(1)?((u,m,p)=>{const g=u.parentNode;return g&&ot.each(m,h=>{g.insertBefore(h,u)}),((h,f)=>{const b=$.before(h),y=sr(f).next(b);return y?y.toRange():null})(u,p)})(a,s,c):d(2)?(u=a,p=c,e.insertAfter((m=s).reverse(),u),_0(m[0],p)):((u,m,p,g)=>{const h=((b,y)=>{const C=y.cloneRange(),k=y.cloneRange();return C.setStartBefore(b),k.setEndAfter(b),[C.cloneContents(),k.cloneContents()]})(u,g),f=u.parentNode;return f&&(f.insertBefore(h[0],u),ot.each(m,b=>{f.insertBefore(b,u)}),f.insertBefore(h[1],u),f.removeChild(u)),_0(m[m.length-1],p)})(a,s,c,n):null;var u,m,p})(c,s,a.getRng(),f),u&&a.setRng(u),e;!0===n.paste&&((C,k,E,N)=>{var R;const I=k.firstChild,q=k.lastChild,H=I===("bookmark"===q.attr("data-mce-type")?q.prev:q),T=Lt(xN,I.name);if(H&&T){const P="false"!==I.attr("contenteditable"),B=(null===(R=C.getParent(E,C.isBlock))||void 0===R?void 0:R.nodeName.toLowerCase())===I.name,j=S.from(ds(N,E)).forall(Po);return P&&B&&j}return!1})(s,f,g,t.getBody())&&(null===(o=f.firstChild)||void 0===o||o.unwrap()),(C=>{let k=C;for(;k=k.walk();)1===k.type&&k.attr("data-mce-fragment","1")})(f);let b=f.lastChild;if(b&&"mce_marker"===b.attr("id")){const C=b;for(b=b.prev;b;b=b.walk(!0))if(3===b.type||!s.isBlock(b.name)){b.parent&&t.schema.isValidChild(b.parent.name,"span")&&b.parent.insert(C,b,"br"===b.name);break}}if(t._selectionOverrides.showBlockCaretContainer(g),h.invalid){t.selection.setContent(d);let C,k=a.getNode();const E=t.getBody();for(Di(k)?k=C=E:C=k;C&&C!==E;)k=C,C=C.parentNode;e=k===E?E.innerHTML:s.getOuterHTML(k);const N=i.parse(e),R=(P=>{for(let B=P;B;B=B.walk())if("mce_marker"===B.attr("id"))return S.some(B);return S.none()})(N),I=R.bind(vN).getOr(N);R.each(P=>P.replace(f));const q=f.children(),H=null!==(r=f.parent)&&void 0!==r?r:N;f.unwrap();const T=Ot(q,P=>E0(t.schema,P,H));sg(T,t.schema,I),S0(i.getNodeFilters(),i.getAttributeFilters(),N),e=c.serialize(N),k===E?s.setHTML(E,e):s.setOuterHTML(k,e)}else e=c.serialize(f),((C,k,E)=>{var N;if("all"===E.getAttribute("data-mce-bogus"))null===(N=E.parentNode)||void 0===N||N.insertBefore(C.dom.createFragment(k),E);else{const R=E.firstChild;!R||R===E.lastChild&&"BR"===R.nodeName?C.dom.setHTML(E,k):C.selection.setContent(k,{no_events:!0})}})(t,e,g);var y;return((C,k)=>{const E=C.schema.getTextInlineElements(),N=C.dom;if(k){const R=C.getBody(),I=og(C);ot.each(N.select("*[data-mce-fragment]"),q=>{if(st(E[q.nodeName.toLowerCase()])&&_n(Lm(N,q),P=>!Bv.has(P)))for(let H=q.parentElement;st(H)&&H!==R&&!X_(N,q,H);H=H.parentElement)if(I.compare(H,q)){N.remove(q,!0);break}})}})(t,l),((C,k)=>{var E,N,R;let I;const q=C.dom,H=C.selection;if(!k)return;H.scrollIntoView(k);const T=ds(C.getBody(),k);if(T&&"false"===q.getContentEditable(T))return q.remove(k),void H.select(T);let P=q.createRng();const B=k.previousSibling;if(tt(B)){P.setStart(B,null!==(N=null===(E=B.nodeValue)||void 0===E?void 0:E.length)&&void 0!==N?N:0);const lt=k.nextSibling;tt(lt)&&(B.appendData(lt.data),null===(R=lt.parentNode)||void 0===R||R.removeChild(lt))}else P.setStartBefore(k),P.setEndBefore(k);const j=q.getParent(k,q.isBlock);q.remove(k),j&&q.isEmpty(j)&&(x(A(j)),P.setStart(j,0),P.setEnd(j,0),cg(j)||j.getAttribute("data-mce-fragment")||!(I=(lt=>{let Z=$.fromRangeStart(lt);return Z=sr(C.getBody()).next(Z),Z?.toRange()})(P))?q.add(j,q.create("br",{"data-mce-bogus":"1"})):(P=I,q.remove(j))),H.setRng(P)})(t,s.get("mce_marker")),y=t.getBody(),ot.each(y.getElementsByTagName("*"),C=>{C.removeAttribute("data-mce-fragment")}),((C,k)=>{S.from(C.getParent(k,"td,th")).map(A).each(XS)})(s,a.getStart()),(C=>{J(Ae(C.getBody().querySelectorAll("details")),k=>{const E=Ot(Ae(k.children),N=>"SUMMARY"===N.nodeName);E.length>1&&J(E.slice(1),N=>{const R=A(N);Br(R,"mce-accordion-summary"),Qp(R,"p")})})})(t),((C,k,E)=>{const N=tc(A(E),R=>re(R,A(k)));Wo(N,N.length-2).filter(fn).fold(()=>xu(C,k),R=>xu(C,R.dom))})(t.schema,t.getBody(),a.getStart()),e},nd=t=>t instanceof Pn,dg=(t,e,n)=>{var o;t.dom.setHTML(t.getBody(),e),!0!==n&&is(o=t)&&Cn(o.getBody()).each(r=>{const a=r.getNode(),s=Ir(a)?Cn(a).getOr(r):r;o.selection.setRng(s.toRange())})},N0=t=>Yt(t)?t:de,ug=(t,e,n)=>{const o=e(t),r=N0(n);return o.orThunk(()=>r(t)?S.none():((a,s,i)=>{let l=a.dom;const c=N0(i);for(;l.parentNode;){l=l.parentNode;const d=A(l),u=s(d);if(u.isSome())return u;if(c(d))break}return S.none()})(t,e,r))},mg=pm,R0=(t,e,n)=>{const o=t.formatter.get(n);if(o)for(let r=0;r<o.length;r++){const a=o[r];if(ao(a)&&!1===a.inherit&&t.dom.is(e,a.selector))return!0}return!1},gg=(t,e,n,o,r)=>{const a=t.dom.getRoot();if(e===a)return!1;const s=t.dom.getParent(e,i=>!!R0(t,i,n)||i.parentNode===a||!!lr(t,i,n,o,!0));return!!lr(t,s,n,o,r)},od=(t,e,n)=>!(!$e(n)||!mg(e,n.inline))||!(!ir(n)||!mg(e,n.block))||!!ao(n)&&it(e)&&t.is(e,n.selector),A0=(t,e,n,o,r,a)=>{const s=n[o],i="attributes"===o;if(Yt(n.onmatch))return n.onmatch(e,n,o);if(s)if(Ba(s)){for(let l=0;l<s.length;l++)if(i?t.getAttrib(e,s[l]):jc(t,e,s[l]))return!0}else for(const l in s)if(It(s,l)){const c=i?t.getAttrib(e,l):jc(t,e,l),d=ha(s[l],a),u=pe(c)||Xt(c);if(u&&pe(d))continue;if(r&&u&&!n.exact||(!r||n.exact)&&!mg(c,hm(d,l)))return!1}return!0},lr=(t,e,n,o,r)=>{const a=t.formatter.get(n),s=t.dom;if(a&&it(e))for(let i=0;i<a.length;i++){const l=a[i];if(od(t.dom,e,l)&&A0(s,e,l,"attributes",r,o)&&A0(s,e,l,"styles",r,o)){const c=l.classes;if(c)for(let d=0;d<c.length;d++)if(!t.dom.hasClass(e,ha(c[d],o)))return;return l}}},fg=(t,e,n,o,r)=>{if(o)return gg(t,o,e,n,r);if(o=t.selection.getNode(),gg(t,o,e,n,r))return!0;const a=t.selection.getStart();return!(a===o||!gg(t,a,e,n,r))},Zs=dn,T0=t=>(e=>{const n=[];let o=e;for(;o;){if(tt(o)&&o.data!==Zs||o.childNodes.length>1)return[];it(o)&&n.push(o),o=o.firstChild}return n})(t).length>0,O0=t=>{if(t){const e=new Kt(t,t);for(let n=e.current();n;n=e.next())if(tt(n))return n}return null},pg=t=>{const e=Be("span");return Do(e,{id:om,"data-mce-bogus":"1","data-mce-type":"format-caret"}),t&&Se(e,bo(Zs)),e},hg=(t,e,n=!0)=>{const o=t.dom,r=t.selection;if(T0(e))ls(t,!1,A(e),n);else{const a=r.getRng(),s=o.getParent(e,o.isBlock),i=a.startContainer,l=a.startOffset,c=a.endContainer,d=a.endOffset,u=(m=>{const p=O0(m);return p&&p.data.charAt(0)===Zs&&p.deleteData(0,1),p})(e);o.remove(e,!0),i===u&&l>0&&a.setStart(u,l-1),c===u&&d>0&&a.setEnd(u,d-1),s&&o.isEmpty(s)&&la(A(s)),r.setRng(a)}},bg=(t,e,n=!0)=>{const o=t.dom,r=t.selection;if(e)hg(t,e,n);else if(!(e=ns(t.getBody(),r.getStart())))for(;e=o.get(om);)hg(t,e,n)},B0=(t,e)=>(t.appendChild(e),e),D0=(t,e)=>{var n;const o=Kn(t,(a,s)=>B0(a,s.cloneNode(!1)),e),r=null!==(n=o.ownerDocument)&&void 0!==n?n:document;return B0(o,r.createTextNode(Zs))},P0=t=>{const e=pg(!1),n=D0(t,e.dom);return{caretContainer:e,caretPosition:$(n,0)}},M0=(t,e)=>{const{caretContainer:n,caretPosition:o}=P0(e);return bn(A(t),n),w(A(t)),o},L0=(t,e)=>{const n=t.schema.getTextInlineElements();return It(n,ge(e))&&!_o(e.dom)&&!$a(e.dom)},I0=t=>_o(t.dom)&&T0(t.dom),hl={},F0=ke(["pre"]);(t=>{hl[t]||(hl[t]=[]),hl[t].push(n=>{if(!n.selection.getRng().collapsed){const o=n.selection.getSelectedBlocks(),r=Ot(Ot(o,F0),(a=>s=>{const i=s.previousSibling;return F0(i)&&Lt(a,i)})(o));J(r,a=>{((s,i)=>{const l=A(i),c=yo(l).dom;w(l),v(A(s),[Be("br",c),Be("br",c),...ze(l)])})(a.previousSibling,a)})}})})("pre");const U0=["fontWeight","fontStyle","color","fontSize","fontFamily"],j0=(t,e)=>{const n=t.get(e);return Ee(n)?he(n,o=>{return $e(o)&&"span"===o.inline&&At((r=o).styles)&&ie(tn(r.styles),a=>Lt(U0,a));var r}):S.none()},z0=(t,e)=>Dn(e,$.fromRangeStart(t)).isNone(),H0=(t,e)=>!1===yn(e,$.fromRangeEnd(t)).exists(n=>!_e(n.getNode())||yn(e,n).isSome()),$0=t=>e=>qS(e)&&t.isEditable(e),q0=t=>Ot(t.getSelectedBlocks(),$0(t.dom)),vg=ot.each,yg=t=>it(t)&&!ro(t)&&!_o(t)&&!$a(t),V0=(t,e)=>{for(let n=t;n;n=n[e]){if(tt(n)&&Qt(n.data))return t;if(it(n)&&!ro(n))return n}return t},W0=(t,e,n)=>{const o=og(t),r=it(e)&&os(e),a=it(n)&&os(n);if(r&&a){const s=V0(e,"previousSibling"),i=V0(n,"nextSibling");if(o.compare(s,i)){for(let l=s.nextSibling;l&&l!==i;){const c=l;l=l.nextSibling,s.appendChild(c)}return t.dom.remove(i),ot.each(ot.grep(i.childNodes),l=>{s.appendChild(l)}),s}}return n},K0=(t,e,n,o)=>{var r;if(o&&!1!==e.merge_siblings){const a=null!==(r=W0(t,kb(o),o))&&void 0!==r?r:o;W0(t,a,kb(a,!0))}},Cg=(t,e,n)=>{vg(t.childNodes,o=>{yg(o)&&(e(o)&&n(o),o.hasChildNodes()&&Cg(o,e,n))})},G0=(t,e)=>n=>!(!n||!jc(t,n,e)),Y0=(t,e,n)=>o=>{var r,a;t.setStyle(o,e,n),""===o.getAttribute("style")&&o.removeAttribute("style"),r=t,"SPAN"===(a=o).nodeName&&0===r.getAttribs(a).length&&r.remove(a,!0)},us=or([{keep:[]},{rename:["name"]},{removed:[]}]),_N=/^(src|href|style)$/,wg=ot.each,rd=pm,X0=(t,e,n)=>t.isChildOf(e,n)&&e!==n&&!t.isBlock(n),Q0=(t,e,n)=>{let o=e[n?"startContainer":"endContainer"],r=e[n?"startOffset":"endOffset"];if(it(o)){const a=o.childNodes.length-1;!n&&r&&r--,o=o.childNodes[r>a?a:r]}return tt(o)&&n&&r>=o.data.length&&(o=new Kt(o,t.getBody()).next()||o),tt(o)&&!n&&0===r&&(o=new Kt(o,t.getBody()).prev()||o),o},J0=(t,e)=>{const n=e?"firstChild":"lastChild",o=t[n];return/^(TR|TH|TD)$/.test(t.nodeName)&&o?"TR"===t.nodeName&&o[n]||o:t},xg=(t,e,n,o)=>{var r;const a=t.create(n,o);return null===(r=e.parentNode)||void 0===r||r.insertBefore(a,e),a.appendChild(e),a},Z0=(t,e,n,o,r)=>{const a=A(e),s=A(t.create(o,r)),i=n?Ni(a):ec(a);return v(s,i),n?(bn(a,s),Ha(s,a)):(to(a,s),Se(s,a)),s.dom},ty=(t,e,n)=>{const o=e.parentNode;let r;const a=t.dom,s=Bn(t);var i;ir(n)&&o===a.getRoot()&&(n.list_block&&rd(e,n.list_block)||J(Ae(e.childNodes),i=>{Ys(t,s,i.nodeName.toLowerCase())?r?r.appendChild(i):(r=xg(a,i,s),a.setAttribs(r,Yi(t))):r=null})),ao(i=n)&&$e(i)&&tr(ce(i,"mixed"),!0)&&!rd(n.inline,e)||a.remove(e,!0)},ey=(t,e,n)=>nn(t)?{name:e,value:null}:{name:t,value:ha(e,n)},ny=(t,e)=>{""===t.getAttrib(e,"style")&&(e.removeAttribute("style"),e.removeAttribute("data-mce-style"))},oy=(t,e,n,o,r)=>{let a=!1;wg(n.styles,(s,i)=>{const{name:l,value:c}=ey(i,s,o),d=hm(c,l);(n.remove_similar||Oe(c)||!it(r)||rd(jc(t,r,l),d))&&t.setStyle(e,l,""),a=!0}),a&&ny(t,e)},ry=(t,e,n,o,r)=>{const a=t.dom,s=og(t),i=t.schema;if($e(e)&&yc(i,e.inline)&&Wa(i,o)&&o.parentElement===t.getBody())return ty(t,o,e),us.removed();if(!e.ceFalseOverride&&o&&"false"===a.getContentEditableParent(o)||o&&!od(a,o,e)&&(!e.links||"A"!==o.nodeName))return us.keep();const l=o,c=e.preserve_attributes;if($e(e)&&"all"===e.remove&&Ee(c)){const d=Ot(a.getAttribs(l),u=>Lt(c,u.name.toLowerCase()));if(a.removeAllAttribs(l),J(d,u=>a.setAttrib(l,u.name,u.value)),d.length>0)return us.rename("span")}if("all"!==e.remove){oy(a,l,e,n,r),wg(e.attributes,(u,m)=>{const{name:p,value:g}=ey(m,u,n);if(e.remove_similar||Oe(g)||!it(r)||rd(a.getAttrib(r,p),g)){if("class"===p){const h=a.getAttrib(l,p);if(h){let f="";if(J(h.split(/\s+/),b=>{/mce\-\w+/.test(b)&&(f+=(f?" ":"")+b)}),f)return void a.setAttrib(l,p,f)}}if(_N.test(p)&&l.removeAttribute("data-mce-"+p),"style"===p&&ke(["li"])(l)&&"none"===a.getStyle(l,"list-style-type"))return l.removeAttribute(p),void a.setStyle(l,"list-style-type","none");"class"===p&&l.removeAttribute("className"),l.removeAttribute(p)}}),wg(e.classes,u=>{u=ha(u,n),it(r)&&!a.hasClass(r,u)||a.removeClass(l,u)});const d=a.getAttribs(l);for(let u=0;u<d.length;u++)if(!s.isAttributeInternal(d[u].nodeName))return us.keep()}return"none"!==e.remove?(ty(t,l,e),us.removed()):us.keep()},NN=(t,e,n,o)=>ry(t,e,n,o,o).fold(ut(o),r=>(t.dom.createFragment().appendChild(o),t.dom.rename(o,r)),ut(null)),ay=(t,e,n,o,r)=>{(o||t.selection.isEditable())&&((a,s,i,l,c)=>{const d=a.formatter.get(s),u=d[0],m=a.dom,p=a.selection,g=C=>{const k=((E,N,R,I,q)=>{let H;return N.parentNode&&J(zc(E.dom,N.parentNode).reverse(),T=>{if(!H&&it(T)&&"_start"!==T.id&&"_end"!==T.id){const P=lr(E,T,R,I,q);P&&!1!==P.split&&(H=T)}}),H})(a,C,s,i,c);return((E,N,R,I,q,H,T,P)=>{var B,j;let lt,Z;const _t=E.dom;if(R){const mt=R.parentNode;for(let Ut=I.parentNode;Ut&&Ut!==mt;Ut=Ut.parentNode){let Ht=_t.clone(Ut,!1);for(let ft=0;ft<N.length&&(Ht=NN(E,N[ft],P,Ht),null!==Ht);ft++);Ht&&(lt&&Ht.appendChild(lt),Z||(Z=Ht),lt=Ht)}T.mixed&&_t.isBlock(R)||(I=null!==(B=_t.split(R,I))&&void 0!==B?B:I),lt&&Z&&(null===(j=q.parentNode)||void 0===j||j.insertBefore(lt,q),Z.appendChild(q),$e(T)&&K0(E,T,0,lt))}return I})(a,d,k,C,C,0,u,i)},h=C=>ie(d,k=>bl(a,k,i,C,C)),f=C=>{const k=Ae(C.childNodes),E=h(C)||ie(d,R=>od(m,C,R)),N=C.parentNode;if(!E&&st(N)&&vm(u)&&h(N),u.deep&&k.length)for(let R=0;R<k.length;R++)f(k[R]);J(["underline","line-through","overline"],R=>{it(C)&&a.dom.getStyle(C,"text-decoration")===R&&C.parentNode&&_b(m,C.parentNode)===R&&bl(a,{deep:!1,exact:!0,inline:"span",styles:{textDecoration:R}},void 0,C)})},b=C=>{const k=m.get(C?"_start":"_end");if(k){let E=k[C?"firstChild":"lastChild"];return ro(N=E)&&it(N)&&("_start"===N.id||"_end"===N.id)&&(E=E[C?"firstChild":"lastChild"]),tt(E)&&0===E.data.length&&(E=C?k.previousSibling||k.nextSibling:k.nextSibling||k.previousSibling),m.remove(k,!0),E}var N;return null},y=C=>{let k,E,N=rs(m,C,d,C.collapsed);if(u.split){if(N=Gc(N),k=Q0(a,N,!0),E=Q0(a,N),k!==E){if(k=J0(k,!0),E=J0(E,!1),X0(m,k,E)){const I=S.from(k.firstChild).getOr(k);return g(Z0(m,I,!0,"span",{id:"_start","data-mce-type":"bookmark"})),void b(!0)}if(X0(m,E,k)){const I=S.from(E.lastChild).getOr(E);return g(Z0(m,I,!1,"span",{id:"_end","data-mce-type":"bookmark"})),void b(!1)}k=xg(m,k,"span",{id:"_start","data-mce-type":"bookmark"}),E=xg(m,E,"span",{id:"_end","data-mce-type":"bookmark"});const R=m.createRng();R.setStartAfter(k),R.setEndBefore(E),sl(m,R,I=>{J(I,q=>{ro(q)||ro(q.parentNode)||g(q)})}),g(k),g(E),k=b(!0),E=b()}else k=E=g(k);N.startContainer=k.parentNode?k.parentNode:k,N.startOffset=m.nodeIndex(k),N.endContainer=E.parentNode?E.parentNode:E,N.endOffset=m.nodeIndex(E)+1}sl(m,N,R=>{J(R,f)})};if(l){if(al(l)){const C=m.createRng();C.setStartBefore(l),C.setEndAfter(l),y(C)}else y(l);Hb(a,s,l,i)}else p.isCollapsed()&&$e(u)&&!Gs(a).length?((t,e,n,o)=>{const r=t.dom,a=t.selection;let s=!1;const i=t.formatter.get(e);if(!i)return;const l=a.getRng(),c=l.startContainer,d=l.startOffset;let u=c;tt(c)&&(d!==c.data.length&&(s=!0),u=u.parentNode);const m=[];let p;for(;u;){if(lr(t,u,e,n,o)){p=u;break}u.nextSibling&&(s=!0),m.push(u),u=u.parentNode}if(p)if(s){const g=a.getBookmark();l.collapse(!0);let h=rs(r,l,i,!0);h=Gc(h),t.formatter.remove(e,n,h,o),a.moveToBookmark(g)}else{const g=ns(t.getBody(),p),h=pg(!1).dom;((y,C,k)=>{var E,N;const R=y.dom,I=R.getParent(k,Ct(gm,y.schema));I&&R.isEmpty(I)?null===(E=k.parentNode)||void 0===E||E.replaceChild(C,k):((q=>{const H=Rt(q,"br"),T=Ot((P=>{const B=[];let j=P.dom;for(;j;)B.push(A(j)),j=j.lastChild;return B})(q).slice(-1),qa);H.length===T.length&&J(T,w)})(A(k)),R.isEmpty(k)?null===(N=k.parentNode)||void 0===N||N.replaceChild(C,k):R.insertAfter(C,k))})(t,h,g??p);const f=((y,C,k,E,N,R)=>{const I=y.formatter,q=y.dom,H=Ot(tn(I.get()),P=>P!==E&&!ct(P,"removeformat")),T=(P=y,B=k,Re(H,(lt,Z)=>{const _t=bm(P,Z,Ht=>{const ft=kt=>Yt(kt)||kt.length>1&&"%"===kt.charAt(0);return ie(["styles","attributes"],kt=>ce(Ht,kt).exists(Bt=>{const O=Ee(Bt)?Bt:Er(Bt);return ie(O,ft)}))});return P.formatter.matchNode(B,Z,{},_t)?lt.concat([Z]):lt},[]));var P,B;if(Ot(T,P=>!((B,j,lt)=>{const Z=["inline","block","selector","attributes","styles","classes"],_t=mt=>Go(mt,(Ut,Ht)=>ie(Z,ft=>ft===Ht));return bm(B,j,mt=>{const Ut=_t(mt);return bm(B,lt,Ht=>{const ft=_t(Ht);return((kt,Bt,O=br)=>hr(O).eq(kt,Bt))(Ut,ft)})})})(y,P,E)).length>0){const P=k.cloneNode(!1);return q.add(C,P),I.remove(E,N,P,R),q.remove(P),S.some(P)}return S.none()})(t,h,p,e,n,o),b=D0(m.concat(f.toArray()),h);g&&hg(t,g,!1),a.setCursorLocation(b,1),r.isEmpty(p)&&r.remove(p)}})(a,s,i,c):(Sb(a,()=>um(a,y),C=>$e(u)&&fg(a,s,i,C)),a.nodeChanged()),C=a,E=i,"removeformat"===(k=s)?J(q0(C.selection),N=>{J(U0,R=>C.dom.setStyle(N,R,"")),ny(C.dom,N)}):j0(C.formatter,k).each(N=>{J(q0(C.selection),R=>oy(C.dom,R,N,E,null))}),Hb(a,s,l,i);var C,k,E})(t,e,n,o,r)},bl=(t,e,n,o,r)=>ry(t,e,n,o,r).fold(de,a=>(t.dom.rename(o,a),!0),ye),sy=ot.each,ad=ot.each,iy=(t,e,n,o)=>{if(ad(n.styles,(r,a)=>{t.setStyle(e,a,ha(r,o))}),n.styles){const r=t.getAttrib(e,"style");r&&t.setAttrib(e,"data-mce-style",r)}},RN=(t,e,n,o)=>{const r=t.formatter.get(e),a=r[0],s=!o&&t.selection.isCollapsed(),i=t.dom,l=t.selection,c=(g,h=a)=>{Yt(h.onformat)&&h.onformat(g,h,n,o),iy(i,g,h,n),ad(h.attributes,(f,b)=>{i.setAttrib(g,b,ha(f,n))}),ad(h.classes,f=>{const b=ha(f,n);i.hasClass(g,b)||i.addClass(g,b)})},d=(g,h)=>{let f=!1;return ad(g,b=>!(!ao(b)||("false"!==i.getContentEditable(h)||b.ceFalseOverride)&&(!st(b.collapsed)||b.collapsed===s)&&i.is(h,b.selector)&&!_o(h)&&(c(h,b),f=!0,1))),f},m=(g,h,f)=>{const b=[];let y=!0;const C=a.inline||a.block,k=(g=>{if(Nt(g)){const h=i.create(g);return c(h),h}return null})(C);sl(g,h,E=>{let N;const R=I=>{let q=!1,H=y,T=!1;const P=I.parentNode,B=P.nodeName.toLowerCase(),j=g.getContentEditable(I);st(j)&&(H=y,y="true"===j,q=!0,T=Eb(t,I));const lt=y&&!q;if(_e(I)&&!((Z,_t,mt,Ut)=>{if(mE(Z)&&$e(_t)&&mt.parentNode){const Ht=Ip(Z.schema),ft=((t,e)=>((n,o)=>{const r=n.dom;return r.parentNode?(a=A(r.parentNode),he(a.dom.childNodes,i=>(a=>!re(n,a)&&o(a))(A(i))).map(A)):S.none();var a})(t,e).isSome())(A(mt),kt=>_o(kt.dom));return Hn(Ht,Ut)&&De(A(mt.parentNode),!1)&&!ft}return!1})(t,a,I,B))return N=null,void(ir(a)&&g.remove(I));if(Z=I,ir(_t=a)&&!0===_t.wrapper&&lr(t,Z,e,n))N=null;else{if(((Z,_t,mt)=>{const Ut=ir(Ht=a)&&!0!==Ht.wrapper&&gm(t.schema,Z)&&Ys(t,_t,C);var Ht;return mt&&Ut})(I,B,lt)){const Z=g.rename(I,C);return c(Z),b.push(Z),void(N=null)}if(ao(a)){let Z=d(r,I);if(!Z&&st(P)&&vm(a)&&(Z=d(r,P)),!$e(a)||Z)return void(N=null)}st(k)&&((Z,_t,mt,Ut)=>{const Ht=Z.nodeName.toLowerCase(),ft=Ys(t,C,Ht)&&Ys(t,_t,C),kt=!f&&tt(Z)&&pc(Z.data),Bt=_o(Z),O=!$e(a)||!g.isBlock(Z);return(mt||Ut)&&ft&&!kt&&!Bt&&O})(I,B,lt,T)?(N||(N=g.clone(k,!1),P.insertBefore(N,I),b.push(N)),T&&q&&(y=H),N.appendChild(I)):(N=null,J(Ae(I.childNodes),R),q&&(y=H),N=null)}var Z,_t};J(E,R)}),!0===a.links&&J(b,E=>{const N=R=>{"A"===R.nodeName&&c(R,a),J(Ae(R.childNodes),N)};N(E)}),J(b,E=>{const N=(R=>{let I=0;return J(R.childNodes,q=>{var H;st(H=q)&&tt(H)&&0===H.length||ro(q)||I++}),I})(E);var R;!(b.length>1)&&g.isBlock(E)||0!==N?($e(a)||ir(a)&&a.wrapper)&&(a.exact||1!==N||(E=he((R=E).childNodes,mm).filter(q=>"false"!==g.getContentEditable(q)&&od(g,q,a)).map(q=>{const H=g.clone(q,!1);return c(H),g.replace(H,R,!0),g.remove(q,!0),H}).getOr(R)),((R,I,q,H)=>{sy(I,T=>{var P,B,j;$e(T)&&sy(R.dom.select(T.inline,H),P=>{yg(P)&&bl(R,T,q,P,T.exact?P:null)}),P=R.dom,j=H,(B=T).clear_child_styles&&vg(P.select(B.links?"*:not(a)":"*",j),Z=>{yg(Z)&&os(Z)&&vg(B.styles,(_t,mt)=>{P.setStyle(Z,mt,"")})})})})(t,r,n,E),((R,I,q,H,T)=>{const P=T.parentNode;lr(R,P,q,H)&&bl(R,I,H,T)||I.merge_with_parents&&P&&R.dom.getParent(P,B=>!!lr(R,B,q,H)&&(bl(R,I,H,T),!0))})(t,a,e,n,E),((R,I,q,H)=>{if(I.styles&&I.styles.backgroundColor){const T=G0(R,"fontSize");Cg(H,P=>T(P)&&os(P),Y0(R,"backgroundColor",ha(I.styles.backgroundColor,q)))}})(g,a,n,E),((R,I,q,H)=>{const T=P=>{if(it(P)&&it(P.parentNode)&&os(P)){const B=_b(R,P.parentNode);R.getStyle(P,"color")&&B?R.setStyle(P,"text-decoration",B):R.getStyle(P,"text-decoration")===B&&R.setStyle(P,"text-decoration",null)}};I.styles&&(I.styles.color||I.styles.textDecoration)&&(ot.walk(H,T,"childNodes"),T(H))})(g,a,0,E),((R,I,q,H)=>{if($e(I)&&("sub"===I.inline||"sup"===I.inline)){const T=G0(R,"fontSize");Cg(H,B=>T(B)&&os(B),Y0(R,"fontSize",""));const P=Ot(R.select("sup"===I.inline?"sub":"sup",H),os);R.remove(P,!0)}})(g,a,0,E),K0(t,a,0,E)):g.remove(E,!0)})},p=al(o)?o:l.getNode();if("false"===i.getContentEditable(p)&&!Eb(t,p))return d(r,o=p),void zb(t,e,o,n);if(a){if(o)if(al(o)){if(!d(r,o)){const g=i.createRng();g.setStartBefore(o),g.setEndAfter(o),m(i,rs(i,g,r),!0)}}else m(i,o,!0);else s&&$e(a)&&!Gs(t).length?((g,h,f)=>{let b;const y=g.selection,C=g.formatter.get(h);if(!C)return;const k=y.getRng();let E=k.startOffset;const N=k.startContainer.nodeValue;b=ns(g.getBody(),y.getStart());const R=/[^\s\u00a0\u00ad\u200b\ufeff]/;if(N&&E>0&&E<N.length&&R.test(N.charAt(E))&&R.test(N.charAt(E-1))){const H=y.getBookmark();k.collapse(!0);let T=rs(g.dom,k,C);T=Gc(T),g.formatter.apply(h,f,T),y.moveToBookmark(H)}else{let H=b?O0(b):null;b&&H?.data===Zs||(I=g.getDoc(),q=pg(!0).dom,b=I.importNode(q,!0),H=b.firstChild,k.insertNode(b),E=1),g.formatter.apply(h,f,b),y.setCursorLocation(H,E)}var I,q})(t,e,n):(l.setRng(ig(l.getRng())),Sb(t,()=>{um(t,(g,h)=>{const f=h?g:rs(i,g,r);m(i,f,!1)})},ye),t.nodeChanged()),j0(t.formatter,e).each(g=>{var h;J(Ot((f=>{const b=f.getSelectedBlocks(),y=f.getRng();if(f.isCollapsed())return[];if(1===b.length)return z0(y,b[0])&&H0(y,b[0])?b:[];{const C=We(b).filter(N=>z0(y,N)).toArray(),k=Nn(b).filter(N=>H0(y,N)).toArray(),E=b.slice(1,-1);return C.concat(E).concat(k)}})(h=t.selection),$0(h.dom)),h=>iy(i,h,g,n))});h=t,It(hl,g=e)&&J(hl[g],f=>{f(h)})}var g,h;zb(t,e,o,n)},ly=(t,e,n,o)=>{(o||t.selection.isEditable())&&RN(t,e,n,o)},cy=t=>It(t,"vars"),dy=t=>t.selection.getStart(),uy=(t,e,n,o,r)=>yr(e,a=>{const s=t.formatter.matchNode(a,n,r??{},o);return!dt(s)},a=>!!R0(t,a,n)||!o&&st(t.formatter.matchNode(a,n,r,!0))),my=(t,e)=>{const n=e??dy(t);return Ot(zc(t.dom,n),o=>it(o)&&!$a(o))},gy=(t,e,n)=>{const o=my(t,e);le(n,(r,a)=>{const s=i=>{const l=uy(t,o,a,i.similar,cy(i)?i.vars:void 0),c=l.isSome();if(i.state.get()!==c){i.state.set(c);const d=l.getOr(e);cy(i)?i.callback(c,{node:d,format:a,parents:o}):J(i.callbacks,u=>u(c,{node:d,format:a,parents:o}))}};J([r.withSimilar,r.withoutSimilar],s),J(r.withVars,s)})},fy=ot.explode,py=()=>{const t={};return{addFilter:(e,n)=>{J(fy(e),o=>{It(t,o)||(t[o]={name:o,callbacks:[]}),t[o].callbacks.push(n)})},getFilters:()=>Er(t),removeFilter:(e,n)=>{J(fy(e),o=>{if(It(t,o))if(st(n)){const r=t[o],a=Ot(r.callbacks,s=>s!==n);a.length>0?r.callbacks=a:delete t[o]}else delete t[o]})}}},hy=(t,e)=>{t.addNodeFilter("br",(n,o,r)=>{const a=ot.extend({},e.getBlockElements()),s=e.getNonEmptyElements(),i=e.getWhitespaceElements();a.body=1;const l=c=>{return c.name in a&&(1===(u=c).type&&yc(e,u.name)&&dt(u.attr(Li)));var u};for(let c=0,d=n.length;c<d;c++){let u=n[c],m=u.parent;if(m&&a[m.name]&&u===m.lastChild){let p=u.prev;for(;p;){const g=p.name;if("span"!==g||"bookmark"!==p.attr("data-mce-type")){"br"===g&&(u=null);break}p=p.prev}if(u&&(u.remove(),pl(e,s,i,m))){const g=e.getElementRule(m.name);g&&(g.removeEmpty?m.remove():g.paddEmpty&&rg(r,l,m))}}else{let p=u;for(;m&&m.firstChild===p&&m.lastChild===p&&(p=m,!a[m.name]);)m=m.parent;if(p===m){const g=new Pn("#text",3);g.value=Ge,u.replace(g)}}}})},Sg=t=>{const[e,...n]=t.split(","),o=n.join(","),r=/data:([^/]+\/[^;]+)(;.+)?/.exec(e);if(r){const a=";base64"===r[2],s=a?(i=>{const l=/([a-z0-9+\/=\s]+)/i.exec(i);return l?l[1]:""})(o):decodeURIComponent(o);return S.some({type:r[1],data:s,base64Encoded:a})}return S.none()},by=(t,e,n=!0)=>{let o=e;if(n)try{o=atob(e)}catch{return S.none()}const r=new Uint8Array(o.length);for(let a=0;a<r.length;a++)r[a]=o.charCodeAt(a);return S.some(new Blob([r],{type:t}))},vy=t=>new Promise((e,n)=>{const o=new FileReader;o.onloadend=()=>{e(o.result)},o.onerror=()=>{var r;n(null===(r=o.error)||void 0===r?void 0:r.message)},o.readAsDataURL(t)});let TN=0;const yy=(t,e,n)=>Sg(t).bind(({data:o,type:r,base64Encoded:a})=>{if(e&&!a)return S.none();{const s=a?o:btoa(o);return n(s,r)}}),Cy=(t,e,n)=>{const o=t.create("blobid"+TN++,e,n);return t.add(o),o},wy=(t,e,n=!1)=>yy(e,n,(o,r)=>S.from(t.getByData(o,r)).orThunk(()=>by(r,o).map(a=>Cy(t,a,o))));function ms(t){return(ms="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(t)}function kg(t,e){return(kg=Object.setPrototypeOf||function(n,o){return n.__proto__=o,n})(t,e)}function Eg(t,e,n){return(Eg=function(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}()?Reflect.construct:function(o,r,a){var s=[null];s.push.apply(s,r);var i=new(Function.bind.apply(o,s));return a&&kg(i,a.prototype),i}).apply(null,arguments)}function Io(t){return function(e){if(Array.isArray(e))return _g(e)}(t)||function(e){if(typeof Symbol<"u"&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(t)||function(e,n){if(e){if("string"==typeof e)return _g(e,n);var o=Object.prototype.toString.call(e).slice(8,-1);return"Object"===o&&e.constructor&&(o=e.constructor.name),"Map"===o||"Set"===o?Array.from(e):"Arguments"===o||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o)?_g(e,n):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _g(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,o=new Array(e);n<e;n++)o[n]=t[n];return o}var BN=Object.hasOwnProperty,xy=Object.setPrototypeOf,DN=Object.isFrozen,PN=Object.getPrototypeOf,MN=Object.getOwnPropertyDescriptor,Un=Object.freeze,cr=Object.seal,LN=Object.create,Sy=typeof Reflect<"u"&&Reflect,sd=Sy.apply,Ng=Sy.construct;sd||(sd=function(t,e,n){return t.apply(e,n)}),Un||(Un=function(t){return t}),cr||(cr=function(t){return t}),Ng||(Ng=function(t,e){return Eg(t,Io(e))});var ky,IN=Fo(Array.prototype.forEach),FN=Fo(Array.prototype.pop),vl=Fo(Array.prototype.push),id=Fo(String.prototype.toLowerCase),UN=Fo(String.prototype.match),xa=Fo(String.prototype.replace),jN=Fo(String.prototype.indexOf),zN=Fo(String.prototype.trim),jn=Fo(RegExp.prototype.test),Rg=(ky=TypeError,function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return Ng(ky,e)});function Fo(t){return function(e){for(var n=arguments.length,o=new Array(n>1?n-1:0),r=1;r<n;r++)o[r-1]=arguments[r];return sd(t,e,o)}}function se(t,e){xy&&xy(t,null);for(var n=e.length;n--;){var o=e[n];if("string"==typeof o){var r=id(o);r!==o&&(DN(e)||(e[n]=r),o=r)}t[o]=!0}return t}function gs(t){var e,n=LN(null);for(e in t)sd(BN,t,[e])&&(n[e]=t[e]);return n}function ld(t,e){for(;null!==t;){var n=MN(t,e);if(n){if(n.get)return Fo(n.get);if("function"==typeof n.value)return Fo(n.value)}t=PN(t)}return function(o){return console.warn("fallback value for",o),null}}var Ey=Un(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),Ag=Un(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),Tg=Un(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),HN=Un(["animate","color-profile","cursor","discard","fedropshadow","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),Og=Un(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover"]),$N=Un(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),_y=Un(["#text"]),Ny=Un(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","xmlns","slot"]),Bg=Un(["accent-height","accumulate","additive","alignment-baseline","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),Ry=Un(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),cd=Un(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),qN=cr(/\{\{[\w\W]*|[\w\W]*\}\}/gm),VN=cr(/<%[\w\W]*|[\w\W]*%>/gm),WN=cr(/^data-[\-\w.\u00B7-\uFFFF]/),KN=cr(/^aria-[\-\w]+$/),GN=cr(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),YN=cr(/^(?:\w+script|data):/i),XN=cr(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),QN=cr(/^html$/i),ZN=function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:typeof window>"u"?null:window,n=function(z){return t(z)};if(n.version="2.3.8",n.removed=[],!e||!e.document||9!==e.document.nodeType)return n.isSupported=!1,n;var o=e.document,r=e.document,a=e.DocumentFragment,s=e.HTMLTemplateElement,i=e.Node,l=e.Element,c=e.NodeFilter,d=e.NamedNodeMap,u=void 0===d?e.NamedNodeMap||e.MozNamedAttrMap:d,m=e.HTMLFormElement,p=e.DOMParser,g=e.trustedTypes,h=l.prototype,f=ld(h,"cloneNode"),b=ld(h,"nextSibling"),y=ld(h,"childNodes"),C=ld(h,"parentNode");if("function"==typeof s){var k=r.createElement("template");k.content&&k.content.ownerDocument&&(r=k.content.ownerDocument)}var E=function(z,zt){if("object"!==ms(z)||"function"!=typeof z.createPolicy)return null;var Tt=null,fe="data-tt-policy-suffix";zt.currentScript&&zt.currentScript.hasAttribute(fe)&&(Tt=zt.currentScript.getAttribute(fe));var Ie="dompurify"+(Tt?"#"+Tt:"");try{return z.createPolicy(Ie,{createHTML:function(mn){return mn}})}catch{return console.warn("TrustedTypes policy "+Ie+" could not be created."),null}}(g,o),N=E?E.createHTML(""):"",I=r.implementation,q=r.createNodeIterator,H=r.createDocumentFragment,T=r.getElementsByTagName,P=o.importNode,B={};try{B=gs(r).documentMode?r.documentMode:{}}catch{}var j={};n.isSupported="function"==typeof C&&I&&void 0!==I.createHTMLDocument&&9!==B;var lt,Z,_t=qN,mt=VN,Ut=WN,Ht=KN,ft=YN,kt=XN,Bt=GN,O=null,D=se({},[].concat(Io(Ey),Io(Ag),Io(Tg),Io(Og),Io(_y))),V=null,Y=se({},[].concat(Io(Ny),Io(Bg),Io(Ry),Io(cd))),W=Object.seal(Object.create(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),K=null,at=null,St=!0,vt=!0,Dt=!1,Mt=!1,gt=!1,Vt=!1,Pe=!1,xe=!1,Ln=!1,In=!1,zo=!0,No=!0,en=!1,zn={},Ne=null,fr=se({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]),Ea=null,ou=se({},["audio","video","img","source","image","track"]),pr=null,Fl=se({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),qn="http://www.w3.org/1998/Math/MathML",_a="http://www.w3.org/2000/svg",Vn="http://www.w3.org/1999/xhtml",Cs=Vn,OS=!1,UO=["application/xhtml+xml","text/html"],fi=null,jO=r.createElement("form"),BS=function(z){return z instanceof RegExp||z instanceof Function},ip=function(z){fi&&fi===z||(z&&"object"===ms(z)||(z={}),z=gs(z),O="ALLOWED_TAGS"in z?se({},z.ALLOWED_TAGS):D,V="ALLOWED_ATTR"in z?se({},z.ALLOWED_ATTR):Y,pr="ADD_URI_SAFE_ATTR"in z?se(gs(Fl),z.ADD_URI_SAFE_ATTR):Fl,Ea="ADD_DATA_URI_TAGS"in z?se(gs(ou),z.ADD_DATA_URI_TAGS):ou,Ne="FORBID_CONTENTS"in z?se({},z.FORBID_CONTENTS):fr,K="FORBID_TAGS"in z?se({},z.FORBID_TAGS):{},at="FORBID_ATTR"in z?se({},z.FORBID_ATTR):{},zn="USE_PROFILES"in z&&z.USE_PROFILES,St=!1!==z.ALLOW_ARIA_ATTR,vt=!1!==z.ALLOW_DATA_ATTR,Dt=z.ALLOW_UNKNOWN_PROTOCOLS||!1,Mt=z.SAFE_FOR_TEMPLATES||!1,gt=z.WHOLE_DOCUMENT||!1,xe=z.RETURN_DOM||!1,Ln=z.RETURN_DOM_FRAGMENT||!1,In=z.RETURN_TRUSTED_TYPE||!1,Pe=z.FORCE_BODY||!1,zo=!1!==z.SANITIZE_DOM,No=!1!==z.KEEP_CONTENT,en=z.IN_PLACE||!1,Bt=z.ALLOWED_URI_REGEXP||Bt,Cs=z.NAMESPACE||Vn,z.CUSTOM_ELEMENT_HANDLING&&BS(z.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(W.tagNameCheck=z.CUSTOM_ELEMENT_HANDLING.tagNameCheck),z.CUSTOM_ELEMENT_HANDLING&&BS(z.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(W.attributeNameCheck=z.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),z.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof z.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(W.allowCustomizedBuiltInElements=z.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),lt=lt=-1===UO.indexOf(z.PARSER_MEDIA_TYPE)?"text/html":z.PARSER_MEDIA_TYPE,Z="application/xhtml+xml"===lt?function(zt){return zt}:id,Mt&&(vt=!1),Ln&&(xe=!0),zn&&(O=se({},Io(_y)),V=[],!0===zn.html&&(se(O,Ey),se(V,Ny)),!0===zn.svg&&(se(O,Ag),se(V,Bg),se(V,cd)),!0===zn.svgFilters&&(se(O,Tg),se(V,Bg),se(V,cd)),!0===zn.mathMl&&(se(O,Og),se(V,Ry),se(V,cd))),z.ADD_TAGS&&(O===D&&(O=gs(O)),se(O,z.ADD_TAGS)),z.ADD_ATTR&&(V===Y&&(V=gs(V)),se(V,z.ADD_ATTR)),z.ADD_URI_SAFE_ATTR&&se(pr,z.ADD_URI_SAFE_ATTR),z.FORBID_CONTENTS&&(Ne===fr&&(Ne=gs(Ne)),se(Ne,z.FORBID_CONTENTS)),No&&(O["#text"]=!0),gt&&se(O,["html","head","body"]),O.table&&(se(O,["tbody"]),delete K.tbody),Un&&Un(z),fi=z)},DS=se({},["mi","mo","mn","ms","mtext"]),PS=se({},["foreignobject","desc","title","annotation-xml"]),zO=se({},["title","style","font","a","script"]),ru=se({},Ag);se(ru,Tg),se(ru,HN);var lp=se({},Og);se(lp,$N);var qr=function(z){vl(n.removed,{element:z});try{z.parentNode.removeChild(z)}catch{try{z.outerHTML=N}catch{z.remove()}}},au=function(z,zt){try{vl(n.removed,{attribute:zt.getAttributeNode(z),from:zt})}catch{vl(n.removed,{attribute:null,from:zt})}if(zt.removeAttribute(z),"is"===z&&!V[z])if(xe||Ln)try{qr(zt)}catch{}else try{zt.setAttribute(z,"")}catch{}},MS=function(z){var zt,Tt;if(Pe)z="<remove></remove>"+z;else{var fe=UN(z,/^[\r\n\t ]+/);Tt=fe&&fe[0]}"application/xhtml+xml"===lt&&(z='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+z+"</body></html>");var Ie=E?E.createHTML(z):z;if(Cs===Vn)try{zt=(new p).parseFromString(Ie,lt)}catch{}if(!zt||!zt.documentElement){zt=I.createDocument(Cs,"template",null);try{zt.documentElement.innerHTML=OS?"":Ie}catch{}}var mn=zt.body||zt.documentElement;return z&&Tt&&mn.insertBefore(r.createTextNode(Tt),mn.childNodes[0]||null),Cs===Vn?T.call(zt,gt?"html":"body")[0]:gt?zt.documentElement:mn},LS=function(z){return q.call(z.ownerDocument||z,z,c.SHOW_ELEMENT|c.SHOW_COMMENT|c.SHOW_TEXT,null,!1)},Ul=function(z){return"object"===ms(i)?z instanceof i:z&&"object"===ms(z)&&"number"==typeof z.nodeType&&"string"==typeof z.nodeName},Vr=function(z,zt,Tt){j[z]&&IN(j[z],function(fe){fe.call(n,zt,Tt,fi)})},IS=function(z){var zt,Tt;if(Vr("beforeSanitizeElements",z,null),(Tt=z)instanceof m&&("string"!=typeof Tt.nodeName||"string"!=typeof Tt.textContent||"function"!=typeof Tt.removeChild||!(Tt.attributes instanceof u)||"function"!=typeof Tt.removeAttribute||"function"!=typeof Tt.setAttribute||"string"!=typeof Tt.namespaceURI||"function"!=typeof Tt.insertBefore)||jn(/[\u0080-\uFFFF]/,z.nodeName))return qr(z),!0;var fe=Z(z.nodeName);if(Vr("uponSanitizeElement",z,{tagName:fe,allowedTags:O}),z.hasChildNodes()&&!Ul(z.firstElementChild)&&(!Ul(z.content)||!Ul(z.content.firstElementChild))&&jn(/<[/\w]/g,z.innerHTML)&&jn(/<[/\w]/g,z.textContent)||"select"===fe&&jn(/<template/i,z.innerHTML))return qr(z),!0;if(!O[fe]||K[fe]){if(!K[fe]&&US(fe)&&(W.tagNameCheck instanceof RegExp&&jn(W.tagNameCheck,fe)||W.tagNameCheck instanceof Function&&W.tagNameCheck(fe)))return!1;if(No&&!Ne[fe]){var Ie=C(z)||z.parentNode,mn=y(z)||z.childNodes;if(mn&&Ie)for(var Qe=mn.length-1;Qe>=0;--Qe)Ie.insertBefore(f(mn[Qe],!0),b(z))}return qr(z),!0}return z instanceof l&&!function(Ho){var Je=C(Ho);Je&&Je.tagName||(Je={namespaceURI:Vn,tagName:"template"});var Ze=id(Ho.tagName),ws=id(Je.tagName);return Ho.namespaceURI===_a?Je.namespaceURI===Vn?"svg"===Ze:Je.namespaceURI===qn?"svg"===Ze&&("annotation-xml"===ws||DS[ws]):!!ru[Ze]:Ho.namespaceURI===qn?Je.namespaceURI===Vn?"math"===Ze:Je.namespaceURI===_a?"math"===Ze&&PS[ws]:!!lp[Ze]:Ho.namespaceURI===Vn&&!(Je.namespaceURI===_a&&!PS[ws])&&!(Je.namespaceURI===qn&&!DS[ws])&&!lp[Ze]&&(zO[Ze]||!ru[Ze])}(z)?(qr(z),!0):"noscript"!==fe&&"noembed"!==fe||!jn(/<\/no(script|embed)/i,z.innerHTML)?(Mt&&3===z.nodeType&&(zt=xa(zt=z.textContent,_t," "),zt=xa(zt,mt," "),z.textContent!==zt&&(vl(n.removed,{element:z.cloneNode()}),z.textContent=zt)),Vr("afterSanitizeElements",z,null),!1):(qr(z),!0)},FS=function(z,zt,Tt){if(zo&&("id"===zt||"name"===zt)&&(Tt in r||Tt in jO))return!1;if((!vt||at[zt]||!jn(Ut,zt))&&(!St||!jn(Ht,zt)))if(!V[zt]||at[zt]){if(!(US(z)&&(W.tagNameCheck instanceof RegExp&&jn(W.tagNameCheck,z)||W.tagNameCheck instanceof Function&&W.tagNameCheck(z))&&(W.attributeNameCheck instanceof RegExp&&jn(W.attributeNameCheck,zt)||W.attributeNameCheck instanceof Function&&W.attributeNameCheck(zt))||"is"===zt&&W.allowCustomizedBuiltInElements&&(W.tagNameCheck instanceof RegExp&&jn(W.tagNameCheck,Tt)||W.tagNameCheck instanceof Function&&W.tagNameCheck(Tt))))return!1}else if(!pr[zt]&&!jn(Bt,xa(Tt,kt,""))&&("src"!==zt&&"xlink:href"!==zt&&"href"!==zt||"script"===z||0!==jN(Tt,"data:")||!Ea[z])&&(!Dt||jn(ft,xa(Tt,kt,"")))&&Tt)return!1;return!0},US=function(z){return z.indexOf("-")>0},jS=function(z){var zt,Tt,fe,Ie;Vr("beforeSanitizeAttributes",z,null);var mn=z.attributes;if(mn){var Qe={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:V};for(Ie=mn.length;Ie--;){var Ho=zt=mn[Ie],Je=Ho.name,Ze=Ho.namespaceURI;Tt="value"===Je?zt.value:zN(zt.value),fe=Z(Je);var ws=Tt;if(Qe.attrName=fe,Qe.attrValue=Tt,Qe.keepAttr=!0,Qe.forceKeepAttr=void 0,Vr("uponSanitizeAttribute",z,Qe),Tt=Qe.attrValue,!Qe.forceKeepAttr)if(Qe.keepAttr)if(jn(/\/>/i,Tt))au(Je,z);else{Mt&&(Tt=xa(Tt,_t," "),Tt=xa(Tt,mt," "));var $O=Z(z.nodeName);if(FS($O,fe,Tt)){if(Tt!==ws)try{Ze?z.setAttributeNS(Ze,Je,Tt):z.setAttribute(Je,Tt)}catch{au(Je,z)}}else au(Je,z)}else au(Je,z)}Vr("afterSanitizeAttributes",z,null)}},HO=function z(zt){var Tt,fe=LS(zt);for(Vr("beforeSanitizeShadowDOM",zt,null);Tt=fe.nextNode();)Vr("uponSanitizeShadowNode",Tt,null),IS(Tt)||(Tt.content instanceof a&&z(Tt.content),jS(Tt));Vr("afterSanitizeShadowDOM",zt,null)};return n.sanitize=function(z,zt){var Tt,fe,Ie,mn,Qe;if((OS=!z)&&(z="\x3c!--\x3e"),"string"!=typeof z&&!Ul(z)){if("function"!=typeof z.toString)throw Rg("toString is not a function");if("string"!=typeof(z=z.toString()))throw Rg("dirty is not a string, aborting")}if(!n.isSupported){if("object"===ms(e.toStaticHTML)||"function"==typeof e.toStaticHTML){if("string"==typeof z)return e.toStaticHTML(z);if(Ul(z))return e.toStaticHTML(z.outerHTML)}return z}if(Vt||ip(zt),n.removed=[],"string"==typeof z&&(en=!1),en){if(z.nodeName){var Ho=Z(z.nodeName);if(!O[Ho]||K[Ho])throw Rg("root node is forbidden and cannot be sanitized in-place")}}else if(z instanceof i)1===(fe=(Tt=MS("\x3c!----\x3e")).ownerDocument.importNode(z,!0)).nodeType&&"BODY"===fe.nodeName||"HTML"===fe.nodeName?Tt=fe:Tt.appendChild(fe);else{if(!xe&&!Mt&&!gt&&-1===z.indexOf("<"))return E&&In?E.createHTML(z):z;if(!(Tt=MS(z)))return xe?null:In?N:""}Tt&&Pe&&qr(Tt.firstChild);for(var Je=LS(en?z:Tt);Ie=Je.nextNode();)3===Ie.nodeType&&Ie===mn||IS(Ie)||(Ie.content instanceof a&&HO(Ie.content),jS(Ie),mn=Ie);if(mn=null,en)return z;if(xe){if(Ln)for(Qe=H.call(Tt.ownerDocument);Tt.firstChild;)Qe.appendChild(Tt.firstChild);else Qe=Tt;return V.shadowroot&&(Qe=P.call(o,Qe,!0)),Qe}var Ze=gt?Tt.outerHTML:Tt.innerHTML;return gt&&O["!doctype"]&&Tt.ownerDocument&&Tt.ownerDocument.doctype&&Tt.ownerDocument.doctype.name&&jn(QN,Tt.ownerDocument.doctype.name)&&(Ze="<!DOCTYPE "+Tt.ownerDocument.doctype.name+">\n"+Ze),Mt&&(Ze=xa(Ze,_t," "),Ze=xa(Ze,mt," ")),E&&In?E.createHTML(Ze):Ze},n.setConfig=function(z){ip(z),Vt=!0},n.clearConfig=function(){fi=null,Vt=!1},n.isValidAttribute=function(z,zt,Tt){fi||ip({});var fe=Z(z),Ie=Z(zt);return FS(fe,Ie,Tt)},n.addHook=function(z,zt){"function"==typeof zt&&(j[z]=j[z]||[],vl(j[z],zt))},n.removeHook=function(z){if(j[z])return FN(j[z])},n.removeHooks=function(z){j[z]&&(j[z]=[])},n.removeAllHooks=function(){j={}},n}();const Ay=ot.each,tR=ot.trim,eR=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"],nR={ftp:21,http:80,https:443,mailto:25},oR=["img","video"],Ty=(t,e,n)=>{const o=(r=>{try{return decodeURIComponent(r)}catch{return unescape(r)}})(e).replace(/\s/g,"");return!t.allow_script_urls&&(!!/((java|vb)script|mhtml):/i.test(o)||!t.allow_html_data_urls&&(/^data:image\//i.test(o)?(a=n,(st(r=t.allow_svg_data_urls)?!r:!st(a)||!Lt(oR,a))&&/^data:image\/svg\+xml/i.test(o)):/^data:/i.test(o)));var r,a};class Uo{static parseDataUri(e){let n;const o=decodeURIComponent(e).split(","),r=/data:([^;]+)/.exec(o[0]);return r&&(n=r[1]),{type:n,data:o[1]}}static isDomSafe(e,n,o={}){if(o.allow_script_urls)return!0;{const r=da.decode(e).replace(/[\s\u0000-\u001F]+/g,"");return!Ty(o,r,n)}}static getDocumentBaseUrl(e){var n;let o;return o=0!==e.protocol.indexOf("http")&&"file:"!==e.protocol?null!==(n=e.href)&&void 0!==n?n:"":e.protocol+"//"+e.host+e.pathname,/^[^:]+:\/\/\/?[^\/]+\//.test(o)&&(o=o.replace(/[\?#].*$/,"").replace(/[\/\\][^\/]+$/,""),/[\/\\]$/.test(o)||(o+="/")),o}constructor(e,n={}){this.path="",this.directory="",e=tR(e),this.settings=n;const o=n.base_uri,r=this;if(/^([\w\-]+):([^\/]{2})/i.test(e)||/^\s*#/.test(e))return void(r.source=e);const a=0===e.indexOf("//");if(0!==e.indexOf("/")||a||(e=(o&&o.protocol||"http")+"://mce_host"+e),!/^[\w\-]*:?\/\//.test(e)){const i=o?o.path:new Uo(document.location.href).directory;if(""===o?.protocol)e="//mce_host"+r.toAbsPath(i,e);else{const l=/([^#?]*)([#?]?.*)/.exec(e);l&&(e=(o&&o.protocol||"http")+"://mce_host"+r.toAbsPath(i,l[1])+l[2])}}e=e.replace(/@@/g,"(mce_at)");const s=/^(?:(?![^:@]+:[^:@\/]*@)([^:\/?#.]+):)?(?:\/\/)?((?:(([^:@\/]*):?([^:@\/]*))?@)?(\[[a-zA-Z0-9:.%]+\]|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/.exec(e);s&&Ay(eR,(i,l)=>{let c=s[l];c&&(c=c.replace(/\(mce_at\)/g,"@@")),r[i]=c}),o&&(r.protocol||(r.protocol=o.protocol),r.userInfo||(r.userInfo=o.userInfo),r.port||"mce_host"!==r.host||(r.port=o.port),r.host&&"mce_host"!==r.host||(r.host=o.host),r.source=""),a&&(r.protocol="")}setPath(e){const n=/^(.*?)\/?(\w+)?$/.exec(e);n&&(this.path=n[0],this.directory=n[1],this.file=n[2]),this.source="",this.getURI()}toRelative(e){if("./"===e)return e;const n=new Uo(e,{base_uri:this});if("mce_host"!==n.host&&this.host!==n.host&&n.host||this.port!==n.port||this.protocol!==n.protocol&&""!==n.protocol)return n.getURI();const o=this.getURI(),r=n.getURI();if(o===r||"/"===o.charAt(o.length-1)&&o.substr(0,o.length-1)===r)return o;let a=this.toRelPath(this.path,n.path);return n.query&&(a+="?"+n.query),n.anchor&&(a+="#"+n.anchor),a}toAbsolute(e,n){const o=new Uo(e,{base_uri:this});return o.getURI(n&&this.isSameOrigin(o))}isSameOrigin(e){if(this.host==e.host&&this.protocol==e.protocol){if(this.port==e.port)return!0;const n=this.protocol?nR[this.protocol]:null;if(n&&(this.port||n)==(e.port||n))return!0}return!1}toRelPath(e,n){let o,r,a=0,s="";const i=e.substring(0,e.lastIndexOf("/")).split("/"),l=n.split("/");if(i.length>=l.length)for(o=0,r=i.length;o<r;o++)if(o>=l.length||i[o]!==l[o]){a=o+1;break}if(i.length<l.length)for(o=0,r=l.length;o<r;o++)if(o>=i.length||i[o]!==l[o]){a=o+1;break}if(1===a)return n;for(o=0,r=i.length-(a-1);o<r;o++)s+="../";for(o=a-1,r=l.length;o<r;o++)s+=o!==a-1?"/"+l[o]:l[o];return s}toAbsPath(e,n){let o=0;const r=/\/$/.test(n)?"/":"",a=e.split("/"),s=n.split("/"),i=[];Ay(a,u=>{u&&i.push(u)});const l=[];for(let u=s.length-1;u>=0;u--)0!==s[u].length&&"."!==s[u]&&(".."!==s[u]?o>0?o--:l.push(s[u]):o++);const c=i.length-o;let d;return d=c<=0?Ao(l).join("/"):i.slice(0,c).join("/")+"/"+Ao(l).join("/"),0!==d.indexOf("/")&&(d="/"+d),r&&d.lastIndexOf("/")!==d.length-1&&(d+=r),d}getURI(e=!1){let n;return this.source&&!e||(n="",e||(n+=this.protocol?this.protocol+"://":"//",this.userInfo&&(n+=this.userInfo+"@"),this.host&&(n+=this.host),this.port&&(n+=":"+this.port)),this.path&&(n+=this.path),this.query&&(n+="?"+this.query),this.anchor&&(n+="#"+this.anchor),this.source=n),this.source}}const rR=ot.makeMap("src,href,data,background,action,formaction,poster,xlink:href"),Dg="data-mce-type";let Oy=0;const By=(t,e,n,o)=>{var r,a,s,i;const l=e.validate,c=n.getSpecialElements();8===t.nodeType&&!e.allow_conditional_comments&&/^\[if/i.test(null!==(r=t.nodeValue)&&void 0!==r?r:"")&&(t.nodeValue=" "+t.nodeValue);const d=null!==(a=o?.tagName)&&void 0!==a?a:t.nodeName.toLowerCase();if(1!==t.nodeType||"body"===d)return;const u=A(t),m=As(u,Dg),p=An(u,"data-mce-bogus");if(!m&&Nt(p))return void("all"===p?w(u):_(u));const g=n.getElementRule(d);if(!l||g){if(st(o)&&(o.allowedTags[d]=!0),l&&g&&!m){if(J(null!==(s=g.attributesForced)&&void 0!==s?s:[],h=>{Me(u,h.name,"{$uid}"===h.value?"mce_"+Oy++:h.value)}),J(null!==(i=g.attributesDefault)&&void 0!==i?i:[],h=>{As(u,h.name)||Me(u,h.name,"{$uid}"===h.value?"mce_"+Oy++:h.value)}),g.attributesRequired&&!ie(g.attributesRequired,h=>As(u,h))||g.removeEmptyAttrs&&(h=>{const f=h.dom.attributes;return null==f||0===f.length})(u))return void _(u);g.outputName&&g.outputName!==d&&Qp(u,g.outputName)}}else It(c,d)?w(u):_(u)},Dy=(t,e,n,o,r)=>!(o in rR&&Ty(t,r,n))&&(!t.validate||e.isValid(n,o)||ht(o,"data-")||ht(o,"aria-")),Py=(t,e)=>t.hasAttribute(Dg)&&("id"===e||"class"===e||"style"===e),My=(t,e)=>t in e.getBoolAttrs(),aR=(t,e,n)=>{const{attributes:o}=t;for(let r=o.length-1;r>=0;r--){const a=o[r],s=a.name,i=a.value;Dy(e,n,t.tagName.toLowerCase(),s,i)||Py(t,s)?My(s,n)&&t.setAttribute(s,s):t.removeAttribute(s)}},Ly=ot.makeMap,Iy=ot.extend,Fy=(t,e,n)=>{const o=t.name,r=o in n&&"title"!==o&&"textarea"!==o,a=e.childNodes;for(let s=0,i=a.length;s<i;s++){const l=a[s],c=new Pn(l.nodeName.toLowerCase(),l.nodeType);if(it(l)){const d=l.attributes;for(let u=0,m=d.length;u<m;u++){const p=d[u];c.attr(p.name,p.value)}}else tt(l)?(c.value=l.data,r&&(c.raw=!0)):(sa(l)||zS(l)||HS(l))&&(c.value=l.data);Fy(c,l,n),t.append(c)}},ti=(t={},e=ua())=>{const n=py(),o=py(),r={validate:!0,root_name:"body",sanitize:!0,...t},a=new DOMParser,s=((h,f)=>{if(h.sanitize){const b=((t,e)=>{const n=ZN();return n.addHook("uponSanitizeElement",(o,r)=>{By(o,t,e,r)}),n.addHook("uponSanitizeAttribute",(o,r)=>{const a=o.tagName.toLowerCase(),{attrName:s,attrValue:i}=r;r.keepAttr=Dy(t,e,a,s,i),r.keepAttr?(r.allowedAttributes[s]=!0,My(s,e)&&(r.attrValue=s),t.allow_svg_data_urls&&ht(i,"data:image/svg+xml")&&(r.forceKeepAttr=!0)):Py(o,s)&&(r.forceKeepAttr=!0)}),n})(h,f);return(y,C)=>{b.sanitize(y,((k,E)=>{const N={IN_PLACE:!0,ALLOW_UNKNOWN_PROTOCOLS:!0,ALLOWED_TAGS:["#comment","#cdata-section","body"],ALLOWED_ATTR:[]};return N.PARSER_MEDIA_TYPE=E,k.allow_script_urls?N.ALLOWED_URI_REGEXP=/.*/:k.allow_html_data_urls&&(N.ALLOWED_URI_REGEXP=/^(?!(\w+script|mhtml):)/i),N})(h,C)),b.removed=[]}}return(b,y)=>{const C=document.createNodeIterator(b,NodeFilter.SHOW_ELEMENT|NodeFilter.SHOW_COMMENT|NodeFilter.SHOW_TEXT);let k;for(;k=C.nextNode();)By(k,h,f),it(k)&&aR(k,h,f)}})(r,e),l=n.getFilters,u=o.getFilters,p=(h,f)=>{const b=Nt(f.attr(Dg)),y=1===f.type&&!It(h,f.name)&&!Ep(e,f);return 3===f.type||y&&!b},g={schema:e,addAttributeFilter:o.addFilter,getAttributeFilters:u,removeAttributeFilter:o.removeFilter,addNodeFilter:n.addFilter,getNodeFilters:l,removeNodeFilter:n.removeFilter,parse:(h,f={})=>{var b;const y=r.validate,C=null!==(b=f.context)&&void 0!==b?b:r.root_name,k=((B,j,lt="html")=>{const Z="xhtml"===lt?"application/xhtml+xml":"text/html",_t=It(e.getSpecialElements(),j.toLowerCase()),mt=_t?`<${j}>${B}</${j}>`:B,Ht=a.parseFromString("xhtml"===lt?`<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>${mt}</body></html>`:`<body>${mt}</body>`,Z).body;return s(Ht,Z),_t?Ht.firstChild:Ht})(h,C,f.format);xu(e,k);const E=new Pn(C,11);Fy(E,k,e.getSpecialElements()),k.innerHTML="";const[N,R]=((B,j,lt,Z)=>{const _t=lt.validate,mt=j.getNonEmptyElements(),Ut=j.getWhitespaceElements(),Ht=Iy(Ly("script,style,head,html,body,title,meta,param"),j.getBlockElements()),ft=Ip(j),kt=/[ \t\r\n]+/g,Bt=/^[ \t\r\n]+/,O=/[ \t\r\n]+$/,D=W=>{let K=W.parent;for(;st(K);){if(K.name in Ut)return!0;K=K.parent}return!1},V=W=>W.name in Ht||Ep(j,W),Y=(W,K)=>!st(K?W.prev:W.next)&&!pe(W.parent)&&V(W.parent)&&(W.parent!==B||!0===Z.isRootContent);return[W=>{var K,St,vt;if(3===W.type&&!D(W)){let at=null!==(K=W.value)&&void 0!==K?K:"";at=at.replace(kt," "),vt=V,(st(St=W.prev)&&(vt(St)||"br"===St.name)||Y(W,!0))&&(at=at.replace(Bt,"")),0===at.length?W.remove():W.value=at}},W=>{var K,vt,Dt;if(1===W.type){const at=j.getElementRule(W.name);if(_t&&at){const St=pl(j,mt,Ut,W);at.paddInEmptyBlock&&St&&(vt=>{let Dt=vt;for(;st(Dt);){if(Dt.name in ft)return pl(j,mt,Ut,Dt);Dt=Dt.parent}return!1})(W)?rg(Z,V,W):at.removeEmpty&&St?V(W)?W.remove():W.unwrap():at.paddEmpty&&(St||k0(vt=W,"#text")&&(null===(Dt=vt?.firstChild)||void 0===Dt?void 0:Dt.value)===Ge)&&rg(Z,V,W)}}else if(3===W.type&&!D(W)){let at=null!==(K=W.value)&&void 0!==K?K:"";(W.next&&V(W.next)||Y(W,!1))&&(at=at.replace(O,"")),0===at.length?W.remove():W.value=at}}]})(E,e,r,f),I=[],H={nodes:{},attributes:{}},T=B=>w0(l(),u(),B,H);if(((B,j,lt)=>{const Z=[];for(let _t=B,mt=_t;_t;mt=_t,_t=_t.walk()){const Ut=_t;J(j,Ht=>Ht(Ut)),pe(Ut.parent)&&Ut!==B?_t=mt:Z.push(Ut)}for(let _t=Z.length-1;_t>=0;_t--){const mt=Z[_t];J(lt,Ut=>Ut(mt))}})(E,[N,T],[R,y?B=>{return lt=I,void(E0(e,j=B)&&lt.push(j));var j,lt}:jt]),I.reverse(),y&&I.length>0)if(f.context){const{pass:B,fail:j}=Ve(I,lt=>lt.parent===E);sg(j,e,E,T),f.invalid=B.length>0}else sg(I,e,E,T);const P=((B,j)=>{var lt;const Z=null!==(lt=j.forced_root_block)&&void 0!==lt?lt:B.forced_root_block;return!1===Z?"":!0===Z?"p":Z})(r,f);return P&&("body"===E.name||f.isRootContent)&&((B,j)=>{const lt=Iy(Ly("script,style,head,html,body,title,meta,param"),e.getBlockElements()),Z=/^[ \t\r\n]+/,_t=/[ \t\r\n]+$/;let mt=B.firstChild,Ut=null;const Ht=ft=>{var kt,Bt;ft&&(mt=ft.firstChild,mt&&3===mt.type&&(mt.value=null===(kt=mt.value)||void 0===kt?void 0:kt.replace(Z,"")),mt=ft.lastChild,mt&&3===mt.type&&(mt.value=null===(Bt=mt.value)||void 0===Bt?void 0:Bt.replace(_t,"")))};if(e.isValidChild(B.name,j.toLowerCase())){for(;mt;){const ft=mt.next;p(lt,mt)?(Ut||(Ut=new Pn(j,1),Ut.attr(r.forced_root_block_attrs),B.insert(Ut,mt)),Ut.append(mt)):(Ht(Ut),Ut=null),mt=ft}Ht(Ut)}})(E,P),f.invalid||x0(H,f),E}};return((t,e)=>{const n=t.schema;e.remove_trailing_brs&&hy(t,n),t.addAttributeFilter("href",r=>{let a=r.length;const s=i=>{const l=i?ot.trim(i):"";return/\b(noopener)\b/g.test(l)?l:l.split(" ").filter(d=>d.length>0).concat(["noopener"]).sort().join(" ")};if(!e.allow_unsafe_link_target)for(;a--;){const i=r[a];"a"===i.name&&"_blank"===i.attr("target")&&i.attr("rel",s(i.attr("rel")))}}),e.allow_html_in_named_anchor||t.addAttributeFilter("id,name",r=>{let a,s,i,l,c=r.length;for(;c--;)if(l=r[c],"a"===l.name&&l.firstChild&&!l.attr("href"))for(i=l.parent,a=l.lastChild;a&&i;)s=a.prev,i.insert(a,l),a=s}),e.fix_list_elements&&t.addNodeFilter("ul,ol",r=>{let a,s,i=r.length;for(;i--;)if(a=r[i],s=a.parent,s&&("ul"===s.name||"ol"===s.name))if(a.prev&&"li"===a.prev.name)a.prev.append(a);else{const l=new Pn("li",1);l.attr("style","list-style-type: none"),a.wrap(l)}});const o=n.getValidClasses();e.validate&&o&&t.addAttributeFilter("class",r=>{var a;let s=r.length;for(;s--;){const i=r[s],l=null!==(a=i.attr("class"))&&void 0!==a?a:"",c=ot.explode(l," ");let d="";for(let u=0;u<c.length;u++){const m=c[u];let p=!1,g=o["*"];g&&g[m]&&(p=!0),g=o[i.name],!p&&g&&g[m]&&(p=!0),p&&(d&&(d+=" "),d+=m)}d.length||(d=null),i.attr("class",d)}}),((r,a)=>{const{blob_cache:s}=a;if(s){const i=l=>{const c=l.attr("src");var d;(d=l).attr("src")===Jt.transparentSrc||st(d.attr("data-mce-placeholder"))||(d=>st(d.attr("data-mce-bogus")))(l)||pe(c)||wy(s,c,!0).each(d=>{l.attr("src",d.blobUri())})};r.addAttributeFilter("src",l=>J(l,i))}})(t,e)})(g,r),(f=r).inline_styles&&((t,e,n)=>{var o;const r=Tu();var a,s,i;e.convert_fonts_to_spans&&(a=t,s=r,i=ot.explode(null!==(o=e.font_size_legacy_values)&&void 0!==o?o:""),a.addNodeFilter("font",l=>{J(l,c=>{const d=s.parse(c.attr("style")),u=c.attr("color"),m=c.attr("face"),p=c.attr("size");var g;u&&(d.color=u),m&&(d["font-family"]=m),p&&we(p).each(g=>{d["font-size"]=i[g-1]}),c.name="span",c.attr("style",s.serialize(d)),g=c,J(["color","face","size"],f=>{g.attr(f,null)})})})),((a,s,i)=>{a.addNodeFilter("strike",l=>{const c="html4"!==s.type;J(l,d=>{if(c)d.name="s";else{const u=i.parse(d.attr("style"));u["text-decoration"]="line-through",d.name="span",d.attr("style",i.serialize(u))}})})})(t,n,r)})(g,f,e),g;var f},Uy=(t,e,n)=>{const o=nd(a=t)?ba({validate:!1}).serialize(a):a,r=e(o);var a;if(r.isDefaultPrevented())return r;if(nd(t)){if(r.content!==o){const a=ti({validate:!1,forced_root_block:!1,sanitize:n}).parse(r.content,{context:t.name});return{...r,content:a}}return{...r,content:t}}return r},jy=(t,e)=>{if(e.no_events)return On.value(e);{const n=t.dispatch("BeforeGetContent",e);return n.isDefaultPrevented()?On.error(qb(t,{content:"",...n}).content):On.value(n)}},zy=(t,e,n)=>n.no_events?e:Uy(e,r=>qb(t,{...n,content:r}),Xu(t)).content,Pg=(t,e)=>{if(e.no_events)return On.value(e);{const n=Uy(e.content,o=>{return r=t,a={...e,content:o},r.dispatch("BeforeSetContent",a);var r,a},Xu(t));return n.isDefaultPrevented()?($b(t,n),On.error(void 0)):On.value(n)}},Mg=(t,e,n)=>{n.no_events||$b(t,{...n,content:e})},Lg=(t,e,n)=>({element:t,width:e,rows:n}),Hy=(t,e)=>({element:t,cells:e}),iR=(t,e)=>({x:t,y:e}),$y=(t,e)=>Zn(t,e).bind(we).getOr(1),lR=(t,e,n)=>{const o=t.rows;return!!(o[n]?o[n].cells:[])[e]},qy=t=>Re(t,(e,n)=>n.cells.length>e?n.cells.length:e,0),Vy=(t,e)=>{const n=t.rows;for(let o=0;o<n.length;o++){const r=n[o].cells;for(let a=0;a<r.length;a++)if(re(r[a],e))return S.some(iR(a,o))}return S.none()},Wy=(t,e,n,o,r)=>{const a=[],s=t.rows;for(let i=n;i<=r;i++){const l=s[i].cells,c=e<o?l.slice(e,o+1):l.slice(o,e+1);a.push(Hy(s[i].element,c))}return a},cR=t=>((e,n)=>{const o=Xa(e.element),r=Be("tbody");return v(r,n),Se(o,r),o})(t,te(t.rows,n=>{const o=te(n.cells,a=>{const s=Xp(a);return Le(s,"colspan"),Le(s,"rowspan"),s}),r=Xa(n.element);return v(r,o),r})),Ky=()=>xm([]),dd=(t,e)=>e>=0&&e<t.length&&Ks(t.charAt(e)),Gy=t=>Fr(t.innerText),Yy=t=>it(t)?t.outerHTML:tt(t)?da.encodeRaw(t.data,!1):sa(t)?"\x3c!--"+t.data+"--\x3e":"",fR=fo(()=>document.implementation.createHTMLDocument("undo")),ud=t=>{const e=(n=t.getBody(),Ot(te(Ae(n.childNodes),Yy),a=>a.length>0));var n;const o=gn(e,a=>{const s=Nv(t.serializer,a);return s.length>0?[s]:[]}),r=o.join("");return-1!==r.indexOf("</iframe>")?(a=>({type:"fragmented",fragments:a,content:"",bookmark:null,beforeBookmark:null}))(o):(a=>({type:"complete",fragments:null,content:a,bookmark:null,beforeBookmark:null}))(r)},Ig=(t,e,n)=>{const o=n?e.beforeBookmark:e.bookmark;"fragmented"===e.type?((t,e)=>{((n,o)=>{let r=0;J(n,a=>{var s,i;0===a[0]?r++:1===a[0]?(((s,i,l)=>{const c=(d=>{let u;const m=document.createElement("div"),p=document.createDocumentFragment();for(d&&(m.innerHTML=d);u=m.firstChild;)p.appendChild(u);return p})(i);s.hasChildNodes()&&l<s.childNodes.length?s.insertBefore(c,s.childNodes[l]):s.appendChild(c)})(o,a[1],r),r++):2===a[0]&&(i=r,(s=o).hasChildNodes()&&i<s.childNodes.length&&s.removeChild(s.childNodes[i]))})})(((n,o)=>{const r=n.length+o.length+2,a=new Array(r),s=new Array(r),i=(u,m,p,g,h)=>{const f=c(u,m,p,g);if(null===f||f.start===m&&f.diag===m-g||f.end===u&&f.diag===u-p){let b=u,y=p;for(;b<m||y<g;)b<m&&y<g&&n[b]===o[y]?(h.push([0,n[b]]),++b,++y):m-u>g-p?(h.push([2,n[b]]),++b):(h.push([1,o[y]]),++y)}else{i(u,f.start,p,f.start-f.diag,h);for(let b=f.start;b<f.end;++b)h.push([0,n[b]]);i(f.end,m,f.end-f.diag,g,h)}},l=(u,m,p,g)=>{let h=u;for(;h-m<g&&h<p&&n[h]===o[h-m];)++h;return((f,b,y)=>({start:f,end:b,diag:y}))(u,h,m)},c=(u,m,p,g)=>{const h=m-u,f=g-p;if(0===h||0===f)return null;const b=h-f,y=f+h,C=(y%2==0?y:y+1)/2;let k,E,N,R,I;for(a[1+C]=u,s[1+C]=m+1,k=0;k<=C;++k){for(E=-k;E<=k;E+=2){for(N=E+C,a[N]=E===-k||E!==k&&a[N-1]<a[N+1]?a[N+1]:a[N-1]+1,R=a[N],I=R-u+p-E;R<m&&I<g&&n[R]===o[I];)a[N]=++R,++I;if(b%2!=0&&b-k<=E&&E<=b+k&&s[N-b]<=a[N])return l(s[N-b],E+u-p,m,g)}for(E=b-k;E<=b+k;E+=2){for(N=E+C-b,s[N]=E===b-k||E!==b+k&&s[N+1]<=s[N-1]?s[N+1]-1:s[N-1],R=s[N]-1,I=R-u+p-E;R>=u&&I>=p&&n[R]===o[I];)s[N]=R--,I--;if(b%2==0&&-k<=E&&E<=k&&s[N]<=a[N+b])return l(s[N],E+u-p,m,g)}}return null},d=[];return i(0,n.length,0,o.length,d),d})(te(Ae(e.childNodes),Yy),t),e)})(e.fragments,t.getBody()):t.setContent(e.content,{format:"raw",no_selection:!st(o)||!mb(o)||!o.isFakeCaret}),o&&(t.selection.moveToBookmark(o),t.selection.scrollIntoView())},Fg=t=>"fragmented"===t.type?t.fragments.join(""):t.content,Xy=t=>{const e=Be("body",fR());return X(e,Fg(t)),J(Rt(e,"*[data-mce-bogus]"),_),F(e)},Ug=(t,e)=>{return!(!t||!e)&&(o=e,!(Fg(t)!==Fg(o))||((n,o)=>Xy(n)===Xy(o))(t,e));var o},jg=t=>0===t.get(),md=(t,e,n)=>{jg(n)&&(t.typing=e)},Qy=(t,e)=>{t.typing&&(md(t,!1,e),t.add())},Jy=t=>({init:{bindEvents:jt},undoManager:{beforeChange:(e,n)=>{return o=t,a=n,void(jg(e)&&a.set(qu(o.selection)));var o,a},add:(e,n,o,r,a,s)=>((i,l,c,d,u,m,p)=>{const g=ud(i),h=ot.extend(m||{},g);if(!jg(d)||i.removed)return null;const f=l.data[c.get()];if(i.dispatch("BeforeAddUndo",{level:h,lastLevel:f,originalEvent:p}).isDefaultPrevented()||f&&Ug(f,h))return null;l.data[c.get()]&&u.get().each(C=>{l.data[c.get()].beforeBookmark=C});const b=yE(i);if(b&&l.data.length>b){for(let C=0;C<l.data.length-1;C++)l.data[C]=l.data[C+1];l.data.length--,c.set(l.data.length)}h.bookmark=qu(i.selection),c.get()<l.data.length-1&&(l.data.length=c.get()+1),l.data.push(h),c.set(l.data.length-1);const y={level:h,lastLevel:f,originalEvent:p};return c.get()>0?(i.setDirty(!0),i.dispatch("AddUndo",y),i.dispatch("change",y)):i.dispatch("AddUndo",y),h})(t,e,n,o,r,a,s),undo:(e,n,o)=>((r,a,s,i)=>{let l;return a.typing&&(a.add(),a.typing=!1,md(a,!1,s)),i.get()>0&&(i.set(i.get()-1),l=a.data[i.get()],Ig(r,l,!0),r.setDirty(!0),r.dispatch("Undo",{level:l})),l})(t,e,n,o),redo:(e,n)=>((o,r,a)=>{let s;return r.get()<a.length-1&&(r.set(r.get()+1),s=a[r.get()],Ig(o,s,!1),o.setDirty(!0),o.dispatch("Redo",{level:s})),s})(t,e,n),clear:(e,n)=>{return o=t,a=n,(r=e).data=[],a.set(0),r.typing=!1,void o.dispatch("ClearUndos");var o,r,a},reset:e=>{return(n=e).clear(),void n.add();var n},hasUndo:(e,n)=>{return o=t,r=e,n.get()>0||r.typing&&r.data[0]&&!Ug(ud(o),r.data[0]);var o,r},hasRedo:(e,n)=>{return o=e,n.get()<o.data.length-1&&!o.typing;var o},transact:(e,n,o)=>{return s=o,Qy(r=e,n),r.beforeChange(),r.ignore(s),r.add();var r,s},ignore:(e,n)=>((o,r)=>{try{o.set(o.get()+1),r()}finally{o.set(o.get()-1)}})(e,n),extra:(e,n,o,r)=>((a,s,i,l,c)=>{if(s.transact(l)){const d=s.data[i.get()].bookmark,u=s.data[i.get()-1];Ig(a,u,!0),s.transact(c)&&(s.data[i.get()-1].beforeBookmark=d)}})(t,e,n,o,r)},formatter:{match:(e,n,o,r)=>fg(t,e,n,o,r),matchAll:(e,n)=>((o,r,a)=>{const s=[],i={},l=o.selection.getStart();return o.dom.getParent(l,c=>{for(let d=0;d<r.length;d++){const u=r[d];!i[u]&&lr(o,c,u,a)&&(i[u]=!0,s.push(u))}},o.dom.getRoot()),s})(t,e,n),matchNode:(e,n,o,r)=>lr(t,e,n,o,r),canApply:e=>((n,o)=>{const r=n.formatter.get(o),a=n.dom;if(r&&n.selection.isEditable()){const s=n.selection.getStart(),i=zc(a,s);for(let l=r.length-1;l>=0;l--){const c=r[l];if(!ao(c))return!0;for(let d=i.length-1;d>=0;d--)if(a.is(i[d],c.selector))return!0}}return!1})(t,e),closest:e=>((n,o)=>{const r=a=>re(a,A(n.getBody()));return S.from(n.selection.getStart(!0)).bind(a=>ug(A(a),s=>Ko(o,i=>{return lr(n,s.dom,c=i)?S.some(c):S.none();var c}),r)).getOrNull()})(t,e),apply:(e,n,o)=>ly(t,e,n,o),remove:(e,n,o,r)=>ay(t,e,n,o,r),toggle:(e,n,o)=>((r,a,s,i)=>{const l=r.formatter.get(a);l&&(!fg(r,a,s,i)||"toggle"in l[0]&&!l[0].toggle?ly(r,a,s,i):ay(r,a,s,i))})(t,e,n,o),formatChanged:(e,n,o,r,a)=>{return((m,p,g,h,f,b)=>{const y=p.get();J(g.split(","),C=>{const k=ce(y,C).getOrThunk(()=>{const N={withSimilar:{state:Ye(!1),similar:!0,callbacks:[]},withoutSimilar:{state:Ye(!1),similar:!1,callbacks:[]},withVars:[]};return y[C]=N,N}),E=()=>{const N=my(m);return uy(m,N,C,f,b).isSome()};if(dt(b)){const N=f?k.withSimilar:k.withoutSimilar;N.callbacks.push(h),1===N.callbacks.length&&N.state.set(E())}else k.withVars.push({state:Ye(E()),similar:f,vars:b,callback:h})}),p.set(y)})(t,i=e,l=n,c=o,r,a),{unbind:()=>((m,p,g)=>{const h=m.get();J(p.split(","),f=>ce(h,f).each(b=>{h[f]={withSimilar:{...b.withSimilar,callbacks:Ot(b.withSimilar.callbacks,y=>y!==g)},withoutSimilar:{...b.withoutSimilar,callbacks:Ot(b.withoutSimilar.callbacks,y=>y!==g)},withVars:Ot(b.withVars,y=>y.callback!==g)}})),m.set(h)})(i,l,c)};var i,l,c}},editor:{getContent:e=>{return o=e,S.from((n=t).getBody()).fold(ut("tree"===o.format?new Pn("body",11):""),r=>((t,e,n)=>{let o;return o="raw"===e.format?ot.trim(G_(t.serializer,n.innerHTML)):"text"===e.format?((r,a)=>{const s=r.getDoc(),i=Co(A(r.getBody())),l=Be("div",s);Me(l,"data-mce-bogus","all"),Ps(l,{position:"fixed",left:"-9999999px",top:"0"}),X(l,a.innerHTML),Rv(l),Av(l);const c=Ia(u=i)?u:A(yo(u).dom.body);var u;Se(c,l);const d=Fr(l.dom.innerText);return w(l),d})(t,n):"tree"===e.format?t.serializer.serialize(n,e):((r,a)=>{const s=Bn(r),i=new RegExp(`^(<${s}[^>]*>(&nbsp;|&#160;|\\s|\xa0|<br \\/>|)<\\/${s}>[\r\n]*|<br \\/>[\r\n]*)$`);return a.replace(i,"")})(t,t.serializer.serialize(n,e)),"text"!==e.format&&!gc(A(n))&&Nt(o)?ot.trim(o):o})(n,o,r));var n,o},setContent:(e,n)=>{return r=e,a=n,S.from((o=t).getBody()).map(s=>nd(r)?((i,l,c,d)=>{S0(i.parser.getNodeFilters(),i.parser.getAttributeFilters(),c);const u=ba({validate:!1},i.schema).serialize(c),m=gc(A(l))?u:ot.trim(u);return dg(i,m,d.no_selection),{content:c,html:m}})(o,s,r,a):((i,l,c,d)=>{if(0===c.length||/^\s+$/.test(c)){const u='<br data-mce-bogus="1">';"TABLE"===l.nodeName?c="<tr><td>"+u+"</td></tr>":/^(UL|OL)$/.test(l.nodeName)&&(c="<li>"+u+"</li>");const m=Bn(i);return i.schema.isValidChild(l.nodeName.toLowerCase(),m.toLowerCase())?(c=u,c=i.dom.createHTML(m,Yi(i),c)):c||(c=u),dg(i,c,d.no_selection),{content:c,html:c}}{"raw"!==d.format&&(c=ba({validate:!1},i.schema).serialize(i.parser.parse(c,{isRootContent:!0,insert:!0})));const u=gc(A(l))?c:ot.trim(c);return dg(i,u,d.no_selection),{content:u,html:u}}})(o,s,r,a)).getOr({content:r,html:nd(a.content)?"":a.content});var o,r,a},insertContent:(e,n)=>SN(t,e,n),addVisual:e=>((n,o)=>{const r=n.dom,a=st(o)?o:n.getBody();J(r.select("table,a",a),s=>{switch(s.nodeName){case"TABLE":const i=EE(n),l=r.getAttrib(s,"border");l&&"0"!==l||!n.hasVisual?r.removeClass(s,i):r.addClass(s,i);break;case"A":if(!r.getAttrib(s,"href")){const c=r.getAttrib(s,"name")||s.id,d=zh(n);c&&n.hasVisual?r.addClass(s,d):r.removeClass(s,d)}}}),n.dispatch("VisualAid",{element:o,hasVisual:n.hasVisual})})(t,e)},selection:{getContent:(e,n)=>((o,r,a={})=>{const s=(i=a,l=r,{...i,format:l,get:!0,selection:!0,getInner:!0});var i,l;return jy(o,s).fold(qe,i=>{const l=((c,d)=>{if("text"===d.format)return S.from((u=c).selection.getRng()).map(m=>{var p;const g=S.from(u.dom.getParent(m.commonAncestorContainer,u.dom.isBlock)),h=u.getBody(),f=g.map(N=>N.nodeName).getOr("div").toLowerCase(),b=A(m.cloneContents());Rv(b),Av(b);const y=u.dom.add(h,f,{"data-mce-bogus":"all",style:"overflow: hidden; opacity: 0;"},b.dom),C=Gy(y),k=Fr(null!==(p=y.textContent)&&void 0!==p?p:"");if(u.dom.remove(y),dd(k,0)||dd(k,k.length-1)){const E=g.getOr(h),N=Gy(E),R=N.indexOf(C);return-1===R?C:(dd(N,R-1)?" ":"")+C+(dd(N,R+C.length)?" ":"")}return C}).getOr("");var u;{const u=((m,p)=>{const g=m.selection.getRng(),h=m.dom.create("body"),f=m.selection.getSel(),b=Mm(m,im(f)),y=p.contextual?((t,e)=>{const n=Cb(e,t);return n.length>0?((t,e)=>{return(n=t,o=e[0],Fa(o,"table",Ct(re,n))).bind(n=>{const o=e[0],r=e[e.length-1],a=(s=>{const i=Lg(Xa(s),0,[]);return J(Rt(s,"tr"),(l,c)=>{J(Rt(l,"td,th"),(d,u)=>{((m,p,g,h,f)=>{const b=$y(f,"rowspan"),y=$y(f,"colspan"),C=m.rows;for(let k=g;k<g+b;k++){C[k]||(C[k]=Hy(Xp(h),[]));for(let E=p;E<p+y;E++)C[k].cells[E]=k===g&&E===p?f:Xa(f)}})(i,((m,p,g)=>{for(;lR(m,p,g);)p++;return p})(i,u,c),c,l,d)})}),Lg(i.element,qy(i.rows),i.rows)})(n);return(s=a,i=o,l=r,Vy(s,i).bind(c=>Vy(s,l).map(d=>((u,m,p)=>{const g=m.x,h=m.y,f=p.x,b=p.y,y=h<b?Wy(u,g,h,f,b):Wy(u,g,b,f,h);return Lg(u.element,qy(y),y)})(s,c,d)))).map(s=>xm([cR(s)]));var s,i,l}).getOrThunk(Ky);var n,o})(t,n):(o=t,(r=e).length>0&&r[0].collapsed?Ky():(a=o,((i,l)=>{const c=Re(l,(d,u)=>(Se(u,d),u),i);return l.length>0?xm([c]):c})(A((s=r[0]).cloneContents()),((t,e)=>{const n=A(e.commonAncestorContainer),o=zr(n,t),r=Ot(o,YS),a=(l=e,he(i=o,c=>"li"===ge(c)&&cm(c,l)).fold(ut([]),c=>{return(d=i,he(d,u=>"ul"===ge(u)||"ol"===ge(u))).map(d=>{const u=Be(ge(d)),m=Go(lc(d),(p,g)=>ht(g,"list-style"));return Ps(u,m),[Be("li"),u]}).getOr([]);var d})),s=r.concat(a.length?a:(i=>Pi(i)?Tn(i).filter(hu).fold(ut([]),l=>[i,l]):hu(i)?[i]:[])(n));var i,l;return te(s,Xa)})(a,s))));var o,r,a,s})(A(m.getBody()),b).dom:g.cloneContents();return y&&h.appendChild(y),m.selection.serializer.serialize(h,p)})(c,d);return"tree"===d.format?u:c.selection.isCollapsed()?"":u}})(o,i);return zy(o,l,i)})})(t,e,n)},autocompleter:{addDecoration:e=>((t,e)=>{if(kv(A(t.getBody())).isNone()){const o=Ma('<span data-mce-autocompleter="1" data-mce-bogus="1"></span>',t.getDoc());Se(o,A(e.extractContents())),e.insertNode(o.dom),Tn(o).each(r=>r.dom.normalize()),(n=o,((r,a)=>{const s=i=>{const l=ze(i);for(let c=l.length-1;c>=0;c--){const d=l[c];if(a(d))return S.some(d);const u=s(d);if(u.isSome())return u}return S.none()};return s(r)})(n,xv)).map(r=>{var a;t.selection.setCursorLocation(r.dom,"img"===ge(a=r)?1:gp(a).fold(()=>ze(a).length,s=>s.length))})}var n})(t,e),removeDecoration:()=>{return e=t,n=A(t.getBody()),kv(n).each(o=>{const r=e.selection.getBookmark();_(o),e.selection.moveToBookmark(r)});var e,n}},raw:{getModel:()=>S.none()}}),dr=t=>It(t.plugins,"rtc"),zg=t=>t.rtcInstance?t.rtcInstance:Jy(t),Te=t=>{const e=t.rtcInstance;if(e)return e;throw new Error("Failed to get RTC instance not yet initialized.")},Zy=t=>Te(t).init.bindEvents(),tC=t=>0===t.dom.length?(w(t),S.none()):S.some(t),eC=(t,e,n,o)=>{t.bind(r=>((o?Vm:Jc)(r.dom,o?r.dom.length:0),e.filter(pn).map(a=>((s,i,l,c)=>{const d=s.dom,u=i.dom,m=c?d.length:u.length;c?(Wm(d,u,!1,!c),l.setStart(u,m)):(Wm(u,d,!1,!c),l.setEnd(u,m))})(r,a,n,o)))).orThunk(()=>{return(a=e,s=o,a.filter(i=>il.isBookmarkNode(i.dom)).bind(s?ea:Mr)).or(e).filter(pn).map(a=>((s,i)=>{Tn(s).each(l=>{const c=s.dom;i&&ml(l,$(c,0))?Jc(c,0):!i&&gl(l,$(c,c.length))&&Vm(c,c.length)})})(a,o));var a,s})},nC=(t,e,n)=>{if(It(t,e)){const o=Ot(t[e],r=>r!==n);0===o.length?delete t[e]:t[e]=o}},oC=t=>!(!t||!t.ownerDocument)&&vo(A(t.ownerDocument),A(t)),rC=(t,e,n,o)=>{let r,a;const{selectorChangedWithUnbind:s}=((f,b)=>{let y,C;const k=(N,R)=>he(R,I=>f.is(I,N)),E=N=>f.getParents(N,void 0,f.getRoot());return{selectorChangedWithUnbind:(N,R)=>(y||(y={},C={},b.on("NodeChange",I=>{const q=I.element,H=E(q),T={};le(y,(P,B)=>{k(B,H).each(j=>{C[B]||(J(P,lt=>{lt(!0,{node:j,selector:B,parents:H})}),C[B]=P),T[B]=P})}),le(C,(P,B)=>{T[B]||(delete C[B],J(P,j=>{j(!1,{node:q,selector:B,parents:H})}))})})),y[N]||(y[N]=[]),y[N].push(R),k(N,E(b.selection.getStart())).each(()=>{C[N]=y[N]}),{unbind:()=>{nC(y,N,R),nC(C,N,R)}})}})(t,o),i=(f,b)=>((y,C,k={})=>{const E=(N=k,R=C,{format:"html",...N,set:!0,selection:!0,content:R});var N,R;Pg(y,E).each(N=>{const R=((q,H)=>{if("raw"!==H.format){const T=q.selection.getRng(),P=q.dom.getParent(T.commonAncestorContainer,q.dom.isBlock),B=P?{context:P.nodeName.toLowerCase()}:{},j=q.parser.parse(H.content,{forced_root_block:!1,...B,...H});return ba({validate:!1},q.schema).serialize(j)}return H.content})(y,N),I=y.selection.getRng();((q,H)=>{const T=S.from(H.firstChild).map(A),P=S.from(H.lastChild).map(A);q.deleteContents(),q.insertNode(H);const B=T.bind(Mr).filter(pn).bind(tC),j=P.bind(ea).filter(pn).bind(tC);eC(B,T,q,!0),eC(j,P,q,!1),q.collapse(!1)})(I,I.createContextualFragment(R)),y.selection.setRng(I),cl(y,I),Mg(y,R,N)})})(o,f,b),l=f=>{const b=d();b.collapse(!!f),u(b)},c=()=>e.getSelection?e.getSelection():e.document.selection,d=()=>{let f;const b=(C,k,E)=>{try{return k.compareBoundaryPoints(C,E)}catch{return-1}},y=e.document;if(st(o.bookmark)&&!is(o)){const C=Am(o);if(C.isSome())return C.map(k=>Mm(o,[k])[0]).getOr(y.createRange())}try{const C=c();C&&!He(C.anchorNode)&&(f=C.rangeCount>0?C.getRangeAt(0):y.createRange(),f=Mm(o,[f])[0])}catch{}if(f||(f=y.createRange()),Di(f.startContainer)&&f.collapsed){const C=t.getRoot();f.setStart(C,0),f.setEnd(C,0)}return r&&a&&(0===b(f.START_TO_START,f,r)&&0===b(f.END_TO_END,f,r)?f=a:(r=null,a=null)),f},u=(f,b)=>{if(!((C=f)&&oC(C.startContainer)&&oC(C.endContainer)))return;var C;const y=c();if(f=o.dispatch("SetSelectionRange",{range:f,forward:b}).range,y){a=f;try{y.removeAllRanges(),y.addRange(f)}catch{}!1===b&&y.extend&&(y.collapse(f.endContainer,f.endOffset),y.extend(f.startContainer,f.startOffset)),r=y.rangeCount>0?y.getRangeAt(0):null}if(!f.collapsed&&f.startContainer===f.endContainer&&y?.setBaseAndExtent&&f.endOffset-f.startOffset<2&&f.startContainer.hasChildNodes()){const C=f.startContainer.childNodes[f.startOffset];C&&"IMG"===C.nodeName&&(y.setBaseAndExtent(f.startContainer,f.startOffset,f.endContainer,f.endOffset),y.anchorNode===f.startContainer&&y.focusNode===f.endContainer||y.setBaseAndExtent(C,0,C,1))}o.dispatch("AfterSetSelectionRange",{range:f,forward:b})},m=()=>{const f=c(),b=f?.anchorNode,y=f?.focusNode;if(!f||!b||!y||He(b)||He(y))return!0;const C=t.createRng(),k=t.createRng();try{C.setStart(b,f.anchorOffset),C.collapse(!0),k.setStart(y,f.focusOffset),k.collapse(!0)}catch{return!0}return C.compareBoundaryPoints(C.START_TO_START,k)<=0},p={dom:t,win:e,serializer:n,editor:o,expand:(f={type:"word"})=>u(ss(t).expand(d(),f)),collapse:l,setCursorLocation:(f,b)=>{const y=t.createRng();st(f)&&st(b)?(y.setStart(f,b),y.setEnd(f,b),u(y),l(!1)):(dm(t,y,o.getBody(),!0),u(y))},getContent:f=>((b,y={})=>{return k=y.format?y.format:"html",E=y,Te(b).selection.getContent(k,E);var k,E})(o,f),setContent:i,getBookmark:(f,b)=>g.getBookmark(f,b),moveToBookmark:f=>g.moveToBookmark(f),select:(f,b)=>{return(y=t,C=f,k=b,S.from(C).bind(E=>S.from(E.parentNode).map(N=>{const R=y.nodeIndex(E),I=y.createRng();return I.setStart(N,R),I.setEnd(N,R+1),k&&(dm(y,I,E,!0),dm(y,I,E,!1)),I}))).each(u),f;var y,C,k},isCollapsed:()=>{const f=d(),b=c();return!(!f||f.item)&&(f.compareEndPoints?0===f.compareEndPoints("StartToEnd",f):!b||f.collapsed)},isEditable:()=>{const f=d(),b=o.getBody().querySelectorAll('[data-mce-selected="1"]');return b.length>0?_n(b,y=>t.isEditable(y.parentElement)):f.startContainer===f.endContainer?t.isEditable(f.startContainer):t.isEditable(f.startContainer)&&t.isEditable(f.endContainer)},isForward:m,setNode:f=>(i(t.getOuterHTML(f)),f),getNode:()=>((f,b)=>{if(!b)return f;let y=b.startContainer,C=b.endContainer;const k=b.startOffset,E=b.endOffset;let N=b.commonAncestorContainer;b.collapsed||(y===C&&E-k<2&&y.hasChildNodes()&&(N=y.childNodes[k]),tt(y)&&tt(C)&&(y=y.length===k?wv(y.nextSibling,!0):y.parentNode,C=0===E?wv(C.previousSibling,!1):C.parentNode,y&&y===C&&(N=y)));const R=tt(N)?N.parentNode:N;return it(R)?R:f})(o.getBody(),d()),getSel:c,setRng:u,getRng:d,getStart:f=>yv(o.getBody(),d(),f),getEnd:f=>Cv(o.getBody(),d(),f),getSelectedBlocks:(f,b)=>((y,C,k,E)=>{const N=[],R=y.getRoot(),I=y.getParent(k||yv(R,C,C.collapsed),y.isBlock),q=y.getParent(E||Cv(R,C,C.collapsed),y.isBlock);if(I&&I!==R&&N.push(I),I&&q&&I!==q){let H;const T=new Kt(I,R);for(;(H=T.next())&&H!==q;)y.isBlock(H)&&N.push(H)}return q&&I!==q&&q!==R&&N.push(q),N})(t,d(),f,b),normalize:()=>{const f=d(),b=c();if(!(im(b).length>1)&&Uc(o)){const y=Wc(t,f);return y.each(C=>{u(C,m())}),y.getOr(f)}return f},selectorChanged:(f,b)=>(s(f,b),p),selectorChangedWithUnbind:s,getScrollContainer:()=>{let f,b=t.getRoot();for(;b&&"BODY"!==b.nodeName;){if(b.scrollHeight>b.clientHeight){f=b;break}b=b.parentNode}return f},scrollIntoView:(f,b)=>{var y;st(f)?((y=o).inline?M_:I_)(y,f,b):cl(o,d(),b)},placeCaretAt:(f,b)=>u(Jb(f,b,o.getDoc())),getBoundingClientRect:()=>{const f=d();return f.collapsed?$.fromRangeStart(f).getClientRects()[0]:f.getBoundingClientRect()},destroy:()=>{e=r=a=null,h.destroy()}},g=il(p),h=Kb(p,o);return p.bookmarkManager=g,p.controlSelection=h,p},pR=(t,e,n)=>{-1===ot.inArray(e,n)&&(t.addAttributeFilter(n,(o,r)=>{let a=o.length;for(;a--;)o[a].attr(r,null)}),e.push(n))},aC=(t,e)=>{const n=((t,e)=>{const n=["data-mce-selected"],o={entity_encoding:"named",remove_trailing_brs:!0,...t},r=e&&e.dom?e.dom:me.DOM,a=e&&e.schema?e.schema:ua(o),s=ti(o,a);return l=o,c=r,(i=s).addAttributeFilter("data-mce-tabindex",(d,u)=>{let m=d.length;for(;m--;){const p=d[m];p.attr("tabindex",p.attr("data-mce-tabindex")),p.attr(u,null)}}),i.addAttributeFilter("src,href,style",(d,u)=>{const m="data-mce-"+u,p=l.url_converter,g=l.url_converter_scope;let h=d.length;for(;h--;){const f=d[h];let b=f.attr(m);void 0!==b?(f.attr(u,b.length>0?b:null),f.attr(m,null)):(b=f.attr(u),"style"===u?b=c.serializeStyle(c.parseStyle(b),f.name):p&&(b=p.call(g,b,u,f.name)),f.attr(u,b.length>0?b:null))}}),i.addAttributeFilter("class",d=>{let u=d.length;for(;u--;){const m=d[u];let p=m.attr("class");p&&(p=p.replace(/(?:^|\s)mce-item-\w+(?!\S)/g,""),m.attr("class",p.length>0?p:null))}}),i.addAttributeFilter("data-mce-type",(d,u,m)=>{let p=d.length;for(;p--;){const g=d[p];"bookmark"===g.attr("data-mce-type")&&!m.cleanup&&(S.from(g.firstChild).exists(f=>{var b;return!pc(null!==(b=f.value)&&void 0!==b?b:"")})?g.unwrap():g.remove())}}),i.addNodeFilter("noscript",d=>{var u;let m=d.length;for(;m--;){const p=d[m].firstChild;p&&(p.value=da.decode(null!==(u=p.value)&&void 0!==u?u:""))}}),i.addNodeFilter("script,style",(d,u)=>{var m;const p=h=>h.replace(/(<!--\[CDATA\[|\]\]-->)/g,"\n").replace(/^[\r\n]*|[\r\n]*$/g,"").replace(/^\s*((<!--)?(\s*\/\/)?\s*<!\[CDATA\[|(<!--\s*)?\/\*\s*<!\[CDATA\[\s*\*\/|(\/\/)?\s*<!--|\/\*\s*<!--\s*\*\/)\s*[\r\n]*/gi,"").replace(/\s*(\/\*\s*\]\]>\s*\*\/(-->)?|\s*\/\/\s*\]\]>(-->)?|\/\/\s*(-->)?|\]\]>|\/\*\s*-->\s*\*\/|\s*-->\s*)\s*$/g,"");let g=d.length;for(;g--;){const h=d[g],f=h.firstChild,b=null!==(m=f?.value)&&void 0!==m?m:"";if("script"===u){const y=h.attr("type");y&&h.attr("type","mce-no/type"===y?null:y.replace(/^mce\-/,"")),"xhtml"===l.element_format&&f&&b.length>0&&(f.value="// <![CDATA[\n"+p(b)+"\n// ]]>")}else"xhtml"===l.element_format&&f&&b.length>0&&(f.value="\x3c!--\n"+p(b)+"\n--\x3e")}}),i.addNodeFilter("#comment",d=>{let u=d.length;for(;u--;){const m=d[u],p=m.value;l.preserve_cdata&&0===p?.indexOf("[CDATA[")?(m.name="#cdata",m.type=4,m.value=c.decode(p.replace(/^\[CDATA\[|\]\]$/g,""))):0===p?.indexOf("mce:protected ")&&(m.name="#text",m.type=3,m.raw=!0,m.value=unescape(p).substr(14))}}),i.addNodeFilter("xml:namespace,input",(d,u)=>{let m=d.length;for(;m--;){const p=d[m];7===p.type?p.remove():1===p.type&&("input"!==u||p.attr("type")||p.attr("type","text"))}}),i.addAttributeFilter("data-mce-type",d=>{J(d,u=>{"format-caret"===u.attr("data-mce-type")&&(u.isEmpty(i.schema.getNonEmptyElements())?u.remove():u.unwrap())})}),i.addAttributeFilter("data-mce-src,data-mce-href,data-mce-style,data-mce-selected,data-mce-expando,data-mce-block,data-mce-type,data-mce-resize,data-mce-placeholder",(d,u)=>{let m=d.length;for(;m--;)d[m].attr(u,null)}),l.remove_trailing_brs&&hy(i,i.schema),{schema:a,addNodeFilter:s.addNodeFilter,addAttributeFilter:s.addAttributeFilter,serialize:(i,l={})=>{const c={format:"html",...l},d=(g=i,b=h=c,st(f=p=e)&&f.hasEventListeners("PreProcess")&&!b.no_events?((f,b,y)=>{let C;const k=f.dom;let E=b.cloneNode(!0);const N=document.implementation;if(N.createHTMLDocument){const R=N.createHTMLDocument("");ot.each("BODY"===E.nodeName?E.childNodes:[E],I=>{R.body.appendChild(R.importNode(I,!0))}),E="BODY"!==E.nodeName?R.body.firstChild:R.body,C=k.doc,k.doc=R}return I={...y,node:E},f.dispatch("PreProcess",I),C&&(k.doc=C),E;var I})(p,g,h):g),u=((p,g,h)=>{const f=Fr(h.getInner?g.innerHTML:p.getOuterHTML(g));return h.selection||gc(A(g))?f:ot.trim(f)})(r,d,c),m=((p,g,h)=>{const f=h.selection?{forced_root_block:!1,...h}:h,b=p.parse(g,f);return(y=>{const C=E=>"br"===E?.name,k=y.lastChild;if(C(k)){const E=k.prev;C(E)&&(k.remove(),E.remove())}})(b),b})(s,u,c);var p,g,h,f,b;return"tree"===c.format?m:((p,g,h,f,b)=>{var E;return((C,k,E)=>{return!k.no_events&&C?(R=C,I={...k,content:E},R.dispatch("PostProcess",I)).content:E;var R,I})(p,b,(E=f,ba(g,h).serialize(E)))})(e,o,a,m,c)},addRules:a.addValidElements,setRules:a.setValidElements,addTempAttr:Ct(pR,s,n),getTempAttrs:ut(n),getNodeFilters:s.getNodeFilters,getAttributeFilters:s.getAttributeFilters,removeNodeFilter:s.removeNodeFilter,removeAttributeFilter:s.removeAttributeFilter};var i,l,c})(t,e);return{schema:n.schema,addNodeFilter:n.addNodeFilter,addAttributeFilter:n.addAttributeFilter,serialize:n.serialize,addRules:n.addRules,setRules:n.setRules,addTempAttr:n.addTempAttr,getTempAttrs:n.getTempAttrs,getNodeFilters:n.getNodeFilters,getAttributeFilters:n.getAttributeFilters,removeNodeFilter:n.removeNodeFilter,removeAttributeFilter:n.removeAttributeFilter}},Hg=(t,e,n={})=>{const o=(r=n,a=e,{format:"html",...r,set:!0,content:a});var r,a;return Pg(t,o).map(r=>{const a=(i=r.content,l=r,zg(t).editor.setContent(i,l));var i,l;return Mg(t,a.html,r),a.content}).getOr(e)},bR="autoresize_on_init,content_editable_state,padd_empty_with_br,block_elements,boolean_attributes,editor_deselector,editor_selector,elements,file_browser_callback_types,filepicker_validator_handler,force_hex_style_colors,force_p_newlines,gecko_spellcheck,images_dataimg_filter,media_scripts,mode,move_caret_before_on_enter_elements,non_empty_elements,self_closing_elements,short_ended_elements,special,spellchecker_select_languages,spellchecker_whitelist,tab_focus,tabfocus_elements,table_responsive_width,text_block_elements,text_inline_elements,toolbar_drawer,types,validate,whitespace_elements,paste_enable_default_filters,paste_filter_drop,paste_word_valid_elements,paste_retain_style_properties,paste_convert_word_fake_lists".split(","),vR="template_cdate_classes,template_mdate_classes,template_selected_content_classes,template_preview_replace_values,template_replace_values,templates,template_cdate_format,template_mdate_format".split(","),yR="bbcode,colorpicker,contextmenu,fullpage,legacyoutput,spellchecker,textcolor".split(","),sC=[{name:"template",replacedWith:"Advanced Template"},{name:"rtc"}],iC=(t,e)=>{const n=Ot(e,o=>It(t,o));return To(n)},lC=(t,e)=>{const n=ot.makeMap(t.plugins," "),o=Ot(e,r=>It(n,r));return To(o)},kR=t=>he(sC,e=>e.name===t).fold(()=>t,e=>e.replacedWith?`${t}, replaced by ${e.replacedWith}`:t),gd=me.DOM,fd=t=>S.from(t).each(e=>e.destroy()),pd=(()=>{const t={};return{add:(e,n)=>{t[e]=n},get:e=>t[e]?t[e]:{icons:{}},has:e=>It(t,e)}})(),ei=vn.ModelManager,cC=(t,e)=>e.dom[t],dC=(t,e)=>parseInt(xo(e,t),10),ER=Ct(cC,"clientWidth"),_R=Ct(cC,"clientHeight"),NR=Ct(dC,"margin-top"),RR=Ct(dC,"margin-left"),uC=t=>{const e=[],n=()=>{const l=t.theme;return l&&l.getNotificationManagerImpl?l.getNotificationManagerImpl():(()=>{const c=()=>{throw new Error("Theme did not provide a NotificationManager implementation.")};return{open:c,close:c,getArgs:c}})()},o=()=>S.from(e[0]),r=()=>{J(e,l=>{l.reposition()})},a=l=>{go(e,c=>c===l).each(c=>{e.splice(c,1)})},s=(l,c=!0)=>{return t.removed||!(u=(d=t).inline?d.getBody():d.getContentAreaContainer(),S.from(u).map(A)).map(ra).getOr(!1)?{}:(c&&t.dispatch("BeforeOpenNotification",{notification:l}),he(e,d=>{return!((u=n().getArgs(d)).type!==(m=l).type||u.text!==m.text||u.progressBar||u.timeout||m.progressBar||m.timeout);var u,m}).getOrThunk(()=>{t.editorManager.setActive(t);const d=n().open(l,()=>{a(d),r(),o().fold(()=>t.focus(),u=>uv(A(u.getEl())))});return(u=>{e.push(u)})(d),r(),t.dispatch("OpenNotification",{notification:{...d}}),d}));var d,u},i=ut(e);return(l=t).on("SkinLoaded",()=>{const c=iE(l);c&&s({text:c,type:"warning",timeout:0},!1),r()}),l.on("show ResizeEditor ResizeWindow NodeChange",()=>{requestAnimationFrame(r)}),l.on("remove",()=>{J(e.slice(),c=>{n().close(c)})}),{open:s,close:()=>{o().each(l=>{n().close(l),a(l),r()})},getNotifications:i};var l},ni=vn.PluginManager,fs=vn.ThemeManager,mC=t=>{let e=[];const n=()=>{const s=t.theme;return s&&s.getWindowManagerImpl?s.getWindowManagerImpl():(()=>{const i=()=>{throw new Error("Theme did not provide a WindowManager implementation.")};return{open:i,openUrl:i,alert:i,confirm:i,close:i}})()},o=(s,i)=>(...l)=>i?i.apply(s,l):void 0,r=s=>{t.dispatch("CloseWindow",{dialog:s}),e=Ot(e,i=>i!==s),0===e.length&&t.focus()},a=s=>{t.editorManager.setActive(t),Rm(t),t.ui.show();const i=s();return e.push(l=i),t.dispatch("OpenWindow",{dialog:l}),i;var l};return t.on("remove",()=>{J(e,s=>{n().close(s)})}),{open:(s,i)=>a(()=>n().open(s,i,r)),openUrl:s=>a(()=>n().openUrl(s,r)),alert:(s,i,l)=>{const c=n();c.alert(s,o(l||c,i))},confirm:(s,i,l)=>{const c=n();c.confirm(s,o(l||c,i))},close:()=>{S.from(e[e.length-1]).each(s=>{n().close(s),r(s)})}}},gC=(t,e)=>{t.notificationManager.open({type:"error",text:e})},hd=(t,e)=>{t._skinLoaded?gC(t,e):t.on("SkinLoaded",()=>{gC(t,e)})},yl=(t,e,n)=>{jb(t,e,{message:n}),console.error(n)},Cl=(t,e,n)=>n?`Failed to load ${t}: ${n} from url ${e}`:`Failed to load ${t} url: ${e}`,bd=(t,...e)=>{const n=window.console;n&&(n.error?n.error(t,...e):n.log(t,...e))},fC=(t,e)=>{const n=t.editorManager.baseURL+"/skins/content",o=`content${t.editorManager.suffix}.css`;return te(e,r=>/^[a-z0-9\-]+$/i.test(r)&&!t.inline?`${n}/${r}/${o}`:t.documentBaseURI.toAbsolute(r))},pC=()=>{let t={};const e=(o,r)=>({status:o,resultUri:r}),n=o=>o in t;return{hasBlobUri:n,getResultUri:o=>{const r=t[o];return r?r.resultUri:null},isPending:o=>!!n(o)&&1===t[o].status,isUploaded:o=>!!n(o)&&2===t[o].status,markPending:o=>{t[o]=e(1,null)},markUploaded:(o,r)=>{t[o]=e(2,r)},removeFailed:o=>{delete t[o]},destroy:()=>{t={}}}};let TR=0;const hC=t=>()=>t.notificationManager.open({text:t.translate("Image uploading..."),type:"info",timeout:-1,progressBar:!0}),bC=(t,e)=>((t,e)=>{const n={},o=(l,c)=>new Promise((d,u)=>{const m=new XMLHttpRequest;m.open("POST",e.url),m.withCredentials=e.credentials,m.upload.onprogress=g=>{c(g.loaded/g.total*100)},m.onerror=()=>{u("Image upload failed due to a XHR Transport error. Code: "+m.status)},m.onload=()=>{if(m.status<200||m.status>=300)return void u("HTTP Error: "+m.status);const g=JSON.parse(m.responseText);var h,f;g&&Nt(g.location)?d((f=g.location,(h=e.basePath)?h.replace(/\/$/,"")+"/"+f.replace(/^\//,""):f)):u("Invalid JSON: "+m.responseText)};const p=new FormData;p.append("file",l.blob(),l.filename()),m.send(p)}),r=Yt(e.handler)?e.handler:o,a=(l,c)=>({url:c,blobInfo:l,status:!0}),s=(l,c)=>({url:"",blobInfo:l,status:!1,error:c}),i=(l,c)=>{ot.each(n[l],d=>{d(c)}),delete n[l]};return{upload:(l,c)=>{return e.url||r!==o?(u=c,d=ot.grep(d=l,m=>!t.isUploaded(m.blobUri())),Promise.all(ot.map(d,m=>{return t.isPending(m.blobUri())?(p=>{const g=p.blobUri();return new Promise(h=>{n[g]=n[g]||[],n[g].push(h)})})(m):(g=r,h=u,t.markPending((p=m).blobUri()),new Promise(f=>{let b,y;try{const C=()=>{b&&(b.close(),y=jt)},k=N=>{C(),t.markUploaded(p.blobUri(),N),i(p.blobUri(),a(p,N)),f(a(p,N))},E=N=>{C(),t.removeFailed(p.blobUri()),i(p.blobUri(),s(p,N)),f(s(p,N))};y=N=>{N<0||N>100||S.from(b).orThunk(()=>S.from(h).map(je)).each(R=>{b=R,R.progressBar.value(N)})},g(p,y).then(k,N=>{E(Nt(N)?{message:N}:N)})}catch(C){f(s(p,C))}}));var p,g,h}))):new Promise(d=>{d([])});var d,u}}})(e,{url:Gk(t),basePath:Yk(t),credentials:Xk(t),handler:Qk(t)}),vC={remove_similar:!0,inherit:!1},oi={selector:"td,th",...vC},DR={tablecellbackgroundcolor:{styles:{backgroundColor:"%value"},...oi},tablecellverticalalign:{styles:{"vertical-align":"%value"},...oi},tablecellbordercolor:{styles:{borderColor:"%value"},...oi},tablecellclass:{classes:["%value"],...oi},tableclass:{selector:"table",classes:["%value"],...vC},tablecellborderstyle:{styles:{borderStyle:"%value"},...oi},tablecellborderwidth:{styles:{borderWidth:"%value"},...oi}},PR=ut(DR),vd=ot.each,lo=me.DOM,$g=t=>st(t)&&At(t),yC=(t,e)=>{const n=e&&e.schema||ua({}),o=s=>{const i=Nt(s)?{name:s,classes:[],attrs:{}}:s,l=lo.create(i.name);return c=l,(d=i).classes.length>0&&lo.addClass(c,d.classes.join(" ")),lo.setAttribs(c,d.attrs),l;var c,d},r=(s,i,l)=>{let c;const d=i[0],u=$g(d)?d.name:void 0,m=((h,f)=>{const b=n.getElementRule(h.nodeName.toLowerCase()),y=b?.parentsRequired;return!(!y||!y.length)&&(f&&Lt(y,f)?f:y[0])})(s,u);if(m)u===m?(c=d,i=i.slice(1)):c=m;else if(d)c=d,i=i.slice(1);else if(!l)return s;const p=c?o(c):lo.create("div");p.appendChild(s),l&&ot.each(l,h=>{const f=o(h);p.insertBefore(f,s)});const g=$g(c)?c.siblings:void 0;return r(p,i,g)},a=lo.create("div");if(t.length>0){const s=t[0],i=o(s),l=$g(s)?s.siblings:void 0;a.appendChild(r(i,t.slice(1),l))}return a},MR=t=>{let e="div";const n={name:e,classes:[],attrs:{},selector:t=ot.trim(t)};return"*"!==t&&(e=t.replace(/(?:([#\.]|::?)([\w\-]+)|(\[)([^\]]+)\]?)/g,(o,r,a,s,i)=>{switch(r){case"#":n.attrs.id=a;break;case".":n.classes.push(a);break;case":":-1!==ot.inArray("checked disabled enabled read-only required".split(" "),a)&&(n.attrs[a]=a)}if("["===s){const l=i.match(/([\w\-]+)(?:\=\"([^\"]+))?/);l&&(n.attrs[l[1]]=l[2])}return""})),n.name=e||"div",n},LR=(t,e)=>{let n="",o=uE(t);if(""===o)return"";const r=p=>Nt(p)?p.replace(/%(\w+)/g,""):"",a=(p,g)=>lo.getStyle(g??t.getBody(),p,!0);if(Nt(e)){const p=t.formatter.get(e);if(!p)return"";e=p[0]}if("preview"in e){const p=e.preview;if(!1===p)return"";o=p||o}let s,i=e.block||e.inline||"span";const l=Nt(c=e.selector)?(c=(c=c.split(/\s*,\s*/)[0]).replace(/\s*(~\+|~|\+|>)\s*/g,"$1"),ot.map(c.split(/(?:>|\s+(?![^\[\]]+\]))/),p=>{const g=ot.map(p.split(/(?:~\+|~|\+)/),MR),h=g.pop();return g.length&&(h.siblings=g),h}).reverse()):[];var c;l.length>0?(l[0].name||(l[0].name=i),i=e.selector,s=yC(l,t)):s=yC([i],t);const d=lo.select(i,s)[0]||s.firstChild;vd(e.styles,(p,g)=>{const h=r(p);h&&lo.setStyle(d,g,h)}),vd(e.attributes,(p,g)=>{const h=r(p);h&&lo.setAttrib(d,g,h)}),vd(e.classes,p=>{const g=r(p);lo.hasClass(d,g)||lo.addClass(d,g)}),t.dispatch("PreviewFormats"),lo.setStyles(s,{position:"absolute",left:-65535}),t.getBody().appendChild(s);const u=a("fontSize"),m=/px$/.test(u)?parseInt(u,10):0;return vd(o.split(" "),p=>{let g=a(p,d);if(!("background-color"===p&&/transparent|rgba\s*\([^)]+,\s*0\)/.test(g)&&(g=a(p),"#ffffff"===Fc(g).toLowerCase())||"color"===p&&"#000000"===Fc(g).toLowerCase())){if("font-size"===p&&/em|%$/.test(g)){if(0===m)return;g=parseFloat(g)/(/%$/.test(g)?100:1)*m+"px"}"border"===p&&g&&(n+="padding:0 2px;"),n+=p+":"+g+";"}}),t.dispatch("AfterPreviewFormats"),lo.remove(s),n},CC=t=>{const e=(o=>{const r={},a=(s,i)=>{s&&(Nt(s)?(Ee(i)||(i=[i]),J(i,l=>{dt(l.deep)&&(l.deep=!ao(l)),dt(l.split)&&(l.split=!ao(l)||$e(l)),dt(l.remove)&&ao(l)&&!$e(l)&&(l.remove="none"),ao(l)&&$e(l)&&(l.mixed=!0,l.block_expand=!0),Nt(l.classes)&&(l.classes=l.classes.split(/\s+/))}),r[s]=i):le(s,(l,c)=>{a(c,l)}))};return a((s=>{const i=s.dom,l=s.schema.type,c={valigntop:[{selector:"td,th",styles:{verticalAlign:"top"}}],valignmiddle:[{selector:"td,th",styles:{verticalAlign:"middle"}}],valignbottom:[{selector:"td,th",styles:{verticalAlign:"bottom"}}],alignleft:[{selector:"figure.image",collapsed:!1,classes:"align-left",ceFalseOverride:!0,preview:"font-family font-size"},{selector:"figure,p,h1,h2,h3,h4,h5,h6,td,th,tr,div,ul,ol,li,pre",styles:{textAlign:"left"},inherit:!1,preview:!1},{selector:"img,audio,video",collapsed:!1,styles:{float:"left"},preview:"font-family font-size"},{selector:"table",collapsed:!1,styles:{marginLeft:"0px",marginRight:"auto"},onformat:d=>{i.setStyle(d,"float",null)},preview:"font-family font-size"},{selector:".mce-preview-object,[data-ephox-embed-iri]",ceFalseOverride:!0,styles:{float:"left"}}],aligncenter:[{selector:"figure,p,h1,h2,h3,h4,h5,h6,td,th,tr,div,ul,ol,li,pre",styles:{textAlign:"center"},inherit:!1,preview:"font-family font-size"},{selector:"figure.image",collapsed:!1,classes:"align-center",ceFalseOverride:!0,preview:"font-family font-size"},{selector:"img,audio,video",collapsed:!1,styles:{display:"block",marginLeft:"auto",marginRight:"auto"},preview:!1},{selector:"table",collapsed:!1,styles:{marginLeft:"auto",marginRight:"auto"},preview:"font-family font-size"},{selector:".mce-preview-object",ceFalseOverride:!0,styles:{display:"table",marginLeft:"auto",marginRight:"auto"},preview:!1},{selector:"[data-ephox-embed-iri]",ceFalseOverride:!0,styles:{marginLeft:"auto",marginRight:"auto"},preview:!1}],alignright:[{selector:"figure.image",collapsed:!1,classes:"align-right",ceFalseOverride:!0,preview:"font-family font-size"},{selector:"figure,p,h1,h2,h3,h4,h5,h6,td,th,tr,div,ul,ol,li,pre",styles:{textAlign:"right"},inherit:!1,preview:"font-family font-size"},{selector:"img,audio,video",collapsed:!1,styles:{float:"right"},preview:"font-family font-size"},{selector:"table",collapsed:!1,styles:{marginRight:"0px",marginLeft:"auto"},onformat:d=>{i.setStyle(d,"float",null)},preview:"font-family font-size"},{selector:".mce-preview-object,[data-ephox-embed-iri]",ceFalseOverride:!0,styles:{float:"right"},preview:!1}],alignjustify:[{selector:"figure,p,h1,h2,h3,h4,h5,h6,td,th,tr,div,ul,ol,li,pre",styles:{textAlign:"justify"},inherit:!1,preview:"font-family font-size"}],bold:[{inline:"strong",remove:"all",preserve_attributes:["class","style"]},{inline:"span",styles:{fontWeight:"bold"}},{inline:"b",remove:"all",preserve_attributes:["class","style"]}],italic:[{inline:"em",remove:"all",preserve_attributes:["class","style"]},{inline:"span",styles:{fontStyle:"italic"}},{inline:"i",remove:"all",preserve_attributes:["class","style"]}],underline:[{inline:"span",styles:{textDecoration:"underline"},exact:!0},{inline:"u",remove:"all",preserve_attributes:["class","style"]}],strikethrough:(()=>{const d={inline:"span",styles:{textDecoration:"line-through"},exact:!0},u={inline:"strike",remove:"all",preserve_attributes:["class","style"]},m={inline:"s",remove:"all",preserve_attributes:["class","style"]};return"html4"!==l?[m,d,u]:[d,m,u]})(),forecolor:{inline:"span",styles:{color:"%value"},links:!0,remove_similar:!0,clear_child_styles:!0},hilitecolor:{inline:"span",styles:{backgroundColor:"%value"},links:!0,remove_similar:!0,clear_child_styles:!0},fontname:{inline:"span",toggle:!1,styles:{fontFamily:"%value"},clear_child_styles:!0},fontsize:{inline:"span",toggle:!1,styles:{fontSize:"%value"},clear_child_styles:!0},lineheight:{selector:"h1,h2,h3,h4,h5,h6,p,li,td,th,div",styles:{lineHeight:"%value"}},fontsize_class:{inline:"span",attributes:{class:"%value"}},blockquote:{block:"blockquote",wrapper:!0,remove:"all"},subscript:{inline:"sub"},superscript:{inline:"sup"},code:{inline:"code"},link:{inline:"a",selector:"a",remove:"all",split:!0,deep:!0,onmatch:(d,u,m)=>it(d)&&d.hasAttribute("href"),onformat:(d,u,m)=>{ot.each(m,(p,g)=>{i.setAttrib(d,g,p)})}},lang:{inline:"span",clear_child_styles:!0,remove_similar:!0,attributes:{lang:"%value","data-mce-lang":d=>{var u;return null!==(u=d?.customValue)&&void 0!==u?u:null}}},removeformat:[{selector:"b,strong,em,i,font,u,strike,s,sub,sup,dfn,code,samp,kbd,var,cite,mark,q,del,ins,small",remove:"all",split:!0,expand:!1,block_expand:!0,deep:!0},{selector:"span",attributes:["style","class"],remove:"empty",split:!0,expand:!1,deep:!0},{selector:"*",attributes:["style","class"],split:!1,expand:!1,deep:!0}]};return ot.each("p h1 h2 h3 h4 h5 h6 div address pre dt dd samp".split(/\s/),d=>{c[d]={block:d,remove:"all"}}),c})(o)),a(PR()),a(dE(o)),{get:s=>st(s)?r[s]:r,has:s=>It(r,s),register:a,unregister:s=>(s&&r[s]&&delete r[s],r)}})(t),n=Ye({});return(o=>{o.addShortcut("meta+b","","Bold"),o.addShortcut("meta+i","","Italic"),o.addShortcut("meta+u","","Underline");for(let r=1;r<=6;r++)o.addShortcut("access+"+r,"",["FormatBlock",!1,"h"+r]);o.addShortcut("access+7","",["FormatBlock",!1,"p"]),o.addShortcut("access+8","",["FormatBlock",!1,"div"]),o.addShortcut("access+9","",["FormatBlock",!1,"address"])})(t),(o=t).on("mouseup keydown",r=>{var a;((s,i,l)=>{const c=s.selection,d=s.getBody();bg(s,null,l),8!==i&&46!==i||!c.isCollapsed()||c.getStart().innerHTML!==Zs||bg(s,ns(d,c.getStart())),37!==i&&39!==i||bg(s,ns(d,c.getStart()))})(o,r.keyCode,(a=o.selection.getRng().endContainer,tt(a)&&pt(a.data,Ge)))}),dr(t)||((o,r)=>{o.set({}),r.on("NodeChange",a=>{gy(r,a.element,o.get())}),r.on("FormatApply FormatRemove",a=>{const s=S.from(a.node).map(i=>al(i)?i:i.startContainer).bind(i=>it(i)?S.some(i):S.from(i.parentElement)).getOrThunk(()=>dy(r));gy(r,s,o.get())})})(n,t),{get:e.get,has:e.has,register:e.register,unregister:e.unregister,apply:(o,r,a)=>{var i,l,c;i=o,l=r,c=a,Te(t).formatter.apply(i,l,c)},remove:(o,r,a,s)=>{var l,c,d,u;l=o,c=r,d=a,u=s,Te(t).formatter.remove(l,c,d,u)},toggle:(o,r,a)=>{var i,l,c;i=o,l=r,c=a,Te(t).formatter.toggle(i,l,c)},match:(o,r,a,s)=>{return l=o,c=r,d=a,u=s,Te(t).formatter.match(l,c,d,u);var l,c,d,u},closest:o=>{return a=o,Te(t).formatter.closest(a);var a},matchAll:(o,r)=>{return s=o,i=r,Te(t).formatter.matchAll(s,i);var s,i},matchNode:(o,r,a,s)=>{return l=o,c=r,d=a,u=s,Te(t).formatter.matchNode(l,c,d,u);var l,c,d,u},canApply:o=>{return a=o,Te(t).formatter.canApply(a);var a},formatChanged:(o,r,a,s)=>{return l=n,c=o,d=r,u=a,m=s,Te(t).formatter.formatChanged(l,c,d,u,m);var l,c,d,u,m},getCssText:Ct(LR,t)};var o},wC=t=>{switch(t.toLowerCase()){case"undo":case"redo":case"mcefocus":return!0;default:return!1}},xC=t=>{const e=ma(),n=Ye(0),o=Ye(0),r={data:[],typing:!1,beforeChange:()=>{var s,i;s=n,i=e,Te(t).undoManager.beforeChange(s,i)},add:(a,s)=>{return l=r,c=o,d=n,u=e,m=a,p=s,Te(t).undoManager.add(l,c,d,u,m,p);var l,c,d,u,m,p},dispatchChange:()=>{t.setDirty(!0);const a=ud(t);a.bookmark=qu(t.selection),t.dispatch("change",{level:a,lastLevel:Wo(r.data,o.get()).getOrUndefined()})},undo:()=>{return s=r,i=n,l=o,Te(t).undoManager.undo(s,i,l);var s,i,l},redo:()=>{return s=o,i=r.data,Te(t).undoManager.redo(s,i);var s,i},clear:()=>{var s,i;s=r,i=o,Te(t).undoManager.clear(s,i)},reset:()=>{var s;s=r,Te(t).undoManager.reset(s)},hasUndo:()=>{return s=r,i=o,Te(t).undoManager.hasUndo(s,i);var s,i},hasRedo:()=>{return s=r,i=o,Te(t).undoManager.hasRedo(s,i);var s,i},transact:a=>{return i=r,l=n,c=a,Te(t).undoManager.transact(i,l,c);var i,l,c},ignore:a=>{var i,l;i=n,l=a,Te(t).undoManager.ignore(i,l)},extra:(a,s)=>{var l,c,d,u;l=r,c=o,d=a,u=s,Te(t).undoManager.extra(l,c,d,u)}};return dr(t)||((a,s,i)=>{const l=Ye(!1),c=d=>{md(s,!1,i),s.add({},d)};a.on("init",()=>{s.add()}),a.on("BeforeExecCommand",d=>{wC(d.command)||(Qy(s,i),s.beforeChange())}),a.on("ExecCommand",d=>{wC(d.command)||c(d)}),a.on("ObjectResizeStart cut",()=>{s.beforeChange()}),a.on("SaveContent ObjectResized blur",c),a.on("dragend",c),a.on("keyup",d=>{const u=d.keyCode;if(d.isDefaultPrevented())return;const m=Jt.os.isMacOS()&&"Meta"===d.key;(u>=33&&u<=36||u>=37&&u<=40||45===u||d.ctrlKey||m)&&(c(),a.nodeChanged()),46!==u&&8!==u||a.nodeChanged(),l.get()&&s.typing&&!Ug(ud(a),s.data[0])&&(a.isDirty()||a.setDirty(!0),a.dispatch("TypingUndo"),l.set(!1),a.nodeChanged())}),a.on("keydown",d=>{const u=d.keyCode;if(!d.isDefaultPrevented()){if(!(u>=33&&u<=36||u>=37&&u<=40||45===u))return!(u<16||u>20)||224===u||91===u||s.typing||d.ctrlKey&&!d.altKey||d.metaKey?void((Jt.os.isMacOS()?d.metaKey:d.ctrlKey&&!d.altKey)&&s.beforeChange()):(s.beforeChange(),md(s,!0,i),s.add({},d),void l.set(!0));s.typing&&c(d)}}),a.on("mousedown",d=>{s.typing&&c(d)}),a.on("input",d=>{var u,m;d.inputType&&("insertReplacementText"===d.inputType||"insertText"===(u=d).inputType&&null===u.data||"insertFromPaste"===(m=d).inputType||"insertFromDrop"===m.inputType)&&c(d)}),a.on("AddUndo Undo Redo ClearUndos",d=>{d.isDefaultPrevented()||a.nodeChanged()})})(t,r,n),(a=t).addShortcut("meta+z","","Undo"),a.addShortcut("meta+y,meta+shift+z","","Redo"),r;var a},IR=[9,27,wt.HOME,wt.END,19,20,44,144,145,33,34,45,16,17,18,91,92,93,wt.DOWN,wt.UP,wt.LEFT,wt.RIGHT].concat(Jt.browser.isFirefox()?[224]:[]),SC="data-mce-placeholder",kC=t=>"keydown"===t.type||"keyup"===t.type,EC=t=>{const e=t.keyCode;return e===wt.BACKSPACE||e===wt.DELETE},FR=(t,e)=>({from:t,to:e}),qg=(t,e)=>{const n=A(t),o=A(e.container());return Zc(n,o).map(r=>((a,s)=>({block:a,position:s}))(r,e))},_C=(t,e)=>Lr(e,n=>Mi(n)||Po(n.dom),n=>re(n,t)).filter(fn).getOr(t),NC=t=>{const e=(n=>{const o=ze(n);return go(o,eo).fold(ut(o),r=>o.slice(0,r))})(t);return J(e,w),e},RC=(t,e)=>{const n=zr(e,t);return he(n.reverse(),o=>De(o)).each(w)},UR=(t,e,n,o)=>{if(De(n))return la(n),Cn(n.dom);0===Ot(ec(o),a=>!De(a)).length&&De(e)&&bn(o,Be("br"));const r=Dn(n.dom,$.before(o.dom));return J(NC(e),a=>{bn(o,a)}),RC(t,e),r},jR=(t,e,n)=>{if(De(n)){if(De(e)){const a=Kn((s=>{const i=(l,c)=>Ri(l).fold(()=>c,d=>mc(d)?i(d,c.concat(Xa(d))):c);return i(s,[])})(n),(s,i)=>(Ms(s,i),i),fc());x(e),Se(e,a)}return w(n),Cn(e.dom)}const o=Eo(n.dom);return J(NC(e),r=>{Se(n,r)}),RC(t,e),o},AC=(t,e)=>{Ws(t,e.dom).bind(n=>S.from(n.getNode())).map(A).filter(qa).each(w)},TC=(t,e,n)=>{return AC(!0,e),AC(!1,n),(o=e,r=n,vo(r,o)?((a,s)=>{const i=zr(s,a);return S.from(i[i.length-1])})(r,o):S.none()).fold(Ct(jR,t,e,n),Ct(UR,t,e,n));var o,r},OC=(t,e,n,o)=>e?TC(t,o,n):TC(t,n,o),Vg=(t,e)=>{const n=A(t.getBody());return(r=n.dom,a=e,s=t.selection.getRng(),s.collapsed?((i,l,c)=>{const d=qg(i,$.fromRangeStart(c)),u=d.bind(m=>oo(l,i,m.position).bind(p=>qg(i,p).map(g=>{return h=i,f=l,_e((b=g).position.getNode())&&!De(b.block)?Ws(!1,b.block.dom).bind(y=>y.isEqual(b.position)?oo(f,h,y).bind(C=>qg(h,C)):S.some(b)).getOr(b):b;var h,f,b})));return on(d,u,FR).filter(m=>{return!re((p=m).from.block,p.to.block)&&((p,g)=>{const h=A(p);return re(_C(h,g.from.block),_C(h,g.to.block))})(i,m)&&(p=>!1===ve(p.from.block.dom)&&!1===ve(p.to.block.dom))(m)&&(p=>{const g=h=>fp(h)||Su(h.dom);return g(p.from.block)&&g(p.to.block)})(m);var p})})(r,a,s):S.none()).map(r=>()=>{OC(n,e,r.from.block,r.to.block).each(a=>{t.selection.setRng(a.toRange())})});var r,a,s},BC=(t,e)=>{const n=A(e),o=Ct(re,t);return wo(n,Mi,o).isSome()},Wg=(t,e)=>t.selection.isCollapsed()?S.none():(t=>{const e=A(t.getBody());return((n,o)=>{const r=Dn(n.dom,$.fromRangeStart(o)).isNone(),a=yn(n.dom,$.fromRangeEnd(o)).isNone();return!(BC(s=n,(i=o).startContainer)||BC(s,i.endContainer))&&r&&a;var s,i})(e,t.selection.getRng())?(n=t,S.some(()=>{n.setContent(""),n.selection.setCursorLocation()})):((n,o)=>{const r=o.getRng();return on(Zc(n,A(r.startContainer)),Zc(n,A(r.endContainer)),(a,s)=>re(a,s)?S.none():S.some(()=>{r.deleteContents(),OC(n,!0,a,s).each(i=>{o.setRng(i.toRange())})})).getOr(S.none())})(e,t.selection);var n})(t),Hr=(t,e,n,o,r)=>S.from(e._selectionOverrides.showCaret(t,n,o,r)),wl=(t,e)=>t.dispatch("BeforeObjectSelected",{target:e}).isDefaultPrevented()?S.none():S.some((n=>{const o=n.ownerDocument.createRange();return o.selectNode(n),o})(e)),Kg=(t,e,n)=>e.collapsed?((o,r,a)=>{const s=Zu(1,o.getBody(),r),i=$.fromRangeStart(s),l=i.getNode();if(Qi(l))return Hr(1,o,l,!i.isAtEnd(),!1);const c=i.getNode(!0);if(Qi(c))return Hr(1,o,c,!1,!1);const d=ds(o.dom.getRoot(),i.getNode());return Qi(d)?Hr(1,o,d,!1,a):S.none()})(t,e,n).getOr(e):e,HR=t=>ya(t)||dl(t),$R=t=>Ca(t)||ul(t),DC=(t,e,n,o,r,a)=>{var s,i;Hr(o,t,a.getNode(!r),r,!0).each(s=>{if(e.collapsed){const i=e.cloneRange();r?i.setEnd(s.startContainer,s.startOffset):i.setStart(s.endContainer,s.endOffset),i.deleteContents()}else e.deleteContents();t.selection.setRng(s)}),s=t.dom,tt(i=n)&&0===i.data.length&&s.remove(i)},Gg=(t,e)=>((n,o)=>{const r=n.selection.getRng();if(!tt(r.commonAncestorContainer))return S.none();const a=o?Xe.Forwards:Xe.Backwards,s=sr(n.getBody()),i=Ct(tm,o?s.next:s.prev),l=o?HR:$R,c=nl(a,n.getBody(),r),d=i(c),u=d&&io(o,d);if(!u||!Pc(c,u))return S.none();if(l(u))return S.some(()=>DC(n,r,c.getNode(),a,o,u));const m=i(u);return m&&l(m)&&Pc(u,m)?S.some(()=>DC(n,r,c.getNode(),a,o,m)):S.none()})(t,e),Yg=(t,e)=>{const n=t.getBody();return e?Cn(n).filter(ya):Eo(n).filter(Ca)},Xg=t=>{const e=t.selection.getRng();return!e.collapsed&&(Yg(t,!0).exists(n=>n.isEqual($.fromRangeStart(e)))||Yg(t,!1).exists(n=>n.isEqual($.fromRangeEnd(e))))},co=or([{remove:["element"]},{moveToElement:["element"]},{moveToPosition:["position"]}]),PC=(t,e,n)=>oo(e,t,n).bind(o=>{return r=o.getNode(),st(r)&&(Mi(A(r))||Pi(A(r)))||((a,s,i,l)=>{const c=d=>mc(A(d))&&!rr(i,l,a);return nb(!s,i).fold(()=>nb(s,l).fold(de,c),c)})(t,e,n,o)?S.none():e&&ve(o.getNode())||!e&&ve(o.getNode(!0))?((a,s,i,l)=>{const c=l.getNode(!s);return Zc(A(a),A(i.getNode())).map(d=>De(d)?co.remove(d.dom):co.moveToElement(c)).orThunk(()=>S.some(co.moveToElement(c)))})(t,e,n,o):e&&Ca(n)||!e&&ya(n)?S.some(co.moveToPosition(o)):S.none();var r}),MC=(t,e)=>S.from(ds(t.getBody(),e)),LC=t=>{const e=t.dom,n=t.selection,o=ds(t.getBody(),n.getNode());if(Po(o)&&e.isBlock(o)&&e.isEmpty(o)){const r=e.create("br",{"data-mce-bogus":"1"});e.setHTML(o,""),o.appendChild(r),n.setRng($.before(r).toRange())}return!0},Qg=(t,e)=>t.selection.isCollapsed()?((t,e)=>{const n=t.selection.getNode();return MC(t,n).filter(ve).fold(()=>((o,r,a)=>{const s=Zu(r?1:-1,o,a),i=$.fromRangeStart(s),l=A(o);return!r&&Ca(i)?S.some(co.remove(i.getNode(!0))):r&&ya(i)?S.some(co.remove(i.getNode())):!r&&ya(i)&&Hm(l,i)?oN(l,i).map(c=>co.remove(c.getNode())):r&&Ca(i)&&zm(l,i)?rN(l,i).map(c=>co.remove(c.getNode())):(c=o,((m,p)=>{const g=p.getNode(!m),h=m?"after":"before";return it(g)&&g.getAttribute("data-mce-caret")===h})(d=r,u=i)?(m=d,p=u.getNode(!d),pe(p)?S.none():m&&ve(p.nextSibling)?S.some(co.moveToElement(p.nextSibling)):!m&&ve(p.previousSibling)?S.some(co.moveToElement(p.previousSibling)):S.none()).orThunk(()=>PC(c,d,u)):PC(c,d,u).bind(m=>((p,g,h)=>h.fold(f=>S.some(co.remove(f)),f=>S.some(co.moveToElement(f)),f=>rr(g,f,p)?S.none():S.some(co.moveToPosition(f))))(c,u,m)));var c,d,u,m,p})(t.getBody(),e,t.selection.getRng()).map(o=>()=>o.fold(((r,a)=>s=>(r._selectionOverrides.hideFakeCaret(),ls(r,a,A(s)),!0))(t,e),((r,a)=>s=>{const i=a?$.before(s):$.after(s);return r.selection.setRng(i.toRange()),!0})(t,e),(r=>a=>(r.selection.setRng(a.toRange()),!0))(t))),()=>S.some(jt))})(t,e):((n,o)=>{const r=n.selection.getNode();return ve(r)&&!dc(r)?MC(n,r.parentNode).filter(ve).fold(()=>S.some(()=>{var a;a=A(n.getBody()),J(Rt(a,".mce-offscreen-selection"),w),ls(n,o,A(n.selection.getNode())),Gm(n)}),()=>S.some(jt)):Xg(n)?S.some(()=>{d0(n,n.selection.getRng(),A(n.getBody()))}):S.none()})(t,e),Jg=(t,e)=>t.selection.isCollapsed()?((n,o)=>{const r=$.fromRangeStart(n.selection.getRng());return oo(o,n.getBody(),r).filter(a=>o?J_(a):Z_(a)).bind(a=>Ju(o?0:-1,a)).map(a=>()=>n.selection.select(a))})(t,e):S.none(),ri=tt,IC=t=>ri(t)&&t.data[0]===dn,FC=t=>ri(t)&&t.data[t.data.length-1]===dn,UC=t=>{var e;return(null!==(e=t.ownerDocument)&&void 0!==e?e:document).createTextNode(dn)},yd=(t,e)=>t?(n=>{var o;if(ri(n.previousSibling))return FC(n.previousSibling)||n.previousSibling.appendData(dn),n.previousSibling;if(ri(n))return IC(n)||n.insertData(0,dn),n;{const r=UC(n);return null===(o=n.parentNode)||void 0===o||o.insertBefore(r,n),r}})(e):(n=>{var o,r;if(ri(n.nextSibling))return IC(n.nextSibling)||n.nextSibling.insertData(0,dn),n.nextSibling;if(ri(n))return FC(n)||n.appendData(dn),n;{const a=UC(n);return n.nextSibling?null===(o=n.parentNode)||void 0===o||o.insertBefore(a,n.nextSibling):null===(r=n.parentNode)||void 0===r||r.appendChild(a),a}})(e),VR=Ct(yd,!0),WR=Ct(yd,!1),jC=(t,e)=>tt(t.container())?yd(e,t.container()):yd(e,t.getNode()),zC=(t,e)=>{const n=e.get();return n&&t.container()===n&&ca(n)},Zg=(t,e)=>e.fold(n=>{ts(t.get());const o=VR(n);return t.set(o),S.some($(o,o.length-1))},n=>Cn(n).map(o=>{if(zC(o,t)){const r=t.get();return $(r,1)}{ts(t.get());const r=jC(o,!0);return t.set(r),$(r,1)}}),n=>Eo(n).map(o=>{if(zC(o,t)){const r=t.get();return $(r,r.length-1)}{ts(t.get());const r=jC(o,!1);return t.set(r),$(r,r.length-1)}}),n=>{ts(t.get());const o=WR(n);return t.set(o),S.some($(o,1))}),HC=(t,e)=>{for(let n=0;n<t.length;n++){const o=t[n].apply(null,e);if(o.isSome())return o}return S.none()},wn=or([{before:["element"]},{start:["element"]},{end:["element"]},{after:["element"]}]),$C=(t,e)=>pa(e,t)||t,KR=(t,e,n)=>{const o=a0(n),r=$C(e,o.container());return wa(t,r,o).fold(()=>yn(r,o).bind(Ct(wa,t,r)).map(a=>wn.before(a)),S.none)},GR=(t,e)=>null===ns(t,e),qC=(t,e,n)=>wa(t,e,n).filter(Ct(GR,e)),YR=(t,e,n)=>{const o=s0(n);return qC(t,e,o).bind(r=>Dn(r,o).isNone()?S.some(wn.start(r)):S.none())},XR=(t,e,n)=>{const o=a0(n);return qC(t,e,o).bind(r=>yn(r,o).isNone()?S.some(wn.end(r)):S.none())},QR=(t,e,n)=>{const o=s0(n),r=$C(e,o.container());return wa(t,r,o).fold(()=>Dn(r,o).bind(Ct(wa,t,r)).map(a=>wn.after(a)),S.none)},VC=t=>!r0(tf(t)),ur=(t,e,n)=>HC([KR,YR,XR,QR],[t,e,n]).filter(VC),tf=t=>t.fold(qe,qe,qe,qe),WC=t=>t.fold(ut("before"),ut("start"),ut("end"),ut("after")),Cd=t=>t.fold(wn.before,wn.before,wn.after,wn.after),ef=t=>t.fold(wn.start,wn.start,wn.end,wn.end),JR=(t,e,n,o,r,a)=>on(wa(e,n,o),wa(e,n,r),(s,i)=>s!==i&&((l,c,d)=>{const u=pa(c,l),m=pa(d,l);return st(u)&&u===m})(n,s,i)?wn.after(t?s:i):a).getOr(a),ZR=(t,e)=>t.fold(ye,n=>{return r=e,!(WC(o=n)===WC(r)&&tf(o)===tf(r));var o,r}),tA=(t,e)=>t?e.fold(Fe(S.some,wn.start),S.none,Fe(S.some,wn.after),S.none):e.fold(S.none,Fe(S.some,wn.before),S.none,Fe(S.some,wn.end)),KC=(t,e,n)=>{const o=t?1:-1;return e.setRng($(n.container(),n.offset()+o).toRange()),e.getSel().modify("move",t?"forward":"backward","word"),!0};var mr;!function(t){t[t.Br=0]="Br",t[t.Block=1]="Block",t[t.Wrap=2]="Wrap",t[t.Eol=3]="Eol"}(mr||(mr={}));const wd=(t,e)=>t===Xe.Backwards?Ao(e):e,eA=(t,e,n)=>t===Xe.Forwards?e.next(n):e.prev(n),nA=(t,e,n,o)=>_e(o.getNode(e===Xe.Forwards))?mr.Br:!1===rr(n,o)?mr.Block:mr.Wrap,GC=(t,e,n,o)=>{const r=sr(n);let a=o;const s=[];for(;a;){const i=eA(e,r,a);if(!i)break;if(_e(i.getNode(!1)))return e===Xe.Forwards?{positions:wd(e,s).concat([i]),breakType:mr.Br,breakAt:S.some(i)}:{positions:wd(e,s),breakType:mr.Br,breakAt:S.some(i)};if(i.isVisible()){if(t(a,i)){const l=nA(0,e,a,i);return{positions:wd(e,s),breakType:l,breakAt:S.some(i)}}s.push(i),a=i}else a=i}return{positions:wd(e,s),breakType:mr.Eol,breakAt:S.none()}},YC=(t,e,n,o)=>e(n,o).breakAt.map(r=>{const a=e(n,r).positions;return t===Xe.Backwards?a.concat(r):[r].concat(a)}).getOr([]),nf=(t,e)=>Re(t,(n,o)=>n.fold(()=>S.some(o),r=>on(We(r.getClientRects()),We(o.getClientRects()),(a,s)=>{const i=Math.abs(e-a.left);return Math.abs(e-s.left)<=i?o:r}).or(n)),S.none()),xd=(t,e)=>We(e.getClientRects()).bind(n=>nf(t,n.left)),xl=Ct(GC,$.isAbove,-1),Sl=Ct(GC,$.isBelow,1),XC=Ct(YC,-1,xl),QC=Ct(YC,1,Sl),oA=(t,e)=>xd(XC(t,e),e),rA=(t,e)=>xd(QC(t,e),e),aA=ve,JC=(t,e)=>Math.abs(t.left-e),ZC=(t,e)=>Math.abs(t.right-e),t1=(t,e)=>Bo(t,(n,o)=>{const r=Math.min(JC(n,e),ZC(n,e)),a=Math.min(JC(o,e),ZC(o,e));return a===r&&Hn(o,"node")&&aA(o.node)||a<r?o:n}),sA=t=>{const e=n=>te(n,o=>{const r=Ja(o);return r.node=t,r});if(it(t))return e(t.getClientRects());if(tt(t)){const n=t.ownerDocument.createRange();return n.setStart(t,0),n.setEnd(t,t.data.length),e(n.getClientRects())}return[]},e1=t=>gn(t,sA);var kl;!function(t){t[t.Up=-1]="Up",t[t.Down=1]="Down"}(kl||(kl={}));const n1=(t,e,n,o,r,a)=>{let s=0;const i=[],l=u=>{let m=e1([u]);-1===t&&(m=m.reverse());for(let p=0;p<m.length;p++){const g=m[p];if(!n(g,c)){if(i.length>0&&e(g,Fn(i))&&s++,g.line=s,r(g))return!0;i.push(g)}}return!1},c=Fn(a.getClientRects());if(!c)return i;const d=a.getNode();return d&&(l(d),((u,m,p,g)=>{let h=g;for(;h=Zi(h,u,yp,m);)if(p(h))return})(t,o,l,d)),i},iA=Ct(n1,kl.Up,Vi,Wi),lA=Ct(n1,kl.Down,Wi,Vi),o1=t=>Fn(t.getClientRects()),r1=t=>e=>e.line>t,of=t=>e=>e.line===t,$r=(t,e)=>{t.selection.setRng(e),cl(t,t.selection.getRng())},rf=(t,e,n)=>S.some(Kg(t,e,n)),a1=(t,e,n,o,r,a)=>{const s=e===Xe.Forwards,i=sr(t.getBody()),l=Ct(tm,s?i.next:i.prev),c=s?o:r;if(!n.collapsed){const g=_c(n);if(a(g))return Hr(e,t,g,e===Xe.Backwards,!1);if(Xg(t)){const h=n.cloneRange();return h.collapse(e===Xe.Backwards),S.from(h)}}const d=nl(e,t.getBody(),n);if(c(d))return wl(t,d.getNode(!s));let u=l(d);const m=hp(n);if(!u)return m?S.some(n):S.none();if(u=io(s,u),c(u))return Hr(e,t,u.getNode(!s),s,!1);const p=l(u);return p&&c(p)&&Pc(u,p)?Hr(e,t,p.getNode(!s),s,!1):m?rf(t,u.toRange(),!1):S.none()},s1=(t,e,n,o,r,a)=>{const s=nl(e,t.getBody(),n),i=Fn(s.getClientRects()),l=e===kl.Down,c=t.getBody();if(!i)return S.none();if(Xg(t)){const h=l?$.fromRangeEnd(n):$.fromRangeStart(n);return(l?rA:oA)(c,h).orThunk(()=>S.from(h)).map(f=>f.toRange())}const d=(l?lA:iA)(c,r1(1),s),u=Ot(d,of(1)),m=i.left,p=t1(u,m);if(p&&a(p.node)){const h=Math.abs(m-p.left),f=Math.abs(m-p.right);return Hr(e,t,p.node,h<f,!1)}let g;if(g=o(s)?s.getNode():r(s)?s.getNode(!0):_c(n),g){const h=((b,y,C,k)=>{const E=sr(y);let N,R,I,q;const H=[];let T=0;1===b?(N=E.next,R=Wi,I=Vi,q=$.after(k)):(N=E.prev,R=Vi,I=Wi,q=$.before(k));const P=o1(q);do{if(!q.isVisible())continue;const B=o1(q);if(I(B,P))continue;H.length>0&&R(B,Fn(H))&&T++;const j=Ja(B);if(j.position=q,j.line=T,C(j))return H;H.push(j)}while(q=N(q));return H})(e,c,r1(1),g);let f=t1(Ot(h,of(1)),m);if(f||(f=Fn(Ot(h,of(0))),f))return rf(t,f.position.toRange(),!1)}return 0===u.length?af(t,l).filter(l?r:o).map(h=>Kg(t,h.toRange(),!1)):S.none()},af=(t,e)=>{const n=t.selection.getRng(),o=e?$.fromRangeEnd(n):$.fromRangeStart(n),r=(a=o.container(),s=t.getBody(),wo(A(a),i=>s_(i.dom),i=>i.dom===s).map(i=>i.dom).getOr(s));var a,s;if(e){const i=Sl(r,o);return Nn(i.positions)}{const i=xl(r,o);return We(i.positions)}},i1=(t,e,n)=>af(t,e).filter(n).exists(o=>(t.selection.setRng(o.toRange()),!0)),Sd=(t,e)=>{const n=t.dom.createRng();n.setStart(e.container(),e.offset()),n.setEnd(e.container(),e.offset()),t.selection.setRng(n)},l1=(t,e)=>{t?e.setAttribute("data-mce-selected","inline-boundary"):e.removeAttribute("data-mce-selected")},c1=(t,e,n)=>Zg(e,n).map(o=>(Sd(t,o),n)),d1=(t,e,n)=>!!Xi(t)&&((t,e,n)=>{const o=t.getBody(),r=((a,s,i)=>{const l=$.fromRangeStart(a);if(a.collapsed)return l;{const c=$.fromRangeEnd(a);return i?Dn(s,c).getOr(c):yn(s,l).getOr(l)}})(t.selection.getRng(),o,n);return((a,s,i,l)=>{const c=io(a,l),d=ur(s,i,c);return ur(s,i,c).bind(Ct(tA,a)).orThunk(()=>((u,m,p,g,h)=>{const f=io(u,h);return oo(u,p,f).map(Ct(io,u)).fold(()=>g.map(Cd),b=>ur(m,p,b).map(Ct(JR,u,m,p,f,b)).filter(Ct(ZR,g))).filter(VC)})(a,s,i,d,l))})(n,Ct(cs,t),o,r).bind(a=>c1(t,e,a))})(t,e,n).isSome(),u1=(t,e,n)=>!!Xi(e)&&((o,r)=>{const a=r.selection.getRng(),s=o?$.fromRangeEnd(a):$.fromRangeStart(a);return!!Yt(r.selection.getSel().modify)&&(o&&bu(s)?KC(!0,r.selection,s):!(o||!vu(s))&&KC(!1,r.selection,s))})(t,e),uA=Ct(u1,!0),mA=Ct(u1,!1),kd=(t,e,n)=>{if(Xi(t)){const o=af(t,e).getOrThunk(()=>{const r=t.selection.getRng();return e?$.fromRangeEnd(r):$.fromRangeStart(r)});return ur(Ct(cs,t),t.getBody(),o).exists(r=>{const a=Cd(r);return Zg(n,a).exists(s=>(Sd(t,s),!0))})}return!1},m1=(t,e)=>n=>Zg(e,n).map(o=>()=>Sd(t,o)),g1=(t,e,n,o)=>{const r=t.getBody(),a=Ct(cs,t);t.undoManager.ignore(()=>{t.selection.setRng(((s,i)=>{const l=document.createRange();return l.setStart(s.container(),s.offset()),l.setEnd(i.container(),i.offset()),l})(n,o)),Km(t),ur(a,r,$.fromRangeStart(t.selection.getRng())).map(ef).bind(m1(t,e)).each(En)}),t.nodeChanged()},sf=(t,e,n)=>{if(t.selection.isCollapsed()&&Xi(t)){const o=$.fromRangeStart(t.selection.getRng());return((r,a,s,i)=>{const l=(u=r.getBody(),m=i.container(),pa(m,u)||u),c=Ct(cs,r),d=ur(c,l,i);var u,m;return d.bind(u=>s?u.fold(ut(S.some(ef(u))),S.none,ut(S.some(Cd(u))),S.none):u.fold(S.none,ut(S.some(Cd(u))),S.none,ut(S.some(ef(u))))).map(m1(r,a)).getOrThunk(()=>{const u=ol(s,l,i),m=u.bind(p=>ur(c,l,p));return on(d,m,()=>wa(c,l,i).bind(p=>{return on(Cn(g=p),Eo(g),(h,f)=>{const b=io(!0,h),y=io(!1,f);return yn(g,b).forall(C=>C.isEqual(y))}).getOr(!0)?S.some(()=>{ls(r,s,A(p))}):S.none();var g})).getOrThunk(()=>m.bind(()=>u.map(p=>()=>{s?g1(r,a,i,p):g1(r,a,p,i)})))})})(t,e,n,o)}return S.none()},f1=(t,e)=>{const n=A(t.getBody()),o=A(t.selection.getStart()),r=zr(o,n);return go(r,e).fold(ut(r),a=>r.slice(0,a))},gA=t=>1===oa(t),p1=(t,e)=>{const n=Ct(L0,t);return gn(e,o=>n(o)?[o.dom]:[])},h1=t=>{const e=f1(t,eo);return p1(t,e)},b1=(t,e)=>{const n=t.selection.getStart(),o=((r,a)=>{const s=a.parentElement;return _e(a)&&!Oe(s)&&r.dom.isEmpty(s)})(t,n)||I0(A(n))?M0(n,e):((r,a)=>{const{caretContainer:s,caretPosition:i}=P0(a);return r.insertNode(s.dom),i})(t.selection.getRng(),e);t.selection.setRng(o.toRange())},v1=t=>tt(t.startContainer),lf=(t,e)=>t.selection.isCollapsed()?((t,e)=>{const n=Ot(f1(t,r=>eo(r)||oa(r)>1),gA);return Nn(n).bind(o=>{const r=$.fromRangeStart(t.selection.getRng());return l0(e,r,o.dom)&&!I0(o)?S.some(()=>((a,s,i,l)=>{const c=p1(s,l);if(0===c.length)ls(s,a,i);else{const d=M0(i.dom,c);s.selection.setRng(d.toRange())}})(e,t,o,n)):S.none()})})(t,e):(n=>{if((t=>{const e=t.selection.getRng();return 0===(n=e).startOffset&&v1(n)&&((n,o)=>{const r=o.startContainer.parentElement;return!Oe(r)&&L0(n,A(r))})(t,e)&&(n=>{return(r=>{const a=r.startContainer.parentNode,s=r.endContainer.parentNode;return!Oe(a)&&!Oe(s)&&a.isEqualNode(s)})(o=n)&&(r=>{const a=r.endContainer;return r.endOffset===(tt(a)?a.length:a.childNodes.length)})(o)||(o=>!o.endContainer.isEqualNode(o.commonAncestorContainer))(n);var o})(e);var n})(n)){const o=h1(n);return S.some(()=>{Km(n),((r,a)=>{const s=Gn(a,h1(r));s.length>0&&b1(r,s)})(n,o)})}return S.none()})(t),El=t=>((e=>{const n=e.selection.getRng();return n.collapsed&&(v1(n)||e.dom.isEmpty(n.startContainer))&&!(t=>wo(t,r=>_o(r.dom),eo).isSome())(A(e.selection.getStart()))})(t)&&b1(t,[]),!0),cf=(t,e,n)=>st(n)?S.some(()=>{t._selectionOverrides.hideFakeCaret(),ls(t,e,A(n))}):S.none(),df=(t,e)=>t.selection.isCollapsed()?((n,o)=>{const r=o?dl:ul,s=nl(o?Xe.Forwards:Xe.Backwards,n.getBody(),n.selection.getRng());return r(s)?cf(n,o,s.getNode(!o)):S.from(io(o,s)).filter(i=>r(i)&&Pc(s,i)).bind(i=>cf(n,o,i.getNode(!o)))})(t,e):((n,o)=>{const r=n.selection.getNode();return nr(r)?cf(n,o,r):S.none()})(t,e),uf=t=>we(t??"").getOr(0),y1=(t,e)=>(t||"table"===ge(e)?"margin":"padding")+("rtl"===xo(e,"direction")?"-right":"-left"),C1=t=>{const e=x1(t);return!t.mode.isReadOnly()&&(e.length>1||(n=t,_n(e,r=>{const a=y1(Mh(n),r),s=za(r,a).map(uf).getOr(0);return"false"!==n.dom.getContentEditable(r.dom)&&s>0})));var n},w1=t=>hu(t)||Pi(t),x1=t=>Ot(M(t.selection.getSelectedBlocks()),e=>!w1(e)&&!Tn(e).exists(w1)&&Lr(e,n=>Po(n.dom)||ve(n.dom)).exists(n=>Po(n.dom))),S1=(t,e)=>{var n,o;const{dom:r}=t,a=tE(t),s=null!==(o=null===(n=/[a-z%]+$/i.exec(a))||void 0===n?void 0:n[0])&&void 0!==o?o:"px",i=uf(a),l=Mh(t);J(x1(t),c=>{((d,u,m,p,g,h)=>{const f=y1(m,A(h)),b=uf(d.getStyle(h,f));if("outdent"===u){const y=Math.max(0,b-p);d.setStyle(h,f,y?y+g:"")}else d.setStyle(h,f,b+p+g)})(r,e,l,i,s,c.dom)})},k1=t=>S1(t,"outdent"),E1=t=>{if(t.selection.isCollapsed()&&C1(t)){const e=t.dom,n=t.selection.getRng(),o=$.fromRangeStart(n),r=e.getParent(n.startContainer,e.isBlock);if(null!==r&&jm(A(r),o))return S.some(()=>k1(t))}return S.none()},_1=(t,e,n)=>Ko([E1,Qg,Gg,(o,r)=>sf(o,e,r),Vg,ng,Jg,df,Wg,lf],o=>o(t,n)).filter(o=>t.selection.isEditable()),N1=t=>void 0===t.touches||1!==t.touches.length?S.none():S.some(t.touches[0]),mf=(t,e)=>It(t,e.nodeName),vA=(t,e)=>!!tt(e)||!!it(e)&&!mf(t.getBlockElements(),e)&&!ro(e)&&!Wa(t,e),yA=(t,e)=>!(!tt(e)||0!==e.data.length&&(!/^\s+$/.test(e.data)||e.nextSibling&&!mf(t,e.nextSibling))),R1=t=>t.dom.create(Bn(t),Yi(t)),CA=t=>{const e=t.dom,n=t.selection,o=t.schema,r=o.getBlockElements(),a=n.getStart(),s=t.getBody();let i,l,c=!1;const d=Bn(t);if(!a||!it(a))return;const u=s.nodeName.toLowerCase();if(!o.isValidChild(u,d.toLowerCase())||(C=r,k=s,ie(Uv(A(a),A(k)),N=>mf(C,N.dom))))return;var C,k;const m=n.getRng(),{startContainer:p,startOffset:g,endContainer:h,endOffset:f}=m,b=is(t);let y=s.firstChild;for(;y;)if(it(y)&&dk(o,y),vA(o,y)){if(yA(r,y)){l=y,y=y.nextSibling,e.remove(l);continue}i||(i=R1(t),s.insertBefore(i,y),c=!0),l=y,y=y.nextSibling,i.appendChild(l)}else i=null,y=y.nextSibling;c&&b&&(m.setStart(p,g),m.setEnd(h,f),n.setRng(m),t.nodeChanged())},A1=(t,e,n)=>{const o=A(R1(t)),r=fc();Se(o,r),n(e,o);const a=document.createRange();return a.setStartBefore(r.dom),a.setEndBefore(r.dom),a},T1=t=>e=>-1!==(" "+e.attr("class")+" ").indexOf(t),wA=(t,e,n)=>function(o){const r=arguments,a=r[r.length-2],s=a>0?e.charAt(a-1):"";if('"'===s)return o;if(">"===s){const i=e.lastIndexOf("<",a);if(-1!==i&&-1!==e.substring(i,a).indexOf('contenteditable="false"'))return o}return'<span class="'+n+'" data-mce-content="'+t.dom.encode(r[0])+'">'+t.dom.encode("string"==typeof r[1]?r[1]:r[0])+"</span>"},O1=(t,e)=>{e.hasAttribute("data-mce-caret")&&(yu(e),t.selection.setRng(t.selection.getRng()),t.selection.scrollIntoView(e))},xA=(t,e)=>{const n=Ua(A(t.getBody()),"*[data-mce-caret]").map(r=>r.dom).getOrNull();if(n)return"compositionstart"===e.type?(e.preventDefault(),e.stopPropagation(),void O1(t,n)):void(pp(n)&&(O1(t,n),t.undoManager.add()))},B1=ve,D1=(t,e,n)=>{const o=sr(t.getBody()),r=Ct(tm,1===e?o.next:o.prev);if(n.collapsed){const a=t.dom.getParent(n.startContainer,"PRE");if(!a)return;if(!r($.fromRangeStart(n))){const s=A((i=>{const l=i.dom.create(Bn(i));return l.innerHTML='<br data-mce-bogus="1">',l})(t));1===e?to(A(a),s):bn(A(a),s),t.selection.select(s.dom,!0),t.selection.collapse()}}},P1=(t,e)=>((n,o)=>{const r=o?Xe.Forwards:Xe.Backwards,a=n.selection.getRng();return(s=r,i=n,l=a,a1(i,s,l,ya,Ca,B1)).orThunk(()=>(D1(n,r,a),S.none()));var s,i,l})(t,((n,o)=>{const r=o?n.getEnd(!0):n.getStart(!0);return r0(r)?!o:o})(t.selection,e)).exists(n=>($r(t,n),!0)),M1=(t,e)=>((n,o)=>{const r=o?1:-1,a=n.selection.getRng();return(s=r,i=n,l=a,s1(i,s,l,c=>ya(c)||Iv(c),c=>Ca(c)||Fv(c),B1)).orThunk(()=>(D1(n,r,a),S.none()));var s,i,l})(t,e).exists(n=>($r(t,n),!0)),L1=(t,e)=>i1(t,e,e?Ca:ya),Ed=(t,e)=>Yg(t,!e).map(n=>{const o=n.toRange(),r=t.selection.getRng();return e?o.setStart(r.startContainer,r.startOffset):o.setEnd(r.endContainer,r.endOffset),o}).exists(n=>($r(t,n),!0)),SA=t=>Lt(["figcaption"],ge(t)),I1=(t,e)=>!!t.selection.isCollapsed()&&((t,e)=>{const n=A(t.getBody()),o=$.fromRangeStart(t.selection.getRng());return((r,a)=>{const s=Ct(re,a);return Lr(A(r.container()),eo,s).filter(SA)})(o,n).exists(()=>{if(r=n,s=o,e?Sl(r.dom,s).breakAt.isNone():xl(r.dom,s).breakAt.isNone()){const r=A1(t,n,e?Se:Ha);return t.selection.setRng(r),!0}var r,s;return!1})})(t,e),F1={shiftKey:!1,altKey:!1,ctrlKey:!1,metaKey:!1,keyCode:0},U1=(t,e)=>e.keyCode===t.keyCode&&e.shiftKey===t.shiftKey&&e.altKey===t.altKey&&e.ctrlKey===t.ctrlKey&&e.metaKey===t.metaKey,qt=(t,...e)=>()=>t.apply(null,e),_l=(t,e)=>{return he((o=e,gn(te(t,a=>({...F1,...a})),r=>U1(r,o)?[r]:[])),n=>n.action());var o},j1=(t,e)=>{return Ko((o=e,gn(te(t,a=>({...F1,...a})),r=>U1(r,o)?[r]:[])),n=>n.action());var o},z1=(t,e)=>{const n=e?Xe.Forwards:Xe.Backwards,o=t.selection.getRng();return a1(t,n,o,dl,ul,nr).exists(r=>($r(t,r),!0))},H1=(t,e)=>{const n=e?1:-1,o=t.selection.getRng();return s1(t,n,o,dl,ul,nr).exists(r=>($r(t,r),!0))},$1=(t,e)=>i1(t,e,e?ul:dl),q1=or([{none:["current"]},{first:["current"]},{middle:["current","target"]},{last:["current"]}]),Nl={...q1,none:t=>q1.none(t)},V1=(t,e,n)=>gn(ze(t),o=>hn(o,e)?n(o)?[o]:[]:V1(o,e,n)),W1=(t,e)=>er(t,"table",e),gf=(t,e,n,o,r=ye)=>{const a=1===o;if(!a&&n<=0)return Nl.first(t[0]);if(a&&n>=t.length-1)return Nl.last(t[t.length-1]);{const s=n+o,i=t[s];return r(i)?Nl.middle(e,i):gf(t,e,s,o,r)}},K1=(t,e)=>W1(t,e).bind(n=>{const o=V1(n,"th,td",ye);return go(o,r=>re(t,r)).map(r=>({index:r,all:o}))}),G1=(t,e,n,o,r)=>{const a=Rt(A(n),"td,th,caption").map(i=>i.dom);var i;return((i,l,c)=>Re(i,(d,u)=>d.fold(()=>S.some(u),m=>{const p=Math.sqrt(Math.abs(m.x-l)+Math.abs(m.y-c)),g=Math.sqrt(Math.abs(u.x-l)+Math.abs(u.y-c));return S.some(g<p?u:m)}),S.none()))(Ot((i=t,gn(a,c=>{const d={left:(u=Ja(c.getBoundingClientRect())).left- -1,top:u.top- -1,right:u.right+-2,bottom:u.bottom+-2,width:u.width+-1,height:u.height+-1};var u;return[{x:d.left,y:i(d),cell:c},{x:d.right,y:i(d),cell:c}]})),i=>e(i,r)),o,r).map(i=>i.cell)},EA=Ct(G1,t=>t.bottom,(t,e)=>t.y<e),_A=Ct(G1,t=>t.top,(t,e)=>t.y>e),Y1=(t,e,n)=>{const o=t(e,n);return(r=o).breakType===mr.Wrap&&0===r.positions.length||!_e(n.getNode())&&(r=>r.breakType===mr.Br&&1===r.positions.length)(o)?!((r,a,s)=>s.breakAt.exists(i=>r(a,i).breakAt.isSome()))(t,e,o):o.breakAt.isNone();var r},NA=Ct(Y1,xl),RA=Ct(Y1,Sl),AA=(t,e,n,o)=>{const r=t.selection.getRng(),a=e?1:-1;return!(!Xh()||!((s,i,l)=>{const c=$.fromRangeStart(i);return Ws(!s,l).exists(d=>d.isEqual(c))})(e,r,n)||(Hr(a,t,n,!e,!1).each(s=>{$r(t,s)}),0))},X1=(t,e,n)=>{const o=((a,s)=>{const i=s.getNode(a);return Ir(i)?S.some(i):S.none()})(!!e,n),r=!1===e;o.fold(()=>$r(t,n.toRange()),a=>Ws(r,t.getBody()).filter(s=>s.isEqual(n)).fold(()=>$r(t,n.toRange()),s=>{return i=e,c=a,void(l=t).undoManager.transact(()=>{const d=i?to:bn,u=A1(l,A(c),d);$r(l,u)});var i,l,c}))},TA=(t,e,n,o)=>{const r=t.selection.getRng(),a=$.fromRangeStart(r),s=t.getBody();if(!e&&NA(o,a)){const i=(l=s,(u=c=n,m=d=a,We(m.getClientRects()).bind(p=>EA(u,p.left,p.top)).bind(p=>{return xd(Eo(g=p).map(h=>xl(g,h).positions.concat(h)).getOr([]),m);var g})).orThunk(()=>We(d.getClientRects()).bind(u=>nf(XC(l,$.before(c)),u.left))).getOr($.before(c)));return X1(t,e,i),!0}var l,c,d,u,m;if(e&&RA(o,a)){const i=((l,c,d)=>((u,m)=>Nn(m.getClientRects()).bind(p=>_A(u,p.left,p.top)).bind(p=>{return xd(Cn(g=p).map(h=>[h].concat(Sl(g,h).positions)).getOr([]),m);var g}))(c,d).orThunk(()=>We(d.getClientRects()).bind(u=>nf(QC(l,$.after(c)),u.left))).getOr($.after(c)))(s,n,a);return X1(t,e,i),!0}return!1},Q1=(t,e,n)=>S.from(t.dom.getParent(t.selection.getNode(),"td,th")).bind(o=>S.from(t.dom.getParent(o,"table")).map(r=>n(t,e,r,o))).getOr(!1),J1=(t,e)=>Q1(t,e,AA),Z1=(t,e)=>Q1(t,e,TA),tw=(t,e,n)=>n.fold(S.none,S.none,(o,r)=>{return(a=r,((s,i)=>{const l=c=>{for(let d=0;d<c.childNodes.length;d++){const u=A(c.childNodes[d]);if(i(u))return S.some(u);const m=l(c.childNodes[d]);if(m.isSome())return m}return S.none()};return l(s.dom)})(a,xv)).map(s=>{return(t=>{const e=Qs.getWin(t).dom,n=(r,a,s,i)=>Cm(e,r,a,s,i),o=t.match({domRange:a=>{const s=A(a.startContainer),i=A(a.endContainer);return Qb(s,a.startOffset,i,a.endOffset)},relative:__,exact:Qb});return((r,a)=>{var i;return((i,l)=>{const c=l.ltr();return c.collapsed?l.rtl().filter(d=>!1===d.collapsed).map(d=>ll.rtl(A(d.endContainer),d.endOffset,A(d.startContainer),d.startOffset)).getOrThunk(()=>Yb(0,ll.ltr,c)):Yb(0,ll.ltr,c)})(0,(i=r,a.match({domRange:c=>({ltr:ut(c),rtl:S.none}),relative:(c,d)=>({ltr:fo(()=>Gb(i,c,d)),rtl:fo(()=>S.some(Gb(i,d,c)))}),exact:(c,d,u,m)=>({ltr:fo(()=>Cm(i,c,d,u,m)),rtl:fo(()=>S.some(Cm(i,u,m,c,d)))})})))})(e,o).match({ltr:n,rtl:n})})(Qs.exact(i=s,0,i,0));var i});var a},o=>(t.execCommand("mceTableInsertRowAfter"),ew(t,e,o))),ew=(t,e,n)=>{return tw(t,e,(r=aa,K1(o=n,void 0).fold(()=>Nl.none(o),a=>gf(a.all,o,a.index,1,r))));var o,r},OA=(t,e,n)=>{return tw(t,e,(r=aa,K1(o=n,void 0).fold(()=>Nl.none(),a=>gf(a.all,o,a.index,-1,r))));var o,r},nw=(t,e)=>{const n=["table","li","dl"],o=A(t.getBody()),r=s=>{const i=ge(s);return re(s,o)||Lt(n,i)},a=t.selection.getRng();return(s=A(e?a.endContainer:a.startContainer),i=r,((l,c,d=de)=>d(c)?S.none():Lt(l,ge(c))?S.some(c):Fa(c,l.join(","),u=>hn(u,"table")||d(u)))(["td","th"],s,i)).map(s=>(W1(s,r).each(i=>{t.model.table.clearSelectedCells(i.dom)}),t.selection.collapse(!e),(e?ew:OA)(t,r,s).each(i=>{t.selection.setRng(i)}),!0)).getOr(!1);var s,i},jo=(t,e)=>({container:t,offset:e}),_d=me.DOM,Nd=t=>e=>t===e?-1:0,ff=(t,e,n)=>{if(tt(t)&&e>=0)return S.some(jo(t,e));{const o=Qa(_d);return S.from(o.backwards(t,e,Nd(t),n)).map(r=>jo(r.container,r.container.data.length))}},pf=(t,e,n)=>{if(!tt(t))return S.none();if(e>=0&&e<=t.data.length)return S.some(jo(t,e));{const r=Qa(_d);return S.from(r.backwards(t,e,Nd(t),n)).bind(a=>pf(a.container,e+a.container.data.length,n))}},ow=(t,e,n)=>{if(!tt(t))return S.none();const o=t.data;if(e<=o.length)return S.some(jo(t,e));{const r=Qa(_d);return S.from(r.forwards(t,e,Nd(t),n)).bind(a=>ow(a.container,e-o.length,n))}},hf=(t,e,n,o,r)=>{const a=Qa(t,(s=>i=>s.isBlock(i)||Lt(["BR","IMG","HR","INPUT"],i.nodeName)||"false"===s.getContentEditable(i))(t));return S.from(a.backwards(e,n,o,r))},rw=t=>Fr(t.toString().replace(/\u00A0/g," ")),aw=t=>""!==t&&-1!==" \xa0\f\n\r\t\v".indexOf(t),bf=(t,e)=>t.substring(e.length),sw=(t,e,n,o=0)=>{return(r=A(e.startContainer),er(r,Sv)).fold(()=>((a,s,i,l=0)=>{if(!(c=s).collapsed||!tt(c.startContainer))return S.none();var c;const d={text:"",offset:0},u=a.getParent(s.startContainer,a.isBlock)||a.getRoot();return hf(a,s.startContainer,s.startOffset,(m,p,g)=>(d.text=g+d.text,d.offset+=p,((h,f,b)=>{let y;const C=b.charAt(0);for(y=f-1;y>=0;y--){const k=h.charAt(y);if(aw(k))return S.none();if(C===k&&ct(h,b,y,f))break}return S.some(y)})(d.text,d.offset,i).getOr(p)),u).bind(m=>{const p=s.cloneRange();if(p.setStart(m.container,m.offset),p.setEnd(s.endContainer,s.endOffset),p.collapsed)return S.none();const g=rw(p);return 0!==g.lastIndexOf(i)||bf(g,i).length<l?S.none():S.some({text:bf(g,i),range:p,trigger:i})})})(t,e,n,o),a=>{const s=t.createRng();s.selectNode(a.dom);const i=rw(s);return S.some({range:s,text:bf(i,n),trigger:n})});var r},iw=t=>{if(3===t.nodeType)return jo(t,t.data.length);{const e=t.childNodes;return e.length>0?iw(e[e.length-1]):jo(t,e.length)}},lw=(t,e)=>{const n=t.childNodes;return n.length>0&&e<n.length?lw(n[e],0):n.length>0&&1===t.nodeType&&n.length===e?iw(n[n.length-1]):jo(t,e)},cw=(t,e,n,o={})=>{var r;const a=e(),s=null!==(r=t.selection.getRng().startContainer.nodeValue)&&void 0!==r?r:"",i=Ot(a.lookupByTrigger(n.trigger),c=>n.text.length>=c.minChars&&c.matches.getOrThunk(()=>(d=>u=>{const m=lw(u.startContainer,u.startOffset);return!((p,g)=>{var h;const f=null!==(h=p.getParent(g.container,p.isBlock))&&void 0!==h?h:p.getRoot();return hf(p,g.container,g.offset,(b,y)=>0===y?-1:y,f).filter(b=>{const y=b.container.data.charAt(b.offset-1);return!aw(y)}).isSome()})(d,m)})(t.dom))(n.range,s,n.text));if(0===i.length)return S.none();const l=Promise.all(te(i,c=>c.fetch(n.text,c.maxResults,o).then(d=>({matchText:n.text,items:d,columns:c.columns,onAction:c.onAction,highlightOn:c.highlightOn}))));return S.some({lookupData:l,context:n})};var gr;!function(t){t[t.Error=0]="Error",t[t.Value=1]="Value"}(gr||(gr={}));const vf=(t,e,n)=>t.stype===gr.Error?e(t.serror):n(t.svalue),ai=t=>({stype:gr.Value,svalue:t}),Rl=t=>({stype:gr.Error,serror:t}),BA=vf,yf=t=>At(t)&&tn(t).length>100?" removed due to size":JSON.stringify(t,null,2),Rd=(t,e)=>Rl([{path:t,getErrorInfo:e}]),dw=t=>(...e)=>{if(0===e.length)throw new Error("Can't merge zero objects");const n={};for(let o=0;o<e.length;o++){const r=e[o];for(const a in r)It(r,a)&&(n[a]=t(n[a],r[a]))}return n},Cf=dw((t,e)=>ln(t)&&ln(e)?Cf(t,e):e),uw=(dw((t,e)=>e),t=>({tag:"defaultedThunk",process:ut(t)})),mw=(t,e,n)=>{switch(t.tag){case"field":return e(t.key,t.newKey,t.presence,t.prop);case"custom":return n(t.newKey,t.instantiator)}},wf=t=>({extract:(e,n)=>{return(o=t(n)).stype===gr.Error?Rd(e,ut(o.serror)):o;var o},toString:ut("val")}),MA=wf(ai),gw=(t,e,n,o)=>o(ce(t,e).getOrThunk(()=>n(t))),LA=(t,e,n,o,r)=>{const a=i=>r.extract(e.concat([o]),i),s=i=>i.fold(()=>ai(S.none()),l=>{return(d=r.extract(e.concat([o]),l)).stype===gr.Value?{stype:gr.Value,svalue:(0,S.some)(d.svalue)}:d;var d});switch(t.tag){case"required":return i=e,d=a,ce(l=n,c=o).fold(()=>{return m=c,p=l,Rd(i,()=>'Could not find valid *required* value for "'+m+'" in '+yf(p));var m,p},d);case"defaultedThunk":return gw(n,o,t.process,a);case"option":return((i,l,c)=>c(ce(i,l)))(n,o,s);case"defaultedOptionThunk":return((i,l,c,d)=>d(ce(i,l).map(u=>!0===u?c(i):u)))(n,o,t.process,s);case"mergeWithThunk":return gw(n,o,ut({}),i=>{const l=Cf(t.process(n),i);return a(l)})}var i,l,c,d},xf=t=>({extract:(e,n)=>((o,r,a)=>{const s={},i=[];for(const l of a)mw(l,(c,d,u,m)=>{const p=LA(u,o,r,c,m);BA(p,g=>{i.push(...g)},g=>{s[d]=g})},(c,d)=>{s[c]=d(r)});return i.length>0?Rl(i):ai(s)})(e,n,t),toString:()=>"obj{\n"+te(t,n=>mw(n,(o,r,a,s)=>o+" -> "+s.toString(),(o,r)=>"state("+o+")")).join("\n")+"}"}),fw=t=>({extract:(e,n)=>(t=>{const e=(o=>{const r=[],a=[];return J(o,s=>{vf(s,i=>a.push(i),i=>r.push(i))}),{values:r,errors:a}})(t);return e.errors.length>0?(n=e.errors,Fe(Rl,Cr)(n)):ai(e.values);var n})(te(n,(r,a)=>t.extract(e.concat(["["+a+"]"]),r))),toString:()=>"array("+t.toString()+")"}),pw=(t,e)=>((t,e)=>({extract:(n,o)=>ce(o,t).fold(()=>{return a=t,Rd(n,()=>'Choice schema did not contain choice key: "'+a+'"');var a},r=>{return a=n,s=o,ce(i=e,l=r).fold(()=>{return d=i,u=l,Rd(a,()=>'The chosen schema: "'+u+'" did not exist in branches: '+yf(d));var d,u},c=>c.extract(a.concat(["branch: "+l]),s));var a,s,i,l}),toString:()=>"chooseOn("+t+"). Possible values: "+tn(e)}))(t,Yr(e,xf)),FA=ut(MA),Ad=(t,e)=>wf(n=>{const o=typeof n;return t(n)?ai(n):Rl(`Expected type: ${e} but got: ${o}`)}),UA=Ad(nn,"number"),Td=Ad(Nt,"string"),jA=Ad(xn,"boolean"),Sf=Ad(Yt,"function"),Al=(t,e,n,o)=>({tag:"field",key:t,newKey:e,presence:n,prop:o}),hw=(t,e)=>({tag:"custom",newKey:t,instantiator:e}),bw=(t,e)=>Al(t,t,{tag:"required",process:{}},e),vw=t=>bw(t,Td),yw=t=>bw(t,Sf),kf=(t,e)=>Al(t,t,{tag:"option",process:{}},e),Od=t=>kf(t,Td),si=(t,e,n)=>Al(t,t,uw(e),n),Cw=(t,e)=>si(t,e,UA),ww=(t,e,n)=>{return si(t,e,(o=n,r=a=>Lt(o,a)?On.value(a):On.error(`Unsupported value: "${a}", choose one of "${o.join(", ")}".`),wf(a=>r(a).fold(Rl,ai))));var o,r},Ef=(t,e)=>si(t,e,jA),_f=(t,e)=>si(t,e,Sf),zA=vw("type"),HA=yw("fetch"),Nf=yw("onAction"),$A=_f("onSetup",()=>jt),qA=Od("text"),VA=Od("icon"),WA=Od("tooltip"),KA=Od("label"),GA=Ef("active",!1),YA=Ef("enabled",!0),xw=Ef("primary",!1),Tl=t=>si("type",t,Td),XA=xf([zA,vw("trigger"),Cw("minChars",1),(t=>Al(t,t,uw(1),FA()))("columns"),Cw("maxResults",10),kf("matches",Sf),HA,Nf,(Sw=Td,si("highlightOn",[],fw(Sw)))]);var Sw;const Rf=[YA,WA,VA,qA,$A],kw=[GA].concat(Rf),QA=[_f("predicate",de),ww("scope","node",["node","editor"]),ww("position","selection",["node","selection","line"])],JA=Rf.concat([Tl("contextformbutton"),xw,Nf,hw("original",qe)]),ZA=kw.concat([Tl("contextformbutton"),xw,Nf,hw("original",qe)]),tT=Rf.concat([Tl("contextformbutton")]),eT=kw.concat([Tl("contextformtogglebutton")]),nT=pw("type",{contextformbutton:JA,contextformtogglebutton:ZA});xf([Tl("contextform"),_f("initValue",ut("")),KA,((t,e)=>Al(t,t,{tag:"required",process:{}},fw(e)))("commands",nT),kf("launch",pw("type",{contextformbutton:tT,contextformtogglebutton:eT}))].concat(QA));const rT=t=>{const e=ma(),n=Ye(!1),o=e.isSet,r=()=>{o()&&(Te(t).autocompleter.removeDecoration(),t.dispatch("AutocompleterEnd"),n.set(!1),e.clear())},a=fo(()=>(t=>{const e=t.ui.registry.getAll().popups,n=Yr(e,a=>{return(s=a,((t,e,n)=>{return o=(i=e.extract([t],s=n)).stype===gr.Error?{stype:gr.Error,serror:(i=>({input:s,errors:i}))(i.serror)}:i,vf(o,On.error,On.value);var s,i,o})("Autocompleter",XA,{trigger:s.ch,...s})).fold(i=>{throw new Error("Errors: \n"+(c=>{const d=c.length>10?c.slice(0,10).concat([{path:[],getErrorInfo:ut("... (only showing first ten failures)")}]):c;return te(d,u=>"Failed path: ("+u.path.join(" > ")+")\n"+u.getErrorInfo())})((l=i).errors).join("\n")+"\n\nInput object: "+yf(l.input));var l},qe);var s}),o=Oa(kr(n,a=>a.trigger)),r=Er(n);return{dataset:n,triggers:o,lookupByTrigger:a=>Ot(r,s=>s.trigger===a)}})(t)),s=i=>{var l;(l=i,e.get().map(c=>sw(t.dom,t.selection.getRng(),c.trigger).bind(d=>cw(t,a,d,l))).getOrThunk(()=>((c,d)=>{const u=d(),m=c.selection.getRng();return(p=c.dom,g=m,h=u,Ko(h.triggers,f=>sw(p,g,f))).bind(p=>cw(c,d,p));var p,g,h})(t,a))).fold(r,l=>{var c,u;c=l.context,o()||(u=c.range,Te(t).autocompleter.addDecoration(u),e.set({trigger:c.trigger,matchLength:c.text.length})),l.lookupData.then(c=>{e.get().map(d=>{const u=l.context;d.trigger===u.trigger&&(u.text.length-d.matchLength>=10?r():(e.set({...d,matchLength:u.text.length}),n.get()?t.dispatch("AutocompleterUpdate",{lookupData:c}):(n.set(!0),t.dispatch("AutocompleterStart",{lookupData:c}))))})})})};t.addCommand("mceAutocompleterReload",(i,l)=>{const c=At(l)?l.fetchOptions:{};s(c)}),t.addCommand("mceAutocompleterClose",r),((i,l)=>{const c=Lu(l.load,50);i.on("keypress compositionend",d=>{27!==d.which&&c.throttle()}),i.on("keydown",d=>{const u=d.which;8===u?c.throttle():27===u&&l.cancelIfNecessary()}),i.on("remove",c.cancel)})(t,{cancelIfNecessary:r,load:s})},Ew=t=>(e,n,o={})=>{const r=e.getBody(),a={bubbles:!0,composed:!0,data:null,isComposing:!1,detail:0,view:null,target:r,currentTarget:r,eventPhase:Event.AT_TARGET,originalTarget:r,explicitOriginalTarget:r,isTrusted:!1,srcElement:r,cancelable:!1,preventDefault:jt,inputType:n},s=Fp(new InputEvent(t));return e.dispatch(t,{...s,...a,...o})},Bd=Ew("input"),Dd=Ew("beforeinput"),ii=(t,e)=>{const n=t.dom,o=t.schema.getMoveCaretBeforeOnEnterElements();if(!e)return;if(/^(LI|DT|DD)$/.test(e.nodeName)){const a=(s=>{for(;s;){if(it(s)||tt(s)&&s.data&&/[\r\n\s]/.test(s.data))return s;s=s.nextSibling}return null})(e.firstChild);a&&/^(UL|OL|DL)$/.test(a.nodeName)&&e.insertBefore(n.doc.createTextNode(Ge),e.firstChild)}const r=n.createRng();if(e.normalize(),e.hasChildNodes()){const a=new Kt(e,e);let s,i=e;for(;s=a.current();){if(tt(s)){r.setStart(s,0),r.setEnd(s,0);break}if(o[s.nodeName.toLowerCase()]){r.setStartBefore(s),r.setEndBefore(s);break}i=s,s=a.next()}s||(r.setStart(i,0),r.setEnd(i,0))}else _e(e)?e.nextSibling&&n.isBlock(e.nextSibling)?(r.setStartBefore(e),r.setEndBefore(e)):(r.setStartAfter(e),r.setEndAfter(e)):(r.setStart(e,0),r.setEnd(e,0));t.selection.setRng(r),cl(t,r)},Pd=(t,e)=>{const n=t.getRoot();let o,r=e;for(;r!==n&&r&&"false"!==t.getContentEditable(r);)"true"===t.getContentEditable(r)&&(o=r),r=r.parentNode;return r!==n?o:n},Af=t=>S.from(t.dom.getParent(t.selection.getStart(!0),t.dom.isBlock)),_w=(t,e)=>{const n=t?.parentNode;return st(n)&&n.nodeName===e},Nw=t=>st(t)&&/^(OL|UL|LI)$/.test(t.nodeName),Md=t=>{const e=t.parentNode;return st(n=e)&&/^(LI|DT|DD)$/.test(n.nodeName)?e:t;var n},Ld=(t,e,n)=>{let o=t[n?"firstChild":"lastChild"];for(;o&&!it(o);)o=o[n?"nextSibling":"previousSibling"];return o===e},aT=(t,e)=>e&&"A"===e.nodeName&&t.isEmpty(e),Tf=t=>{t.innerHTML='<br data-mce-bogus="1">'},Of=(t,e)=>t.nodeName===e||t.previousSibling&&t.previousSibling.nodeName===e,Bf=(t,e)=>st(e)&&t.isBlock(e)&&!/^(TD|TH|CAPTION|FORM)$/.test(e.nodeName)&&!/^(fixed|absolute)/i.test(e.style.position)&&t.isEditable(e.parentNode)&&"false"!==t.getContentEditable(e),Df=(t,e,n)=>tt(e)?t?1===n&&e.data.charAt(n-1)===dn?0:n:n===e.data.length-1&&e.data.charAt(n)===dn?e.data.length:n:n,Ol=(t,e)=>{Bn(t).toLowerCase()===e.tagName.toLowerCase()&&((n,o,r)=>{const a=n.dom;S.from(r.style).map(a.parseStyle).each(d=>{const u={...lc(A(o)),...d};a.setStyles(o,u)});const s=S.from(r.class).map(d=>d.split(/\s+/)),i=S.from(o.className).map(d=>Ot(d.split(/\s+/),u=>""!==u));on(s,i,(d,u)=>{const m=Ot(u,g=>!Lt(d,g)),p=[...d,...m];a.setAttrib(o,"class",p.join(" "))});const l=["style","class"],c=Go(r,(d,u)=>!Lt(l,u));a.setAttribs(o,c)})(t,e,Yi(t))},Rw={insert:(t,e)=>{let n,o,r,a,s=!1;const i=t.dom,l=t.schema,c=l.getNonEmptyElements(),d=t.selection.getRng(),u=Bn(t),m=d.collapsed&&d.startContainer===t.dom.getRoot(),p=A(d.startContainer),g=na(p,d.startOffset),h=g.exists(T=>xi(T)&&!aa(T)),f=m&&h,b=T=>{let P=n;const B=l.getTextInlineElements();let j;j=T||"TABLE"===r||"HR"===r?i.create(T||u):R.cloneNode(!1);let lt=j;if(!1===qk(t))i.setAttrib(j,"style",null),i.setAttrib(j,"class",null);else do{if(B[P.nodeName]){if(_o(P)||ro(P))continue;const Z=P.cloneNode(!1);i.setAttrib(Z,"id",""),j.hasChildNodes()?(Z.appendChild(j.firstChild),j.appendChild(Z)):(lt=Z,j.appendChild(Z))}}while((P=P.parentNode)&&P!==N);return Ol(t,j),Tf(lt),j},y=T=>{const P=Df(T,n,o);if(tt(n)&&(T?P>0:P<n.data.length))return!1;if(n.parentNode===R&&s&&!T||T&&it(n)&&n===R.firstChild)return!0;if(Of(n,"TABLE")||Of(n,"HR"))return s&&!T||!s&&T;const B=new Kt(n,R);let j;for(tt(n)&&(T&&0===P?B.prev():T||P!==n.data.length||B.next());j=B.current();){if(it(j)){if(!j.getAttribute("data-mce-bogus")){const lt=j.nodeName.toLowerCase();if(c[lt]&&"br"!==lt)return!1}}else if(tt(j)&&!Va(j.data))return!1;T?B.prev():B.next()}return!0},C=()=>{let T;return T=/^(H[1-6]|PRE|FIGURE)$/.test(r)&&"HGROUP"!==I?b(u):b(),((P,B)=>{const j=Vk(P);return!pe(B)&&(Nt(j)?Lt(ot.explode(j),B.nodeName.toLowerCase()):j)})(t,a)&&Bf(i,a)&&i.isEmpty(R,void 0,{includeZwsp:!0})?T=i.split(a,R):i.insertAfter(T,R),ii(t,T),T};Wc(i,d).each(T=>{d.setStart(T.startContainer,T.startOffset),d.setEnd(T.endContainer,T.endOffset)}),n=d.startContainer,o=d.startOffset;const k=!(!e||!e.shiftKey),E=!(!e||!e.ctrlKey);it(n)&&n.hasChildNodes()&&!f&&(s=o>n.childNodes.length-1,n=n.childNodes[Math.min(o,n.childNodes.length-1)]||n,o=s&&tt(n)?n.data.length:0);const N=Pd(i,n);if(!N||((T,P)=>{const B=T.dom.getParent(P,"ol,ul,dl");return null!==B&&"false"===T.dom.getContentEditableParent(B)})(t,n))return;k||(n=((T,P,B,j,lt)=>{var Z,_t;const mt=T.dom,Ut=null!==(Z=Pd(mt,j))&&void 0!==Z?Z:mt.getRoot();let Ht=mt.getParent(j,mt.isBlock);if(!Ht||!Bf(mt,Ht)){if(Ht=Ht||Ut,!Ht.hasChildNodes()){const O=mt.create(P);return Ol(T,O),Ht.appendChild(O),B.setStart(O,0),B.setEnd(O,0),O}let ft,kt=j;for(;kt&&kt.parentNode!==Ht;)kt=kt.parentNode;for(;kt&&!mt.isBlock(kt);)ft=kt,kt=kt.previousSibling;const Bt=null===(_t=ft?.parentElement)||void 0===_t?void 0:_t.nodeName;if(ft&&Bt&&T.schema.isValidChild(Bt,P.toLowerCase())){const O=ft.parentNode,D=mt.create(P);for(Ol(T,D),O.insertBefore(D,ft),kt=ft;kt&&!mt.isBlock(kt);){const V=kt.nextSibling;D.appendChild(kt),kt=V}B.setStart(j,lt),B.setEnd(j,lt)}}return j})(t,u,d,n,o));let R=i.getParent(n,i.isBlock)||i.getRoot();a=st(R?.parentNode)?i.getParent(R.parentNode,i.isBlock):null,r=R?R.nodeName.toUpperCase():"";const I=a?a.nodeName.toUpperCase():"";if("LI"!==I||E||(R=a,a=a.parentNode,r=I),it(a)&&(T=t,B=R,!k&&B.nodeName.toLowerCase()===Bn(T)&&T.dom.isEmpty(B)&&((j,lt)=>{let _t=lt;for(;_t&&_t!==j&&Oe(_t.nextSibling);){const Ut=_t.parentElement;if(!Ut||(mt=Ut,!It(T.schema.getTextBlockElements(),mt.nodeName.toLowerCase())))return mp(Ut);_t=Ut}var mt;return!1})(T.getBody(),B)))return((T,P,B)=>{var j,lt,Z;const _t=P(Bn(T)),mt=T.dom.getParent(B,mp);mt&&(T.dom.insertAfter(_t,mt),ii(T,_t),(null!==(Z=null===(lt=null===(j=B.parentElement)||void 0===j?void 0:j.childNodes)||void 0===lt?void 0:lt.length)&&void 0!==Z?Z:0)>1&&T.dom.remove(B))})(t,b,R);var T,B;if(/^(LI|DT|DD)$/.test(r)&&it(a)&&i.isEmpty(R))return void((T,P,B,j,lt)=>{const Z=T.dom,_t=T.selection.getRng(),mt=B.parentNode;if(B===T.getBody()||!mt)return;var Ut;Nw(Ut=B)&&Nw(Ut.parentNode)&&(lt="LI");let Ht=P(lt);if(Ld(B,j,!0)&&Ld(B,j,!1))if(_w(B,"LI")){const ft=Md(B);Z.insertAfter(Ht,ft),(null===(Bt=(kt=B).parentNode)||void 0===Bt?void 0:Bt.firstChild)===kt?Z.remove(ft):Z.remove(B)}else Z.replace(Ht,B);else if(Ld(B,j,!0))_w(B,"LI")?(Z.insertAfter(Ht,Md(B)),Ht.appendChild(Z.doc.createTextNode(" ")),Ht.appendChild(B)):mt.insertBefore(Ht,B),Z.remove(j);else if(Ld(B,j,!1))Z.insertAfter(Ht,Md(B)),Z.remove(j);else{B=Md(B);const ft=_t.cloneRange();ft.setStartAfter(j),ft.setEndAfter(B);const kt=ft.extractContents();"LI"===lt&&(Bt=>Bt.firstChild&&"LI"===Bt.firstChild.nodeName)(kt)?(Ht=kt.firstChild,Z.insertAfter(kt,B)):(Z.insertAfter(kt,B),Z.insertAfter(Ht,B)),Z.remove(j)}var kt,Bt;ii(T,Ht)})(t,b,a,R,u);if(!(f||R!==t.getBody()&&Bf(i,R)))return;const q=R.parentNode;let H;if(f)H=b(u),g.fold(()=>{Se(p,A(H))},T=>{bn(T,A(H))}),t.selection.setCursorLocation(H,0);else if(Fs(R))H=yu(R),i.isEmpty(R)&&Tf(R),Ol(t,H),ii(t,H);else if(y(!1))H=C();else if(y(!0)&&q){H=q.insertBefore(b(),R);const T=A(d.startContainer).dom.hasChildNodes()&&d.collapsed;ii(t,Of(R,"HR")||T?H:R)}else{const T=(B=>{const j=B.cloneRange();return j.setStart(B.startContainer,Df(!0,B.startContainer,B.startOffset)),j.setEnd(B.endContainer,Df(!1,B.endContainer,B.endOffset)),j})(d).cloneRange();T.setEndAfter(R);const P=T.extractContents();(B=>{J($t(A(B),pn),j=>{const lt=j.dom;lt.nodeValue=Fr(lt.data)})})(P),(B=>{let j=B;do{tt(j)&&(j.data=j.data.replace(/^[\r\n]+/,"")),j=j.firstChild}while(j)})(P),H=P.firstChild,i.insertAfter(P,R),((B,j,lt)=>{var Z;const _t=[];if(!lt)return;let mt=lt;for(;mt=mt.firstChild;){if(B.isBlock(mt))return;it(mt)&&!j[mt.nodeName.toLowerCase()]&&_t.push(mt)}let Ut=_t.length;for(;Ut--;)mt=_t[Ut],(!mt.hasChildNodes()||mt.firstChild===mt.lastChild&&""===(null===(Z=mt.firstChild)||void 0===Z?void 0:Z.nodeValue)||aT(B,mt))&&B.remove(mt)})(i,c,H),((B,j)=>{j.normalize();const lt=j.lastChild;(!lt||it(lt)&&/^(left|right)$/gi.test(B.getStyle(lt,"float",!0)))&&B.add(j,"br")})(i,R),i.isEmpty(R)&&Tf(R),H.normalize(),i.isEmpty(H)?(i.remove(H),C()):(Ol(t,H),ii(t,H))}i.setAttrib(H,"id",""),t.dispatch("NewBlock",{newBlock:H})},fakeEventName:"insertParagraph"},Aw=(t,e,n)=>{const o=t.dom.createRng();n?(o.setStartBefore(e),o.setEndBefore(e)):(o.setStartAfter(e),o.setEndAfter(e)),t.selection.setRng(o),cl(t,o)},sT=(t,e)=>{const n=Be("br");bn(A(e),n),t.undoManager.add()},iT=(t,e)=>{lT(t.getBody(),e)||to(A(e),Be("br"));const n=Be("br");to(A(e),n),Aw(t,n.dom,!1),t.undoManager.add()},lT=(t,e)=>{return n=$.after(e),!!_e(n.getNode())||yn(t,$.after(e)).map(o=>_e(o.getNode())).getOr(!1);var n},Tw=t=>t&&"A"===t.nodeName&&"href"in t,cT=t=>t.fold(de,Tw,Tw,de),dT=(t,e)=>{e.fold(jt,Ct(sT,t),Ct(iT,t),jt)},Ow={insert:(t,e)=>{const n=(o=>{const r=Ct(cs,o),a=$.fromRangeStart(o.selection.getRng());return ur(r,o.getBody(),a).filter(cT)})(t);n.isSome()?n.each(Ct(dT,t)):((o,r)=>{const s=o.dom,i=o.selection.getRng();let l,c=!1;Wc(s,i).each(f=>{i.setStart(f.startContainer,f.startOffset),i.setEnd(f.endContainer,f.endOffset)});let d=i.startOffset,u=i.startContainer;if(it(u)&&u.hasChildNodes()){const f=d>u.childNodes.length-1;u=u.childNodes[Math.min(d,u.childNodes.length-1)]||u,d=f&&tt(u)?u.data.length:0}let m=s.getParent(u,s.isBlock);const p=m&&m.parentNode?s.getParent(m.parentNode,s.isBlock):null;"LI"!==(p?p.nodeName.toUpperCase():"")||!(!r||!r.ctrlKey)||(m=p),tt(u)&&d>=u.data.length&&(((f,b,y)=>{const C=new Kt(b,y);let k;const E=f.getNonEmptyElements();for(;k=C.next();)if(E[k.nodeName.toLowerCase()]||tt(k)&&k.length>0)return!0;return!1})(o.schema,u,m||s.getRoot())||(l=s.create("br"),i.insertNode(l),i.setStartAfter(l),i.setEndAfter(l),c=!0)),l=s.create("br"),zu(s,i,l),Aw(o,l,c),o.undoManager.add()})(t,e)},fakeEventName:"insertLineBreak"},Bw=(t,e)=>Af(t).filter(n=>e.length>0&&hn(A(n),e)).isSome(),$n=or([{br:[]},{block:[]},{none:[]}]),uT=(t,e)=>{return Bw(n=t,$k(n));var n},Dw=t=>(e,n)=>Af(e).filter(r=>Pi(A(r))).isSome()===t,Pw=(t,e)=>(n,o)=>Af(n).fold(ut(""),s=>s.nodeName.toUpperCase())===t.toUpperCase()===e,mT=t=>{const e=Pd(t.dom,t.selection.getStart());return pe(e)},Bl=t=>Pw("pre",t),Id=t=>(e,n)=>jk(e)===t,gT=(t,e)=>{return Bw(n=t,Hk(n));var n},Fd=(t,e)=>e,fT=t=>{const e=Bn(t),n=Pd(t.dom,t.selection.getStart());return st(n)&&t.schema.isValidChild(n.nodeName,e)},pT=t=>{const e=t.selection.getRng(),n=e.collapsed&&e.startContainer===t.dom.getRoot(),o=A(e.startContainer),r=na(o,e.startOffset).map(a=>xi(a)&&!aa(a));return n&&r.getOr(!0)},uo=(t,e)=>(n,o)=>Re(t,(r,a)=>r&&a(n,o),!0)?S.some(e):S.none(),Ud=(t,e,n)=>{e.selection.isCollapsed()||e.execCommand("delete"),st(n)&&Dd(e,t.fakeEventName).isDefaultPrevented()||(t.insert(e,n),st(n)&&Bd(e,t.fakeEventName))},Mw=(t,e)=>{const n=()=>Ud(Ow,t,e),o=()=>Ud(Rw,t,e),r=(a=t,s=e,HC([uo([uT],$n.none()),uo([Bl(!0),mT],$n.none()),uo([Pw("summary",!0)],$n.br()),uo([Bl(!0),Id(!1),Fd],$n.br()),uo([Bl(!0),Id(!1)],$n.block()),uo([Bl(!0),Id(!0),Fd],$n.block()),uo([Bl(!0),Id(!0)],$n.br()),uo([Dw(!0),Fd],$n.br()),uo([Dw(!0)],$n.block()),uo([gT],$n.br()),uo([Fd],$n.br()),uo([fT],$n.block()),uo([pT],$n.block())],[a,!(!s||!s.shiftKey)]).getOr($n.none()));var a,s;switch(zk(t)){case"linebreak":r.fold(n,n,jt);break;case"block":r.fold(o,o,jt);break;case"invert":r.fold(o,n,jt);break;default:r.fold(n,o,jt)}},Lw=po(),hT=Lw.os.isiOS()&&Lw.browser.isSafari(),Iw=(t,e)=>{var n;e.isDefaultPrevented()||(e.preventDefault(),(n=t.undoManager).typing&&(n.typing=!1,n.add()),t.undoManager.transact(()=>{Mw(t,e)}))},bT=po(),Fw=t=>t.stopImmediatePropagation(),Uw=t=>t.keyCode===wt.PAGE_UP||t.keyCode===wt.PAGE_DOWN,jw=(t,e,n)=>{n&&!t.get()?e.on("NodeChange",Fw,!0):!n&&t.get()&&e.off("NodeChange",Fw),t.set(n)},zw=(t,e)=>{const n=e.container(),o=e.offset();return tt(n)?(n.insertData(o,t),S.some($(n,o+t.length))):Dc(e).map(r=>{const a=bo(t);return e.isAtEnd()?to(r,a):bn(r,a),$(a.dom,t.length)})},Hw=Ct(zw,Ge),$w=Ct(zw," "),qw=t=>e=>{t.selection.setRng(e.toRange()),t.nodeChanged()},vT=t=>{const e=$.fromRangeStart(t.selection.getRng()),n=A(t.getBody());if(t.selection.isCollapsed()){const o=Ct(cs,t),r=$.fromRangeStart(t.selection.getRng());return ur(o,t.getBody(),r).bind((a=>s=>s.fold(i=>Dn(a.dom,$.before(i)),i=>Cn(i),i=>Eo(i),i=>yn(a.dom,$.after(i))))(n)).map(a=>()=>((s,i)=>l=>Yv(s,l)?Hw(i):$w(i))(n,e)(a).each(qw(t)))}return S.none()},yT=t=>{return ho(Jt.browser.isFirefox()&&t.selection.isEditable()&&(e=t.dom,n=t.selection.getRng().startContainer,e.isEditable(e.getParent(n,"summary"))),()=>{const o=A(t.getBody());var r,a;t.selection.isCollapsed()||t.getDoc().execCommand("Delete"),(r=o,a=$.fromRangeStart(t.selection.getRng()),Yv(r,a)?Hw(a):$w(a)).each(qw(t))});var e,n},CT=t=>QE(t)?[{keyCode:wt.TAB,action:qt(nw,t,!0)},{keyCode:wt.TAB,shiftKey:!0,action:qt(nw,t,!1)}]:[],wT=t=>{if(t.addShortcut("Meta+P","","mcePrint"),rT(t),dr(t))return Ye(null);{const e=(t=>{const e=Ye(null),n=Ct(cs,t);return t.on("NodeChange",o=>{Xi(t)&&(((r,a,s)=>{const i=te(Rt(A(a.getRoot()),'*[data-mce-selected="inline-boundary"]'),d=>d.dom),l=Ot(i,r),c=Ot(s,r);J(Gn(l,c),Ct(l1,!1)),J(Gn(c,l),Ct(l1,!0))})(n,t.dom,o.parents),((r,a)=>{const s=a.get();if(r.selection.isCollapsed()&&!r.composing&&s){const i=$.fromRangeStart(r.selection.getRng());$.isTextPosition(i)&&!bu(l=i)&&!vu(l)&&(Sd(r,((t,e)=>{return $.isTextPosition(e)?(o=e,qh(n=t)&&o.container()===n?((r,a)=>{const s=Wh(r.data.substr(0,a.offset())),i=Wh(r.data.substr(a.offset()));return(s.text+i.text).length>0?(Kh(r),$(r,a.offset()-s.count)):a})(n,o):Gh(n,o)):((n,o)=>o.container()===n.parentNode?((r,a)=>{const s=a.container(),i=((l,c)=>{const d=Vo(l,c);return-1===d?S.none():S.some(d)})(Ae(s.childNodes),r).map(l=>l<a.offset()?$(s,a.offset()-1):a).getOr(a);return ts(r),i})(n,o):Gh(n,o))(t,e);var n,o})(s,i)),a.set(null))}var l})(t,e),((r,a,s,i)=>{if(a.selection.isCollapsed()){const l=Ot(i,r);J(l,c=>{const d=$.fromRangeStart(a.selection.getRng());ur(r,a.getBody(),d).bind(u=>c1(a,s,u))})}})(n,t,e,o.parents))}),e})(t);return(n=t).on("keyup compositionstart",Ct(xA,n)),((n,o)=>{n.on("keydown",r=>{r.isDefaultPrevented()||((a,s,i)=>{const l=Jt.os.isMacOS()||Jt.os.isiOS();_l([{keyCode:wt.RIGHT,action:qt(P1,a,!0)},{keyCode:wt.LEFT,action:qt(P1,a,!1)},{keyCode:wt.UP,action:qt(M1,a,!1)},{keyCode:wt.DOWN,action:qt(M1,a,!0)},...l?[{keyCode:wt.UP,action:qt(Ed,a,!1),metaKey:!0,shiftKey:!0},{keyCode:wt.DOWN,action:qt(Ed,a,!0),metaKey:!0,shiftKey:!0}]:[],{keyCode:wt.RIGHT,action:qt(J1,a,!0)},{keyCode:wt.LEFT,action:qt(J1,a,!1)},{keyCode:wt.UP,action:qt(Z1,a,!1)},{keyCode:wt.DOWN,action:qt(Z1,a,!0)},{keyCode:wt.RIGHT,action:qt(z1,a,!0)},{keyCode:wt.LEFT,action:qt(z1,a,!1)},{keyCode:wt.UP,action:qt(H1,a,!1)},{keyCode:wt.DOWN,action:qt(H1,a,!0)},{keyCode:wt.RIGHT,action:qt(d1,a,s,!0)},{keyCode:wt.LEFT,action:qt(d1,a,s,!1)},{keyCode:wt.RIGHT,ctrlKey:!l,altKey:l,action:qt(uA,a,s)},{keyCode:wt.LEFT,ctrlKey:!l,altKey:l,action:qt(mA,a,s)},{keyCode:wt.UP,action:qt(I1,a,!1)},{keyCode:wt.DOWN,action:qt(I1,a,!0)}],i).each(c=>{i.preventDefault()})})(n,o,r)})})(t,e),((n,o)=>{let r=!1;n.on("keydown",a=>{r=a.keyCode===wt.BACKSPACE,a.isDefaultPrevented()||((s,i,l)=>{const c=l.keyCode===wt.BACKSPACE?"deleteContentBackward":"deleteContentForward";j1([{keyCode:wt.BACKSPACE,action:qt(E1,s)},{keyCode:wt.BACKSPACE,action:qt(Qg,s,!1)},{keyCode:wt.DELETE,action:qt(Qg,s,!0)},{keyCode:wt.BACKSPACE,action:qt(Gg,s,!1)},{keyCode:wt.DELETE,action:qt(Gg,s,!0)},{keyCode:wt.BACKSPACE,action:qt(sf,s,i,!1)},{keyCode:wt.DELETE,action:qt(sf,s,i,!0)},{keyCode:wt.BACKSPACE,action:qt(ng,s,!1)},{keyCode:wt.DELETE,action:qt(ng,s,!0)},{keyCode:wt.BACKSPACE,action:qt(Jg,s,!1)},{keyCode:wt.DELETE,action:qt(Jg,s,!0)},{keyCode:wt.BACKSPACE,action:qt(df,s,!1)},{keyCode:wt.DELETE,action:qt(df,s,!0)},{keyCode:wt.BACKSPACE,action:qt(Wg,s,!1)},{keyCode:wt.DELETE,action:qt(Wg,s,!0)},{keyCode:wt.BACKSPACE,action:qt(Vg,s,!1)},{keyCode:wt.DELETE,action:qt(Vg,s,!0)},{keyCode:wt.BACKSPACE,action:qt(lf,s,!1)},{keyCode:wt.DELETE,action:qt(lf,s,!0)}],l).filter(d=>s.selection.isEditable()).each(d=>{l.preventDefault(),Dd(s,c).isDefaultPrevented()||(d(),Bd(s,c))})})(n,o,a)}),n.on("keyup",a=>{a.isDefaultPrevented()||((s,i,l)=>{const c=po(),d=c.os,u=c.browser,m=d.isMacOS()?[{keyCode:wt.BACKSPACE,altKey:!0,action:qt(El,s)},{keyCode:wt.DELETE,altKey:!0,action:qt(El,s)}]:[{keyCode:wt.BACKSPACE,ctrlKey:!0,action:qt(El,s)},{keyCode:wt.DELETE,ctrlKey:!0,action:qt(El,s)}];d.isMacOS()&&l&&m.push({keyCode:u.isFirefox()?224:91,action:qt(El,s)}),_l([{keyCode:wt.BACKSPACE,action:qt(LC,s)},{keyCode:wt.DELETE,action:qt(LC,s)},...m],i)})(n,a,r),r=!1})})(t,e),(n=>{let o=S.none();n.on("keydown",r=>{var a;r.keyCode===wt.ENTER&&(hT&&(a=>{if(!a.collapsed)return!1;const s=a.startContainer;if(tt(s)){const i=/^[\uAC00-\uD7AF\u1100-\u11FF\u3130-\u318F\uA960-\uA97F\uD7B0-\uD7FF]$/,l=s.data.charAt(a.startOffset-1);return i.test(l)}return!1})(n.selection.getRng())?(o=S.some((a=n).selection.getBookmark()),a.undoManager.add()):Iw(n,r))}),n.on("keyup",r=>{r.keyCode===wt.ENTER&&o.each(()=>{return s=r,(a=n).undoManager.undo(),o.fold(jt,i=>a.selection.moveToBookmark(i)),Iw(a,s),void(o=S.none());var a,s})})})(t),(n=>{n.on("keydown",o=>{var r,a;o.isDefaultPrevented()||(a=o,j1([{keyCode:wt.SPACEBAR,action:qt(vT,r=n)},{keyCode:wt.SPACEBAR,action:qt(yT,r)}],a).each(s=>{a.preventDefault(),Dd(r,"insertText",{data:" "}).isDefaultPrevented()||(s(),Bd(r,"insertText",{data:" "}))}))})})(t),(n=>{n.on("input",o=>{o.isComposing||(r=>{const a=A(r.getBody());r.selection.isCollapsed()&&((t,e)=>{const n=e.container();if(!tt(n))return S.none();if((o=>{const r=o.container();return tt(r)&&ct(r.data,Ge)})(e)){const o=Jv(t,n,!1)||(r=>{const a=r.data,s=(i=>{const l=i.split("");return te(l,(c,d)=>Ic(c)&&d>0&&d<l.length-1&&am(l[d-1])&&am(l[d+1])?" ":c).join("")})(a);return s!==a&&(r.data=s,!0)})(n)||Zv(t,n,!1);return ho(o,e)}if(qm(t,e)){const o=Jv(t,n,!0)||Zv(t,n,!0);return ho(o,e)}return S.none()})(a,$.fromRangeStart(r.selection.getRng())).each(s=>{r.selection.setRng(s.toRange())})})(n)})})(t),(n=>{n.on("keydown",o=>{var a;o.isDefaultPrevented()||(a=o,_l([...CT(n)],a).each(s=>{a.preventDefault()}))})})(t),((n,o)=>{n.on("keydown",r=>{r.isDefaultPrevented()||((a,s,i)=>{const l=Jt.os.isMacOS()||Jt.os.isiOS();_l([{keyCode:wt.END,action:qt(L1,a,!0)},{keyCode:wt.HOME,action:qt(L1,a,!1)},...l?[]:[{keyCode:wt.HOME,action:qt(Ed,a,!1),ctrlKey:!0,shiftKey:!0},{keyCode:wt.END,action:qt(Ed,a,!0),ctrlKey:!0,shiftKey:!0}],{keyCode:wt.END,action:qt($1,a,!0)},{keyCode:wt.HOME,action:qt($1,a,!1)},{keyCode:wt.END,action:qt(kd,a,!0,s)},{keyCode:wt.HOME,action:qt(kd,a,!1,s)}],i).each(c=>{i.preventDefault()})})(n,o,r)})})(t,e),((n,o)=>{if(bT.os.isMacOS())return;const r=Ye(!1);n.on("keydown",a=>{Uw(a)&&jw(r,n,!0)}),n.on("keyup",a=>{var s,i,l;a.isDefaultPrevented()||(l=a,_l([{keyCode:wt.PAGE_UP,action:qt(kd,s=n,!1,i=o)},{keyCode:wt.PAGE_DOWN,action:qt(kd,s,!0,i)}],l)),Uw(a)&&r.get()&&(jw(r,n,!1),n.nodeChanged())})})(t,e),e}var n};class xT{constructor(e){let n;this.lastPath=[],this.editor=e;const o=this;"onselectionchange"in e.getDoc()||e.on("NodeChange click mouseup keyup focus",r=>{const a=e.selection.getRng(),s={startContainer:a.startContainer,startOffset:a.startOffset,endContainer:a.endContainer,endOffset:a.endOffset};"nodechange"!==r.type&&Sm(s,n)||e.dispatch("SelectionChange"),n=s}),e.on("contextmenu",()=>{e.dispatch("SelectionChange")}),e.on("SelectionChange",()=>{const r=e.selection.getStart(!0);r&&Uc(e)&&!o.isSameElementPath(r)&&e.dom.isChildOf(r,e.getBody())&&e.nodeChanged({selectionChange:!0})}),e.on("mouseup",r=>{!r.isDefaultPrevented()&&Uc(e)&&("IMG"===e.selection.getNode().nodeName?so.setEditorTimeout(e,()=>{e.nodeChanged()}):e.nodeChanged())})}nodeChanged(e={}){const n=this.editor.selection;let o;if(this.editor.initialized&&n&&!CE(this.editor)&&!this.editor.mode.isReadOnly()){const r=this.editor.getBody();o=n.getStart(!0)||r,o.ownerDocument===this.editor.getDoc()&&this.editor.dom.isChildOf(o,r)||(o=r);const a=[];this.editor.dom.getParent(o,s=>s===r||(a.push(s),!1)),this.editor.dispatch("NodeChange",{...e,element:o,parents:a})}}isSameElementPath(e){let n;const o=this.editor,r=Ao(o.dom.getParents(e,ye,o.getBody()));if(r.length===this.lastPath.length){for(n=r.length;n>=0&&r[n]===this.lastPath[n];n--);if(-1===n)return this.lastPath=r,!0}return this.lastPath=r,!1}}const Vw=qi("image"),Pf=qi("event"),jd=t=>e=>{e[Pf]=t},Ww=jd(0),ST=jd(2),kT=jd(1),Mf=qi("mode"),zd=t=>e=>{e[Mf]=t},Kw=(t,e)=>zd(e)(t),Gw=zd(0),Lf=zd(2),_T=zd(1),Yw=t=>e=>S.from(e[Mf]).exists(o=>o===t),li=Yw(0),Xw=Yw(1),NT=["none","copy","link","move"],RT=["none","copy","copyLink","copyMove","link","linkMove","move","all","uninitialized"],If=()=>{const t=new window.DataTransfer;let e="move",n="all";const o={get dropEffect(){return e},set dropEffect(r){Lt(NT,r)&&(e=r)},get effectAllowed(){return n},set effectAllowed(r){(t=>S.from(t[Pf]).exists(n=>0===n))(o)&&Lt(RT,r)&&(n=r)},get items(){return r=o,a=t.items,{...a,get length(){return a.length},add:(s,i)=>{if(li(r)){if(!Nt(s))return a.add(s);if(!dt(i))return a.add(s,i)}return null},remove:s=>{li(r)&&a.remove(s)},clear:()=>{li(r)&&a.clear()}};var r,a},get files(){return Xw(o)?Object.freeze({length:0,item:r=>null}):t.files},get types(){return t.types},setDragImage:(r,a,s)=>{li(o)&&(o[Vw]={image:r,x:a,y:s},t.setDragImage(r,a,s))},getData:r=>Xw(o)?"":t.getData(r),setData:(r,a)=>{li(o)&&t.setData(r,a)},clearData:r=>{li(o)&&t.clearData(r)}};return Gw(o),o},Qw=(t,e)=>t.setData("text/html",e),Jw="x-tinymce/html",Hd=ut(Jw),Ff="\x3c!-- "+Jw+" --\x3e",AT=t=>Ff+t,Zw=t=>-1!==t.indexOf(Ff),tx="%MCEPASTEBIN%",Uf=t=>t.dom.get("mcepastebin"),TT=t=>st(t)&&"mcepastebin"===t.id,ex=t=>t===tx,nx=(t,e)=>(ot.each(e,n=>{t=Ce(n,RegExp)?t.replace(n,""):t.replace(n[0],n[1])}),t),ox=t=>nx(t,[/^[\s\S]*<body[^>]*>\s*|\s*<\/body[^>]*>[\s\S]*$/gi,/<!--StartFragment-->|<!--EndFragment-->/g,[/( ?)<span class="Apple-converted-space">\u00a0<\/span>( ?)/g,(e,n,o)=>n||o?Ge:" "],/<br class="Apple-interchange-newline">/g,/<br>$/i]),rx=(t,e)=>({content:t,cancelled:e}),jf=(t,e)=>(t.insertContent(e,{merge:UE(t),paste:!0}),!0),zf=t=>/^https?:\/\/[\w\-\/+=.,!;:&%@^~(){}?#]+$/i.test(t),OT=(t,e,n)=>{return!(t.selection.isCollapsed()||!zf(e)||(o=t,r=e,a=n,o.undoManager.extra(()=>{a(o,r)},()=>{o.execCommand("mceInsertLink",!1,r)}),0));var o,r,a},BT=(t,e,n)=>{return o=t,!(!zf(r=e)||!ie($h(o),a=>pt(r.toLowerCase(),`.${a.toLowerCase()}`)))&&((o,r,a)=>(o.undoManager.extra(()=>{a(o,r)},()=>{o.insertContent('<img src="'+r+'">')}),!0))(t,e,n);var o,r},DT=(()=>{let e=0;return()=>"mceclip"+e++})(),PT=t=>{const e=If();return Qw(e,t),Lf(e),e},ax=(t,e,n,o,r)=>{const a=((c,d,u)=>{const m=c.dispatch("PastePreProcess",{content:d,internal:u}),p=((g,h)=>{const f=ti({sanitize:Xu(g)},g.schema);f.addNodeFilter("meta",y=>{ot.each(y,C=>{C.remove()})});const b=f.parse(h,{forced_root_block:!1,isRootContent:!0});return ba({validate:!0},g.schema).serialize(b)})(c,m.content);return c.hasEventListeners("PastePostProcess")&&!m.isDefaultPrevented()?((g,h,f)=>{const b=g.dom.create("div",{style:"display:none"},h),y=g.dispatch("PastePostProcess",{node:b,internal:f});return rx(y.node.innerHTML,y.isDefaultPrevented())})(c,p,u):rx(p,m.isDefaultPrevented())})(t,e,n);if(!a.cancelled){const s=a.content,i=()=>{return l=t,c=s,void(o||!jE(l)?jf(l,c):(u=l,m=c,ot.each([OT,BT,jf],p=>!p(u,m,jf))));var l,c,u,m};r?Dd(t,"insertFromPaste",{dataTransfer:PT(s)}).isDefaultPrevented()||(i(),Bd(t,"insertFromPaste")):i()}},$d=(t,e,n,o)=>{const r=n||Zw(e);ax(t,e.replace(Ff,""),r,!1,o)},Hf=(t,e,n)=>{const o=t.dom.encode(e).replace(/\r\n/g,"\n"),r=((a,s,i)=>{const l=a.split(/\n\n/),c=((m,p)=>{let g="<"+m;const h=kr(p,(f,b)=>b+'="'+da.encodeAllRaw(f)+'"');return h.length&&(g+=" "+h.join(" ")),g+">"})(s,i),d="</"+s+">",u=te(l,m=>m.split(/\n/).join("<br />"));return 1===u.length?u[0]:te(u,m=>c+m+d).join("")})(wp(o,HE(t)),Bn(t),Yi(t));ax(t,r,!1,!0,n)},sx=t=>{const e={};if(t&&t.types)for(let n=0;n<t.types.length;n++){const o=t.types[n];try{e[o]=t.getData(o)}catch{e[o]=""}}return e},Sa=(t,e)=>e in t&&t[e].length>0,ix=t=>Sa(t,"text/html")||Sa(t,"text/plain"),lx=(t,e,n)=>{const o="paste"===e.type?e.clipboardData:e.dataTransfer;var r;if(Oc(t)&&o){const a=((s,i)=>{const l=i.items?gn(Ae(i.items),d=>"file"===d.kind?[d.getAsFile()]:[]):[],c=i.files?Ae(i.files):[];return Ot(l.length>0?l:c,(d=>{const u=$h(d);return m=>ht(m.type,"image/")&&ie(u,p=>(g=>{const h=g.toLowerCase(),f={jpg:"jpeg",jpe:"jpeg",jfi:"jpeg",jif:"jpeg",jfif:"jpeg",pjpeg:"jpeg",pjp:"jpeg",svg:"svg+xml"};return ot.hasOwn(f,h)?"image/"+f[h]:"image/"+h})(p)===m.type)})(s))})(t,o);if(a.length>0)return e.preventDefault(),(r=a,Promise.all(te(r,s=>vy(s).then(i=>({file:s,uri:i}))))).then(s=>{n&&t.selection.setRng(n),J(s,i=>{var l,c;l=t,Sg((c=i).uri).each(({data:d,type:u,base64Encoded:m})=>{const p=m?d:btoa(d),g=c.file,h=l.editorUpload.blobCache,b=h.getByData(p,u)??((y,C,k,E)=>{const N=DT(),R=Bh(y)&&st(k.name),I=R?((T,P)=>{const B=P.match(/([\s\S]+?)(?:\.[a-z0-9.]+)$/i);return st(B)?T.dom.encode(B[1]):void 0})(y,k.name):N,H=C.create(N,k,E,I,R?k.name:void 0);return C.add(H),H})(l,h,g,p);$d(l,`<img src="${b.blobUri()}">`,!1,!0)})})}),!0}return!1},$f=(t,e,n,o,r)=>{let a=ox(n);const s=Sa(e,Hd())||Zw(n),i=!s&&!/<(?:\/?(?!(?:div|p|br|span)>)\w+|(?:(?!(?:span style="white-space:\s?pre;?">)|br\s?\/>))\w+\s[^>]+)>/i.test(a),l=zf(a);(ex(a)||!a.length||i&&!l)&&(o=!0),(o||l)&&(a=Sa(e,"text/plain")&&i?e["text/plain"]:(c=>{const d=ua(),u=ti({},d);let m="";const p=d.getVoidElements(),g=ot.makeMap("script noscript style textarea video audio iframe object"," "),h=d.getBlockElements(),f=b=>{const y=b.name,C=b;if("br"!==y){if("wbr"!==y)if(p[y]&&(m+=" "),g[y])m+=" ";else{if(3===b.type&&(m+=b.value),!(b.name in d.getVoidElements())){let k=b.firstChild;if(k)do{f(k)}while(k=k.next)}h[y]&&C.next&&(m+="\n","p"===y&&(m+="\n"))}}else m+="\n"};return c=nx(c,[/<!\[[^\]]+\]>/g]),f(u.parse(c)),m})(a)),ex(a)||(o?Hf(t,a,r):$d(t,a,s,r))},cx=(t,e,n,o)=>{((r,a,s)=>{if(!r)return!1;try{return r.clearData(),r.setData("text/html",a),r.setData("text/plain",s),r.setData(Hd(),a),!0}catch{return!1}})(t.clipboardData,e.html,e.text)?(t.preventDefault(),o()):n(e.html,o)},dx=t=>(e,n)=>{const{dom:o,selection:r}=t,a=o.create("div",{contenteditable:"false","data-mce-bogus":"all"}),s=o.create("div",{contenteditable:"true"},e);o.setStyles(a,{position:"fixed",top:"0",left:"-3000px",width:"1000px",overflow:"hidden"}),a.appendChild(s),o.add(t.getBody(),a);const i=r.getRng();s.focus();const l=o.createRng();l.selectNodeContents(s),r.setRng(l),so.setEditorTimeout(t,()=>{r.setRng(i),o.remove(a),n()},0)},ux=t=>({html:AT(t.selection.getContent({contextual:!0})),text:t.selection.getContent({format:"text"})}),mx=t=>{return!t.selection.isCollapsed()||!!(e=t).dom.getParent(e.selection.getStart(),"td[data-mce-selected],th[data-mce-selected]",e.getBody());var e},gx=(t,e)=>{var n,o;return ss.getCaretRangeFromPoint(null!==(n=e.clientX)&&void 0!==n?n:0,null!==(o=e.clientY)&&void 0!==o?o:0,t.getDoc())},fx=(t,e)=>{t.focus(),e&&t.selection.setRng(e)},LT=/rgb\s*\(\s*([0-9]+)\s*,\s*([0-9]+)\s*,\s*([0-9]+)\s*\)/gi,px=t=>ot.trim(t).replace(LT,Fc).toLowerCase(),IT=(t,e,n)=>{const o=IE(t);if(n||"all"===o||!FE(t))return e;const r=o?o.split(/[, ]/):[];if(r&&"none"!==o){const a=t.dom,s=t.selection.getNode();e=e.replace(/(<[^>]+) style="([^"]*)"([^>]*>)/gi,(i,l,c,d)=>{const u=a.parseStyle(a.decode(c)),m={};for(let g=0;g<r.length;g++){const h=u[r[g]];let f=h,b=a.getStyle(s,r[g],!0);/color/.test(r[g])&&(f=px(f),b=px(b)),b!==f&&(m[r[g]]=h)}const p=a.serializeStyle(m,"span");return p?l+' style="'+p+'"'+d:l+d})}else e=e.replace(/(<[^>]+) style="([^"]*)"([^>]*>)/gi,"$1$3");return e.replace(/(<[^>]+) data-mce-style="([^"]+)"([^>]*>)/gi,(a,s,i,l)=>s+' style="'+i+'"'+l)},hx=t=>la(A(t)),bx=(t,e)=>{const n=e.getNode();dt(n)||t.selection.setCursorLocation(n,e.offset())},vx=(t,e)=>{var n;return 0===t.startOffset&&t.endOffset===(null===(n=e.textContent)||void 0===n?void 0:n.length)},yx=po(),Cx=yx.os,jT=yx.browser.isSafari(),wx=Cx.isMacOS()||Cx.isiOS(),qf=(t,e)=>st(t.getParent(e.container(),"details")),xx=(t,e)=>{const n=t.dom.getParent(e.container(),"details");if(n&&!n.open){const o=t.dom.select("summary",n)[0];o&&Eo(o).each(r=>bx(t,r))}else bx(t,e)},$T=_e,Sx=tt,qT=t=>ve(t.dom),kx=t=>e=>re(A(t),e),Ex=(t,e)=>Lr(A(t),qT,kx(e)),_x=(t,e,n)=>{const o=new Kt(t,e),r=n?o.next.bind(o):o.prev.bind(o);let a=t;for(let s=n?t:r();s&&!$T(s);s=r())ko(s)&&(a=s);return a};var ci;!function(t){t.Before="before",t.After="after"}(ci||(ci={}));const WT=(t,e)=>Math.abs(t.left-e),KT=(t,e)=>Math.abs(t.right-e),YT=(t,e,n)=>e>t.left&&e<t.right?0:Math.min(Math.abs(t.left-e),Math.abs(t.right-e)),Vf=(t,e,n)=>{const o=c=>ko(c.node)?S.some(c):it(c.node)?Vf(Ae(c.node.childNodes),e,n):S.none(),r=(c,d)=>{const u=To(c,(m,p)=>d(m,e,n)-d(p,e,n));return((m,p)=>{if(m.length>=2){const g=o(m[0]).getOr(m[0]),h=o(m[1]).getOr(m[1]);if(Math.abs(p(g,e,n)-p(h,e,n))<2){if(tt(g.node))return S.some(g);if(tt(h.node))return S.some(h)}}return S.none()})(u,d).orThunk(()=>Ko(u,o))},[a,s]=((t,e)=>{return(n=Ot(t,n=>{return(o=e)>=(r=n).top&&o<=r.bottom;var o,r}),Re(n,(o,r)=>o.fold(()=>S.some(r),a=>{const s=Math.min(r.left,a.left),i=Math.min(r.top,a.top),l=Math.max(r.right,a.right),c=Math.max(r.bottom,a.bottom);return S.some({top:i,right:l,bottom:c,left:s,width:l-s,height:c-i})}),S.none())).fold(()=>[[],t],n=>{const{pass:o,fail:r}=Ve(t,a=>((s,i)=>{const l=(c=s,d=i,Math.max(0,Math.min(c.bottom,d.bottom)-Math.max(c.top,d.top))/Math.min(s.height,i.height));var c,d;return((c,d)=>c.top<d.bottom&&c.bottom>d.top)(s,i)&&l>.5})(a,n));return[o,r]});var n})(e1(t),n),{pass:i,fail:l}=Ve(s,c=>c.top<n);return r(a,YT).orThunk(()=>r(l,eh)).orThunk(()=>r(i,eh))},Nx=(t,e,n)=>((o,r,a)=>{const s=A(o),l=((t,e,n)=>S.from(t.dom.elementFromPoint(e,n)).map(Dr))(yo(s),r,a).filter(c=>vo(s,c)).getOr(s);return((c,d,u,m)=>{const p=(g,h)=>{const f=Ot(g.dom.childNodes,$o(b=>it(b)&&b.classList.contains("mce-drag-container")));return h.fold(()=>Vf(f,u,m),b=>{const y=Ot(f,C=>C!==b.dom);return Vf(y,u,m)}).orThunk(()=>(re(g,c)?S.none():_i(g)).bind(b=>p(b,S.some(g))))};return p(d,S.none())})(s,l,r,a)})(t,e,n).filter(o=>Ji(o.node)).map(o=>{return{node:(r=o).node,position:WT(r,a=e)<KT(r,a)?ci.Before:ci.After};var r,a}),Rx=t=>{var e,n;const o=t.getBoundingClientRect(),r=t.ownerDocument,a=r.documentElement,s=r.defaultView;return{top:o.top+(null!==(e=s?.scrollY)&&void 0!==e?e:0)-a.clientTop,left:o.left+(null!==(n=s?.scrollX)&&void 0!==n?n:0)-a.clientLeft}},Ax=t=>({target:t,srcElement:t}),Tx=ve,QT=((...t)=>e=>{for(let n=0;n<t.length;n++)if(t[n](e))return!0;return!1})(Tx,Po),JT=(t,e,n,o)=>{const r=t.dom,a=e.cloneNode(!0);r.setStyles(a,{width:n,height:o}),r.setAttrib(a,"data-mce-selected",null);const s=r.create("div",{class:"mce-drag-container","data-mce-bogus":"all",unselectable:"on",contenteditable:"false"});return r.setStyles(s,{position:"absolute",opacity:.5,overflow:"hidden",border:0,padding:0,margin:0,width:n,height:o}),r.setStyles(a,{margin:0,boxSizing:"border-box"}),s.appendChild(a),s},qd=(t,e)=>n=>()=>{const o="left"===t?n.scrollX:n.scrollY;n.scroll({[t]:o+e,behavior:"smooth"})},Ox=qd("left",-32),Bx=qd("left",32),Dx=qd("top",-32),Px=qd("top",32),Mx=t=>{t&&t.parentNode&&t.parentNode.removeChild(t)},Dl=(t,e,n,o,r)=>{"dragstart"===e&&Qw(o,t.dom.getOuterHTML(n));const a=((t,e,n,o)=>{const r=((a,s)=>{const i=(l=>{const c=If(),d=S.from(l[Mf]);return Lf(l),Ww(c),c.dropEffect=l.dropEffect,c.effectAllowed=l.effectAllowed,(u=l,S.from(u[Vw])).each(u=>c.setDragImage(u.image,u.x,u.y)),J(l.types,u=>{"Files"!==u&&c.setData(u,l.getData(u))}),J(l.files,u=>c.items.add(u)),(u=>S.from(u[Pf]))(l).each(u=>{var m;m=c,jd(u)(m)}),d.each(u=>{Kw(l,u),Kw(c,u)}),c;var u})(a);return"dragstart"===s?(Ww(i),Gw(i)):"drop"===s?(ST(i),Lf(i)):(kT(i),_T(i)),i})(n,t);return dt(o)?((a,s,i)=>{const l=Ue("Function not supported on simulated event.");return{bubbles:!0,cancelBubble:!1,cancelable:!0,composed:!1,currentTarget:null,defaultPrevented:!1,eventPhase:0,isTrusted:!0,returnValue:!1,timeStamp:0,type:a,composedPath:l,initEvent:l,preventDefault:jt,stopImmediatePropagation:jt,stopPropagation:jt,AT_TARGET:window.Event.AT_TARGET,BUBBLING_PHASE:window.Event.BUBBLING_PHASE,CAPTURING_PHASE:window.Event.CAPTURING_PHASE,NONE:window.Event.NONE,altKey:!1,button:0,buttons:0,clientX:0,clientY:0,ctrlKey:!1,metaKey:!1,movementX:0,movementY:0,offsetX:0,offsetY:0,pageX:0,pageY:0,relatedTarget:null,screenX:0,screenY:0,shiftKey:!1,x:0,y:0,detail:0,view:null,which:0,initUIEvent:l,initMouseEvent:l,getModifierState:l,dataTransfer:i,...Ax(s)}})(t,e,r):(a=t,s=o,i=e,l=r,{...s,dataTransfer:l,type:a,...Ax(i)});var a,s,i,l})(e,n,o,r);return t.dispatch(e,a)},Lx=(t,e,n)=>{t.on(o=>{o.intervalId.clear(),o.dragging&&n.fold(()=>Dl(e,"dragend",o.element,o.dataTransfer),r=>Dl(e,"dragend",o.element,o.dataTransfer,r))}),Ix(t)},Ix=t=>{t.on(e=>{e.intervalId.clear(),Mx(e.ghost)}),t.clear()},Pl=ve,Fx=(t,e)=>ds(t.getBody(),e),eO=t=>{const e=t.selection,n=t.dom,o=t.getBody(),r=((t,e,n,o)=>{const r=ma();let a,s;const i=Bn(t),l=t.dom,c=()=>{(d=>{var u,m;const p=Rt(A(d),"*[contentEditable=false],video,audio,embed,object");for(let g=0;g<p.length;g++){const h=p[g].dom;let f=h.previousSibling;if(bc(f)){const b=f.data;1===b.length?null===(u=f.parentNode)||void 0===u||u.removeChild(f):f.deleteData(b.length-1,1)}f=h.nextSibling,hc(f)&&(1===f.data.length?null===(m=f.parentNode)||void 0===m||m.removeChild(f):f.deleteData(0,1))}})(e),s&&(ts(s),s=null),r.on(d=>{l.remove(d.caret),r.clear()}),a&&(clearInterval(a),a=void 0)};return{show:(d,u)=>{let m;if(c(),r_(u))return null;if(!n(u))return s=((p,g)=>{var h;const f=(null!==(h=p.ownerDocument)&&void 0!==h?h:document).createTextNode(dn),b=p.parentNode;if(g){const y=p.previousSibling;if(Is(y)){if(no(y))return y;if(bc(y))return y.splitText(y.data.length-1)}b?.insertBefore(f,p)}else{const y=p.nextSibling;if(Is(y)){if(no(y))return y;if(hc(y))return y.splitText(1),y}p.nextSibling?b?.insertBefore(f,p.nextSibling):b?.appendChild(f)}return f})(u,d),m=u.ownerDocument.createRange(),Qi(s.nextSibling)?(m.setStart(s,0),m.setEnd(s,0)):(m.setStart(s,1),m.setEnd(s,1)),m;{const p=((f,b,y)=>{var C;const k=(null!==(C=b.ownerDocument)&&void 0!==C?C:document).createElement(f);k.setAttribute("data-mce-caret",y?"before":"after"),k.setAttribute("data-mce-bogus","all"),k.appendChild(fc().dom);const E=b.parentNode;return y?E?.insertBefore(k,b):b.nextSibling?E?.insertBefore(k,b.nextSibling):E?.appendChild(k),k})(i,u,d),g=Yh(e,u,d);l.setStyle(p,"top",g.top),s=p;const h=l.create("div",{class:"mce-visual-caret","data-mce-bogus":"all"});l.setStyles(h,{...g}),l.add(e,h),r.set({caret:h,element:u,before:d}),d&&l.addClass(h,"mce-visual-caret-before"),a=setInterval(()=>{r.on(f=>{o()?l.toggleClass(f.caret,"mce-visual-caret-hidden"):l.addClass(f.caret,"mce-visual-caret-hidden")})},500),m=u.ownerDocument.createRange(),m.setStart(p,0),m.setEnd(p,0)}return m},hide:c,getCss:()=>".mce-visual-caret {position: absolute;background-color: black;background-color: currentcolor;}.mce-visual-caret-hidden {display: none;}*[data-mce-caret] {position: absolute;left: -1000px;right: auto;top: 0;margin: 0;padding: 0;}",reposition:()=>{r.on(d=>{const u=Yh(e,d.element,d.before);l.setStyles(d.caret,{...u})})},destroy:()=>clearInterval(a)}})(t,o,n.isBlock,()=>is(t)),a="sel-"+n.uniqueId(),s="data-mce-selected";let i;const l=f=>f!==o&&(Pl(f)||nr(f))&&n.isChildOf(f,o)&&n.isEditable(f.parentNode),c=(f,b,y,C=!0)=>t.dispatch("ShowCaret",{target:b,direction:f,before:y}).isDefaultPrevented()?null:(C&&e.scrollIntoView(b,-1===f),r.show(y,b)),d=f=>no(f)||hc(f)||bc(f),u=f=>d(f.startContainer)||d(f.endContainer),p=(f,b)=>{if(!f)return null;if(f.collapsed){if(!u(f)){const E=b?1:-1,N=nl(E,o,f),R=N.getNode(!b);if(st(R)){if(Ji(R))return c(E,R,!!b&&!N.isAtEnd(),!1);if(ca(R)&&ve(R.nextSibling)){const q=n.createRng();return q.setStart(R,0),q.setEnd(R,0),q}}const I=N.getNode(b);if(st(I)){if(Ji(I))return c(E,I,!b&&!N.isAtEnd(),!1);if(ca(I)&&ve(I.previousSibling)){const q=n.createRng();return q.setStart(I,1),q.setEnd(I,1),q}}}return null}let y=f.startContainer,C=f.startOffset;const k=f.endOffset;if(tt(y)&&0===C&&Pl(y.parentNode)&&(y=y.parentNode,C=n.nodeIndex(y),y=y.parentNode),!it(y))return null;if(k===C+1&&y===f.endContainer){const E=y.childNodes[C];if(l(E))return(N=>{const R=N.cloneNode(!0),I=t.dispatch("ObjectSelected",{target:N,targetClone:R});if(I.isDefaultPrevented())return null;const q=((T,P)=>{const B=A(t.getBody()),j=t.getDoc(),lt=Ua(B,"#"+a).getOrThunk(()=>{const mt=Ma('<div data-mce-bogus="all" class="mce-offscreen-selection"></div>',j);return Me(mt,"id",a),Se(B,mt),mt}),Z=n.createRng();x(lt),v(lt,[bo(Ge,j),A(P),bo(Ge,j)]),Z.setStart(lt.dom.firstChild,1),Z.setEnd(lt.dom.lastChild,0),Ps(lt,{top:n.getPos(T,t.getBody()).y+"px"}),uv(lt);const _t=e.getSel();return _t&&(_t.removeAllRanges(),_t.addRange(Z)),Z})(N,I.targetClone),H=A(N);return J(Rt(A(t.getBody()),`*[${s}]`),T=>{re(H,T)||Le(T,s)}),n.getAttrib(N,s)||N.setAttribute(s,"1"),i=N,h(),q})(E)}return null},g=()=>{i&&i.removeAttribute(s),Ua(A(t.getBody()),"#"+a).each(w),i=null},h=()=>{r.hide()};return dr(t)||(t.on("click",f=>{n.isEditable(f.target)||(f.preventDefault(),t.focus())}),t.on("blur NewBlock",g),t.on("ResizeWindow FullscreenStateChanged",r.reposition),t.on("tap",f=>{const b=f.target,y=Fx(t,b);Pl(y)?(f.preventDefault(),wl(t,y).each(p)):l(b)&&wl(t,b).each(p)},!0),t.on("mousedown",f=>{const b=f.target;if(b!==o&&"HTML"!==b.nodeName&&!n.isChildOf(b,o)||!((C,k,E)=>{const N=A(C.getBody()),R=C.inline?N:A(yo(N).dom.documentElement),I=((q,H,T,P)=>{const B=H.dom.getBoundingClientRect();return{x:T-(q?B.left+H.dom.clientLeft+RR(H):0),y:P-(q?B.top+H.dom.clientTop+NR(H):0)}})(C.inline,R,k,E);return((q,H,T)=>{const P=ER(q),B=_R(q);return H>=0&&T>=0&&H<=P&&T<=B})(R,I.x,I.y)})(t,f.clientX,f.clientY))return;g(),h();const y=Fx(t,b);Pl(y)?(f.preventDefault(),wl(t,y).each(p)):Nx(o,f.clientX,f.clientY).each(C=>{var k;f.preventDefault(),(k=c(1,C.node,C.position===ci.Before,!1))&&e.setRng(k),it(y)?y.focus():t.getBody().focus()})}),t.on("keypress",f=>{wt.modifierPressed(f)||Pl(e.getNode())&&f.preventDefault()}),t.on("GetSelectionRange",f=>{let b=f.range;if(i){if(!i.parentNode)return void(i=null);b=b.cloneRange(),b.selectNode(i),f.range=b}}),t.on("SetSelectionRange",f=>{f.range=(f=>{const b=t.schema.getVoidElements(),y=n.createRng(),C=f.startContainer,k=f.startOffset,E=f.endContainer,N=f.endOffset;return It(b,C.nodeName.toLowerCase())?0===k?y.setStartBefore(C):y.setStartAfter(C):y.setStart(C,k),It(b,E.nodeName.toLowerCase())?0===N?y.setEndBefore(E):y.setEndAfter(E):y.setEnd(E,N),y})(f.range);const b=p(f.range,f.forward);b&&(f.range=b)}),t.on("AfterSetSelectionRange",f=>{const b=f.range,y=b.startContainer.parentElement;var C,k;u(b)||it(C=y)&&"mcepastebin"===C.id||h(),st(k=y)&&n.hasClass(k,"mce-offscreen-selection")||g()}),(t=>{const e=ma(),n=me.DOM,o=document,r=((l,c)=>d=>{if(0===d.button){const u=he(c.dom.getParents(d.target),QT).getOr(null);if(st(u)&&(m=c.dom,p=c.getBody(),Tx(g=u)&&g!==p&&m.isEditable(g.parentElement))){const m=c.dom.getPos(u),p=c.getBody(),g=c.getDoc().documentElement;l.set({element:u,dataTransfer:If(),dragging:!1,screenX:d.screenX,screenY:d.screenY,maxX:(c.inline?p.scrollWidth:g.offsetWidth)-2,maxY:(c.inline?p.scrollHeight:g.offsetHeight)-2,relX:d.pageX-m.x,relY:d.pageY-m.y,width:u.offsetWidth,height:u.offsetHeight,ghost:JT(c,u,u.offsetWidth,u.offsetHeight),intervalId:_k(100)})}}var m,p,g})(e,t),a=((t,e)=>{const n=Ec((r,a)=>{return i=r,l=a,(s=e)._selectionOverrides.hideFakeCaret(),void Nx(s.getBody(),i,l).fold(()=>s.selection.placeCaretAt(i,l),c=>{const d=s._selectionOverrides.showCaret(1,c.node,c.position===ci.Before,!1);d?s.selection.setRng(d):s.selection.placeCaretAt(i,l)});var s,i,l},0);e.on("remove",n.cancel);const o=t;return r=>t.on(a=>{const s=Math.max(Math.abs(r.screenX-a.screenX),Math.abs(r.screenY-a.screenY));if(!a.dragging&&s>10){const c=Dl(e,"dragstart",a.element,a.dataTransfer,r);if(st(c.dataTransfer)&&(a.dataTransfer=c.dataTransfer),c.isDefaultPrevented())return;a.dragging=!0,e.focus()}if(a.dragging){const c=r.currentTarget===e.getDoc().documentElement,d=((u,m)=>({pageX:m.pageX-u.relX,pageY:m.pageY+5}))(a,(m=r,p=(f=u=e).inline?Rx(f.getBody()):{left:0,top:0},g=(f=>{const b=f.getBody();return f.inline?{left:b.scrollLeft,top:b.scrollTop}:{left:0,top:0}})(u),h=((f,b)=>{if(b.target.ownerDocument!==f.getDoc()){const y=Rx(f.getContentAreaContainer()),C=(k=>{const E=k.getBody(),N=k.getDoc().documentElement;return k.inline?{left:E.scrollLeft,top:E.scrollTop}:{left:E.scrollLeft||N.scrollLeft,top:E.scrollTop||N.scrollTop}})(f);return{left:b.pageX-y.left+C.left,top:b.pageY-y.top+C.top}}return{left:b.pageX,top:b.pageY}})(u,m),{pageX:h.left-p.left+g.left,pageY:h.top-p.top+g.top}));i=a.ghost,l=e.getBody(),i.parentNode!==l&&l.appendChild(i),((u,m,p,g,h,f,b,y,C,k,E,N)=>{let R=0,I=0;u.style.left=m.pageX+"px",u.style.top=m.pageY+"px",m.pageX+p>h&&(R=m.pageX+p-h),m.pageY+g>f&&(I=m.pageY+g-f),u.style.width=p-R+"px",u.style.height=g-I+"px";const q=C.clientHeight,H=C.clientWidth,T=b+C.getBoundingClientRect().top,P=y+C.getBoundingClientRect().left;E.on(B=>{B.intervalId.clear(),B.dragging&&N&&(b+8>=q?B.intervalId.set(Px(k)):b-8<=0?B.intervalId.set(Dx(k)):y+8>=H?B.intervalId.set(Bx(k)):y-8<=0?B.intervalId.set(Ox(k)):T+16>=window.innerHeight?B.intervalId.set(Px(window)):T-16<=0?B.intervalId.set(Dx(window)):P+16>=window.innerWidth?B.intervalId.set(Bx(window)):P-16<=0&&B.intervalId.set(Ox(window)))})})(a.ghost,d,a.width,a.height,a.maxX,a.maxY,r.clientY,r.clientX,e.getContentAreaContainer(),e.getWin(),o,c),n.throttle(r.clientX,r.clientY)}var u,m,f,p,g,h,i,l})})(e,t),s=((l,c)=>d=>{l.on(u=>{var m,p,g,h;if(u.intervalId.clear(),u.dragging){if(p=c,g=(p=>{const g=p.getSel();if(st(g)){const h=g.getRangeAt(0).startContainer;return tt(h)?h.parentNode:h}return null})(c.selection),h=u.element,!pe(g)&&g!==h&&!p.dom.isChildOf(g,h)&&p.dom.isEditable(g)){const p=null!==(m=c.getDoc().elementFromPoint(d.clientX,d.clientY))&&void 0!==m?m:c.getBody();Dl(c,"drop",p,u.dataTransfer,d).isDefaultPrevented()||c.undoManager.transact(()=>{((g,h)=>{const f=g.getParent(h.parentNode,g.isBlock);Mx(h),f&&f!==g.getRoot()&&g.isEmpty(f)&&la(A(f))})(c.dom,u.element),(g=>{const h=g.getData("text/html");return""===h?S.none():S.some(h)})(u.dataTransfer).each(g=>c.insertContent(g)),c._selectionOverrides.hideFakeCaret()})}Dl(c,"dragend",c.getBody(),u.dataTransfer,d)}}),Ix(l)})(e,t),i=((l,c)=>d=>Lx(l,c,S.some(d)))(e,t);t.on("mousedown",r),t.on("mousemove",a),t.on("mouseup",s),n.bind(o,"mousemove",a),n.bind(o,"mouseup",i),t.on("remove",()=>{n.unbind(o,"mousemove",a),n.unbind(o,"mouseup",i)}),t.on("keydown",l=>{l.keyCode===wt.ESC&&Lx(e,t,S.none())})})(f=t),SE(f)&&(b=>{const y=E=>{if(!E.isDefaultPrevented()){const N=E.dataTransfer;N&&(Lt(N.types,"Files")||N.files.length>0)&&(E.preventDefault(),"drop"===E.type&&hd(b,"Dropped file type is not supported"))}},C=E=>{Yc(b,E.target)&&y(E)},k=()=>{const E=me.DOM,N=b.dom,R=document,I=b.inline?b.getBody():b.getDoc(),q=["drop","dragover"];J(q,H=>{E.bind(R,H,C),N.bind(I,H,y)}),b.on("remove",()=>{J(q,H=>{E.unbind(R,H,C),N.unbind(I,H,y)})})};b.on("init",()=>{so.setEditorTimeout(b,k,0)})})(f),(f=>{const b=Ec(()=>{if(!f.removed&&f.getBody().contains(document.activeElement)){const y=f.selection.getRng();if(y.collapsed){const C=Kg(f,y,!1);f.selection.setRng(C)}}},0);f.on("focus",()=>{b.throttle()}),f.on("blur",()=>{b.cancel()})})(t),(f=>{f.on("init",()=>{f.on("focusin",b=>{const y=b.target;if(nr(y)){const C=ds(f.getBody(),y),k=ve(C)?C:y;f.selection.getNode()!==k&&wl(f,k).each(E=>f.selection.setRng(E))}})})})(t)),{showCaret:c,showBlockCaretContainer:f=>{f.hasAttribute("data-mce-caret")&&(yu(f),e.scrollIntoView(f))},hideFakeCaret:h,destroy:()=>{r.destroy(),i=null}};var f},nO=(t,e)=>{let n=e;for(let o=t.previousSibling;tt(o);o=o.previousSibling)n+=o.data.length;return n},Ux=(t,e,n,o,r)=>{if(tt(n)&&(o<0||o>n.data.length))return[];const a=r&&tt(n)?[nO(n,o)]:[o];let s=n;for(;s!==e&&s.parentNode;)a.push(t.nodeIndex(s,r)),s=s.parentNode;return s===e?a.reverse():[]},Wf=(t,e,n,o,r,a,s=!1)=>({start:Ux(t,e,n,o,s),end:Ux(t,e,r,a,s)}),jx=(t,e)=>{const n=e.slice(),o=n.pop();return nn(o)?Re(n,(r,a)=>r.bind(s=>S.from(s.childNodes[a])),S.some(t)).bind(r=>tt(r)&&(o<0||o>r.data.length)?S.none():S.some({node:r,offset:o})):S.none()},zx=(t,e)=>jx(t,e.start).bind(({node:n,offset:o})=>jx(t,e.end).map(({node:r,offset:a})=>{const s=document.createRange();return s.setStart(n,o),s.setEnd(r,a),s})),di=(t,e,n)=>{if(e&&t.isEmpty(e)&&!n(e)){const o=e.parentNode;t.remove(e),di(t,o,n)}},Vd=(t,e,n,o=!0)=>{const r=e.startContainer.parentNode,a=e.endContainer.parentNode;e.deleteContents(),o&&!n(e.startContainer)&&(tt(e.startContainer)&&0===e.startContainer.data.length&&t.remove(e.startContainer),tt(e.endContainer)&&0===e.endContainer.data.length&&t.remove(e.endContainer),di(t,r,n),r!==a&&di(t,a,n))},Kf=(t,e)=>S.from(t.dom.getParent(e.startContainer,t.dom.isBlock)),Hx=(t,e,n)=>{const o=t.dynamicPatternsLookup({text:n,block:e});return{...t,blockPatterns:kh(o).concat(t.blockPatterns),inlinePatterns:Eh(o).concat(t.inlinePatterns)}},$x=(t,e,n,o)=>{const r=t.createRng();return r.setStart(e,0),r.setEnd(n,o),r.toString()},qx=(t,e,n)=>{((o,r,a)=>{if(tt(o)&&0>=o.length)return S.some(jo(o,0));{const s=Qa(_d);return S.from(s.forwards(o,0,Nd(o),a)).map(i=>jo(i.container,0))}})(e,0,e).each(o=>{const r=o.container;ow(r,n.start.length,e).each(i=>{const l=t.createRng();l.setStart(r,0),l.setEnd(i.container,i.offset),Vd(t,l,c=>c===e)});const a=A(r),s=uc(a);var i,l;/^\s[^\s]/.test(s)&&(i=a,l=s.slice(1),pu.set(i,l))})},Vx=(t,e)=>t.create("span",{"data-mce-type":"bookmark",id:e}),Wd=(t,e)=>{const n=t.createRng();return n.setStartAfter(e.start),n.setEndBefore(e.end),n},Wx=(t,e,n)=>{const o=zx(t.getRoot(),n).getOrDie("Unable to resolve path range"),r=o.startContainer,a=o.endContainer,s=0===o.endOffset?a:a.splitText(o.endOffset),i=0===o.startOffset?r:r.splitText(o.startOffset),l=i.parentNode;return{prefix:e,end:s.parentNode.insertBefore(Vx(t,e+"-end"),s),start:l.insertBefore(Vx(t,e+"-start"),i)}},Kx=(t,e,n)=>{di(t,t.get(e.prefix+"-end"),n),di(t,t.get(e.prefix+"-start"),n)},Gf=t=>0===t.start.length,Gx=(t,e,n,o)=>{const r=e.start;var a;return hf(t,o.container,o.offset,(a=r,(s,i)=>{const l=s.data.substring(0,i),c=l.lastIndexOf(a.charAt(a.length-1)),d=l.lastIndexOf(a);return-1!==d?d+a.length:-1!==c?c+1:-1}),n).bind(s=>{var i,l;const c=null!==(l=null===(i=n.textContent)||void 0===i?void 0:i.indexOf(r))&&void 0!==l?l:-1;if(-1!==c&&s.offset>=c+r.length){const d=t.createRng();return d.setStart(s.container,s.offset-r.length),d.setEnd(s.container,s.offset),S.some(d)}return pf(s.container,s.offset-r.length,n).map(u=>{const m=t.createRng();return m.setStart(u.container,u.offset),m.setEnd(s.container,s.offset),m}).filter(u=>u.toString()===r).orThunk(()=>Gx(t,e,n,jo(s.container,0)))})},oO=(t,e,n,o)=>{const r=t.dom,a=r.getRoot(),s=n.pattern,i=n.position.container,l=n.position.offset;return pf(i,l-n.pattern.end.length,e).bind(c=>{const d=Wf(r,a,c.container,c.offset,i,l,o);if(Gf(s))return S.some({matches:[{pattern:s,startRng:d,endRng:d}],position:c});{const u=Kd(t,n.remainingPatterns,c.container,c.offset,e,o),m=u.getOr({matches:[],position:c}),p=m.position;return((h,f,b,y,C,k=!1)=>{if(0===f.start.length&&!k){const E=h.createRng();return E.setStart(b,y),E.setEnd(b,y),S.some(E)}return ff(b,y,C).bind(E=>Gx(h,f,C,E).bind(N=>{var R;return k&&(N.endContainer===E.container&&N.endOffset===E.offset||0===E.offset&&(null===(R=N.endContainer.textContent)||void 0===R?void 0:R.length)===N.endOffset)?S.none():S.some(N)}))})(r,s,p.container,p.offset,e,u.isNone()).map(h=>{const f=((b,y,C,k=!1)=>Wf(b,y,C.startContainer,C.startOffset,C.endContainer,C.endOffset,k))(r,a,h,o);return{matches:m.matches.concat([{pattern:s,startRng:f,endRng:d}]),position:jo(h.startContainer,h.startOffset)}})}})},Kd=(t,e,n,o,r,a)=>{const s=t.dom;return ff(n,o,s.getRoot()).bind(i=>{const l=$x(s,r,n,o);for(let c=0;c<e.length;c++){const d=e[c];if(!pt(l,d.end))continue;const u=e.slice();u.splice(c,1);const m=oO(t,r,{pattern:d,remainingPatterns:u,position:i},a);if(m.isNone()&&o>0)return Kd(t,e,n,o-1,r,a);if(m.isSome())return m}return S.none()})},Yx=(t,e,n)=>{t.selection.setRng(n),"inline-format"===e.type?J(e.format,o=>{t.formatter.apply(o)}):t.execCommand(e.cmd,!1,e.value)},Xx=(t,e,n,o,r,a)=>{var s;return((i,l)=>{const c=_n(i,d=>ie(l,u=>d.pattern.start===u.pattern.start&&d.pattern.end===u.pattern.end));return i.length===l.length?c?i:l:i.length>l.length?i:l})(Kd(t,r.inlinePatterns,n,o,e,a).fold(()=>[],i=>i.matches),Kd(t,(s=r.inlinePatterns,To(s,(i,l)=>l.end.length-i.end.length)),n,o,e,a).fold(()=>[],i=>i.matches))},Qx=(t,e)=>{if(0===e.length)return;const n=t.dom,o=t.selection.getBookmark(),r=((a,s)=>{const i=qi("mce_textpattern"),l=Kn(s,(c,d)=>{const u=Wx(a,i+`_end${c.length}`,d.endRng);return c.concat([{...d,endMarker:u}])},[]);return Kn(l,(c,d)=>{const u=l.length-c.length-1,m=Gf(d.pattern)?d.endMarker:Wx(a,i+`_start${u}`,d.startRng);return c.concat([{...d,startMarker:m}])},[])})(n,e);J(r,a=>{const s=n.getParent(a.startMarker.start,n.isBlock),i=l=>l===s;Gf(a.pattern)?((l,c,d,u)=>{const m=Wd(l.dom,d);Vd(l.dom,m,u),Yx(l,c,m)})(t,a.pattern,a.endMarker,i):((l,c,d,u,m)=>{const p=l.dom,g=Wd(p,u),h=Wd(p,d);Vd(p,h,m),Vd(p,g,m);const b=Wd(p,{prefix:d.prefix,start:d.end,end:u.start});Yx(l,c,b)})(t,a.pattern,a.startMarker,a.endMarker,i),Kx(n,a.endMarker,i),Kx(n,a.startMarker,i)}),t.selection.moveToBookmark(o)},Jx=(t,e,n)=>{for(let o=0;o<t.length;o++)if(n(t[o],e))return!0;return!1},Yf=me.DOM,sO=t=>t.inline?t.getElement().nodeName.toLowerCase():void 0,Xf=t=>Go(t,e=>!1===dt(e)),Zx=t=>{const e=t.options.get,n=t.editorUpload.blobCache;return Xf({allow_conditional_comments:e("allow_conditional_comments"),allow_html_data_urls:e("allow_html_data_urls"),allow_svg_data_urls:e("allow_svg_data_urls"),allow_html_in_named_anchor:e("allow_html_in_named_anchor"),allow_script_urls:e("allow_script_urls"),allow_unsafe_link_target:e("allow_unsafe_link_target"),convert_fonts_to_spans:e("convert_fonts_to_spans"),fix_list_elements:e("fix_list_elements"),font_size_legacy_values:e("font_size_legacy_values"),forced_root_block:e("forced_root_block"),forced_root_block_attrs:e("forced_root_block_attrs"),preserve_cdata:e("preserve_cdata"),inline_styles:e("inline_styles"),root_name:sO(t),sanitize:e("xss_sanitization"),validate:!0,blob_cache:n,document:t.getDoc()})},tS=t=>{const e=t.options.get;return Xf({custom_elements:e("custom_elements"),extended_valid_elements:e("extended_valid_elements"),invalid_elements:e("invalid_elements"),invalid_styles:e("invalid_styles"),schema:e("schema"),valid_children:e("valid_children"),valid_classes:e("valid_classes"),valid_elements:e("valid_elements"),valid_styles:e("valid_styles"),verify_html:e("verify_html"),padd_empty_block_inline_children:e("format_empty_lines")})},eS=t=>t.inline?t.ui.styleSheetLoader:t.dom.styleSheetLoader,nS=t=>{const e=eS(t),n=Lh(t),o=t.contentCSS,r=()=>{e.unloadAll(o),t.inline||t.ui.styleSheetLoader.unloadAll(n)},a=()=>{t.removed?r():t.on("remove",r)};if(t.contentStyles.length>0){let l="";ot.each(t.contentStyles,c=>{l+=c+"\r\n"}),t.dom.addStyle(l)}const s=Promise.all(((l,c,d)=>{const u=[eS(l).loadAll(c)];return l.inline?u:u.concat([l.ui.styleSheetLoader.loadAll(d)])})(t,o,n)).then(a).catch(a),i=nE(t);return i&&((l,c)=>{const d=A(l.getBody()),u=Oi(Co(d)),m=Be("style");Me(m,"type","text/css"),Se(m,bo(c)),Se(u,m),l.on("remove",()=>{w(m)})})(t,i),s},Qf=t=>{var e;!0!==t.removed&&(dr(e=t)||e.load({initial:!0,format:"html"}),e.startContent=e.getContent({format:"raw"}),(e=>{e.bindPendingEventDelegates(),e.initialized=!0,e.dispatch("Init"),e.focus(!0),(o=>{const r=o.dom.getRoot();o.inline||Uc(o)&&o.selection.getStart(!0)!==r||Cn(r).each(a=>{const s=a.getNode(),i=Ir(s)?Cn(s).getOr(a):a;o.selection.setRng(i.toRange())})})(e),e.nodeChanged({initial:!0});const n=RE(e);Yt(n)&&n.call(e,e),(o=>{const r=TE(o);r&&so.setEditorTimeout(o,()=>{let a;a=!0===r?o:o.editorManager.get(r),a&&!a.destroyed&&(a.focus(),a.selection.scrollIntoView())},100)})(e)})(t))},oS=t=>{const e=t.getElement();let n=t.getDoc();t.inline&&(Yf.addClass(e,"mce-content-body"),t.contentDocument=n=document,t.contentWindow=window,t.bodyElement=e,t.contentAreaContainer=e);const o=t.getBody();var s;o.disabled=!0,t.readonly=Uh(t),t._editableRoot=wE(t),!t.readonly&&t.hasEditableRoot()&&(t.inline&&"static"===Yf.getStyle(o,"position",!0)&&(o.style.position="relative"),o.contentEditable="true"),o.disabled=!1,t.editorUpload=(t=>{const e=(()=>{let h=[];const f=C=>{if(!C.blob||!C.base64)throw new Error("blob and base64 representations of the image are required for BlobInfo to be created");const k=C.id||"blobid"+TR+++(()=>{const I=()=>Math.round(4294967295*Math.random()).toString(36);return"s"+(new Date).getTime().toString(36)+I()+I()+I()})(),E=C.name||k,N=C.blob;var R;return{id:ut(k),name:ut(E),filename:ut(C.filename||E+"."+(R=N.type,{"image/jpeg":"jpg","image/jpg":"jpg","image/gif":"gif","image/png":"png","image/apng":"apng","image/avif":"avif","image/svg+xml":"svg","image/webp":"webp","image/bmp":"bmp","image/tiff":"tiff"}[R.toLowerCase()]||"dat")),blob:ut(N),base64:ut(C.base64),blobUri:ut(C.blobUri||URL.createObjectURL(N)),uri:ut(C.uri)}},b=C=>he(h,C).getOrUndefined(),y=C=>b(k=>k.id()===C);return{create:(C,k,E,N,R)=>{if(Nt(C))return f({id:C,name:N,filename:R,blob:k,base64:E});if(At(C))return f(C);throw new Error("Unknown input type")},add:C=>{y(C.id())||h.push(C)},get:y,getByUri:C=>b(k=>k.blobUri()===C),getByData:(C,k)=>b(E=>E.base64()===C&&E.blob().type===k),findFirst:b,removeByUri:C=>{h=Ot(h,k=>k.blobUri()!==C||(URL.revokeObjectURL(k.blobUri()),!1))},destroy:()=>{J(h,C=>{URL.revokeObjectURL(C.blobUri())}),h=[]}}})();let n,o;const r=pC(),a=[],s=h=>f=>t.selection?h(f):[],i=(h,f,b)=>{let y=0;do{y=h.indexOf(f,y),-1!==y&&(h=h.substring(0,y)+b+h.substr(y+f.length),y+=b.length-f.length+1)}while(-1!==y);return h},l=(h,f,b)=>(h=i(h,`src="${f}"`,`src="${b}"${b===Jt.transparentSrc?' data-mce-placeholder="1"':""}`),i(h,'data-mce-src="'+f+'"','data-mce-src="'+b+'"')),c=(h,f)=>{J(t.undoManager.data,b=>{"fragmented"===b.type?b.fragments=te(b.fragments,y=>l(y,h,f)):b.content=l(b.content,h,f)})},d=()=>(n||(n=bC(t,r)),p().then(s(h=>{const f=te(h,b=>b.blobInfo);return n.upload(f,hC(t)).then(s(b=>{const y=[];let C=!1;const k=te(b,(E,N)=>{const{blobInfo:R,image:I}=h[N];let q=!1;return E.status&&Wk(t)?(E.url&&!ct(I.src,E.url)&&(C=!0),e.removeByUri(I.src),dr(t)||((H,T)=>{const P=t.convertURL(T,"src");var B;c(H.src,T),Do(A(H),{src:Bh(t)?(B=T,B+(-1===B.indexOf("?")?"?":"&")+(new Date).getTime()):T,"data-mce-src":P})})(I,E.url)):E.error&&(E.error.remove&&(c(I.src,Jt.transparentSrc),y.push(I),q=!0),hd(t,Mo.translate(["Failed to upload image: {0}",E.error.message]))),{element:I,status:E.status,uploadUri:E.url,blobInfo:R,removed:q}});return y.length>0&&!dr(t)?t.undoManager.transact(()=>{J(M(y),E=>{const N=Tn(E);w(E),N.each((R=>I=>{var q,H;(q=R).dom.isEmpty((H=I).dom)&&st(q.schema.getTextBlockElements()[ge(H)])&&Se(I,Ma('<br data-mce-bogus="1" />'))})(t)),e.removeByUri(E.dom.src)})}):C&&t.undoManager.dispatchChange(),k}))}))),u=()=>Oh(t)?d():Promise.resolve([]),m=h=>_n(a,f=>f(h)),p=()=>(o||(o=((t,e)=>{const n={};return{findAll:(o,r=ye)=>{const a=Ot((i=o)?Ae(i.getElementsByTagName("img")):[],i=>{const l=i.src;return!i.hasAttribute("data-mce-bogus")&&!i.hasAttribute("data-mce-placeholder")&&!(!l||l===Jt.transparentSrc)&&(ht(l,"blob:")?!t.isUploaded(l)&&r(i):!!ht(l,"data:")&&r(i))}),s=te(a,i=>{const l=i.src;if(It(n,l))return n[l].then(c=>Nt(c)?c:{image:i,blobInfo:c.blobInfo});{const c=((d,u)=>{const m=()=>Promise.reject("Invalid data URI");if(ht(u,"blob:")){const h=d.getByUri(u);return st(h)?Promise.resolve(h):(p=u,ht(p,"blob:")?(f=p,fetch(f).then(b=>b.ok?b.blob():Promise.reject()).catch(()=>Promise.reject({message:`Cannot convert ${f} to Blob. Resource might not exist or is inaccessible.`,uriType:"blob"}))):ht(p,"data:")?(g=p,new Promise((f,b)=>{Sg(g).bind(({type:y,data:C,base64Encoded:k})=>by(y,C,k)).fold(()=>b("Invalid data URI"),f)})):Promise.reject("Unknown URI format")).then(f=>vy(f).then(b=>yy(b,!1,y=>S.some(Cy(d,f,y))).getOrThunk(m)))}var f,p,g;return ht(u,"data:")?wy(d,u).fold(m,h=>Promise.resolve(h)):Promise.reject("Unknown image data format")})(e,l).then(d=>(delete n[l],{image:i,blobInfo:d})).catch(d=>(delete n[l],d));return n[l]=c,c}});var i;return Promise.all(s)}}})(r,e)),o.findAll(t.getBody(),m).then(s(h=>{const f=Ot(h,b=>Nt(b)?(hd(t,b),!1):"blob"!==b.uriType);return dr(t)||J(f,b=>{c(b.image.src,b.blobInfo.blobUri()),b.image.src=b.blobInfo.blobUri(),b.image.removeAttribute("data-mce-src")}),f}))),g=h=>h.replace(/src="(blob:[^"]+)"/g,(f,b)=>{const y=r.getResultUri(b);if(y)return'src="'+y+'"';let C=e.getByUri(b);return C||(C=Re(t.editorManager.get(),(k,E)=>k||E.editorUpload&&E.editorUpload.blobCache.getByUri(b),void 0)),C?'src="data:'+C.blob().type+";base64,"+C.base64()+'"':f});return t.on("SetContent",()=>{Oh(t)?u():p()}),t.on("RawSaveContent",h=>{h.content=g(h.content)}),t.on("GetContent",h=>{h.source_view||"raw"===h.format||"tree"===h.format||(h.content=g(h.content))}),t.on("PostRender",()=>{t.parser.addNodeFilter("img",h=>{J(h,f=>{const b=f.attr("src");if(!b||e.getByUri(b))return;const y=r.getResultUri(b);y&&f.attr("src",y)})})}),{blobCache:e,addFilter:h=>{a.push(h)},uploadImages:d,uploadImagesAuto:u,scanForImages:p,destroy:()=>{e.destroy(),r.destroy(),o=n=null}}})(t),t.schema=ua(tS(t)),t.dom=me(n,{keep_values:!0,url_converter:t.convertURL,url_converter_scope:t,update_styles:!0,root_element:t.inline?t.getBody():null,collect:t.inline,schema:t.schema,contentCssCors:Jk(t),referrerPolicy:Ku(t),onSetAttrib:s=>{t.dispatch("SetAttrib",s)}}),t.parser=(s=>{const i=ti(Zx(s),s.schema);return i.addAttributeFilter("src,href,style,tabindex",(l,c)=>{const d=s.dom,u="data-mce-"+c;let m=l.length;for(;m--;){const p=l[m];let g=p.attr(c);if(g&&!p.attr(u)){if(0===g.indexOf("data:")||0===g.indexOf("blob:"))continue;"style"===c?(g=d.serializeStyle(d.parseStyle(g),p.name),g.length||(g=null),p.attr(u,g),p.attr(c,g)):"tabindex"===c?(p.attr(u,g),p.attr(c,null)):p.attr(u,s.convertURL(g,c,p.name))}}}),i.addNodeFilter("script",l=>{let c=l.length;for(;c--;){const d=l[c],u=d.attr("type")||"no/type";0!==u.indexOf("mce-")&&d.attr("type","mce-"+u)}}),GE(s)&&i.addNodeFilter("#cdata",l=>{var c;let d=l.length;for(;d--;){const u=l[d];u.type=8,u.name="#comment",u.value="[CDATA["+s.dom.encode(null!==(c=u.value)&&void 0!==c?c:"")+"]]"}}),i.addNodeFilter("p,h1,h2,h3,h4,h5,h6,div",l=>{let c=l.length;const d=s.schema.getNonEmptyElements();for(;c--;){const u=l[c];u.isEmpty(d)&&0===u.getAll("br").length&&u.append(new Pn("br",1))}}),i})(t),t.serializer=aC((s=>{const i=s.options.get;return{...Zx(s),...tS(s),...Xf({remove_trailing_brs:i("remove_trailing_brs"),url_converter:i("url_converter"),url_converter_scope:i("url_converter_scope"),element_format:i("element_format"),entities:i("entities"),entity_encoding:i("entity_encoding"),indent:i("indent"),indent_after:i("indent_after"),indent_before:i("indent_before")})}})(t),t),t.selection=rC(t.dom,t.getWin(),t.serializer,t),t.annotator=Fb(t),t.formatter=CC(t),t.undoManager=xC(t),t._nodeChangeDispatcher=new xT(t),t._selectionOverrides=eO(t),(s=>{const i=ma(),l=Ye(!1),c=Lu(d=>{s.dispatch("longpress",{...d,type:"longpress"}),l.set(!0)},400);s.on("touchstart",d=>{N1(d).each(u=>{c.cancel();const m={x:u.clientX,y:u.clientY,target:d.target};c.throttle(d),l.set(!1),i.set(m)})},!0),s.on("touchmove",d=>{c.cancel(),N1(d).each(u=>{i.on(m=>{((p,g)=>{const h=Math.abs(p.clientX-g.x),f=Math.abs(p.clientY-g.y);return h>5||f>5})(u,m)&&(i.clear(),l.set(!1),s.dispatch("longpresscancel"))})})},!0),s.on("touchend touchcancel",d=>{c.cancel(),"touchcancel"!==d.type&&i.get().filter(u=>u.target.isEqualNode(d.target)).each(()=>{l.get()?d.preventDefault():s.dispatch("tap",{...d,type:"tap"})})},!0)})(t),(t=>{var e;(e=t).on("click",n=>{e.dom.getParent(n.target,"details")&&n.preventDefault()}),(e=>{e.parser.addNodeFilter("details",n=>{const o=JE(e);J(n,r=>{"expanded"===o?r.attr("open","open"):"collapsed"===o&&r.attr("open",null)})}),e.serializer.addNodeFilter("details",n=>{const o=ZE(e);J(n,r=>{"expanded"===o?r.attr("open","open"):"collapsed"===o&&r.attr("open",null)})})})(t),(e=>{e.on("keydown",n=>{n.keyCode!==wt.BACKSPACE&&n.keyCode!==wt.DELETE||((o,r)=>{const a=S.from(o.getParent(r.startContainer,"details")),s=S.from(o.getParent(r.endContainer,"details"));if(a.isSome()||s.isSome()){const i=a.bind(l=>S.from(o.select("summary",l)[0]));return S.some({startSummary:i,startDetails:a,endDetails:s})}return S.none()})(e.dom,e.selection.getRng()).fold(()=>{((o,r)=>{const{dom:a,selection:s}=o,i=o.getBody();if(o.selection.isCollapsed()){const l=$.fromRangeStart(s.getRng()),c=a.getParent(l.container(),a.isBlock);if(c&&a.isEmpty(c))if(Oe(c.nextSibling)){const d=Dn(i,l).filter(u=>qf(a,u));if(d.isSome())return d.each(u=>{r||xx(o,u)}),!0}else if(Oe(c.previousSibling)&&yn(i,l).filter(d=>qf(a,d)))return!0;return ol(r,i,l).fold(de,d=>!!qf(a,d)&&(c&&a.isEmpty(c)&&o.dom.remove(c),r||xx(o,d),!0))}return!1})(e,n.keyCode===wt.DELETE)&&n.preventDefault()},o=>{((t,e,n)=>{const o=t.selection,r=o.getNode(),a=o.getRng(),s=n.keyCode===wt.BACKSPACE,i=n.keyCode===wt.DELETE,l=t.selection.isCollapsed(),c=$.fromRangeStart(a),d=t.getBody();return!((l||!((u,m)=>{const p=m.startSummary.exists(f=>f.contains(u.startContainer)),g=m.startSummary.exists(f=>f.contains(u.endContainer)),h=m.startDetails.forall(f=>m.endDetails.forall(b=>f!==b));return(p||g)&&!(p&&g)||h})(a,e))&&!(l&&s&&(u=c,m=e,m.startSummary.exists(p=>{return g=u,Cn(p).exists(f=>f.isEqual(g));var g})))&&!(l&&i&&((u,m)=>m.startSummary.exists(p=>{return g=u,Eo(h=p).exists(f=>_e(f.getNode())&&Dn(h,f).exists(b=>b.isEqual(g))||f.isEqual(g));var g,h}))(c,e))&&!(l&&s&&((u,m)=>m.startDetails.exists(p=>Dn(p,u).forall(g=>m.startSummary.exists(h=>!h.contains(u.container())&&h.contains(g.container())))))(c,e))&&!(l&&i&&((u,m,p)=>p.startDetails.exists(g=>yn(u,m).forall(h=>!g.contains(h.container()))))(d,c,e))&&(!jT||!VS(r)||(!l&&vx(a,r)||l0(i,c,r)?hx(r):t.undoManager.transact(()=>{const u=o.getSel();let{anchorNode:m,anchorOffset:p,focusNode:g,focusOffset:h}=u??{};const f=()=>{st(m)&&st(p)&&st(g)&&st(h)&&u?.setBaseAndExtent(m,p,g,h)},b=(C,k)=>{J(C.childNodes,E=>{al(E)&&k.appendChild(E)})},y=t.dom.create("span",{"data-mce-bogus":"all"});b(r,y),r.appendChild(y),f(),l&&(wx&&(n.altKey||s&&n.metaKey)||!wx&&n.ctrlKey)&&u?.modify("extend",s?"left":"right",n.metaKey?"line":"word"),!o.isCollapsed()&&vx(o.getRng(),y)?hx(r):(t.execCommand(s?"Delete":"ForwardDelete"),m=u?.anchorNode,p=u?.anchorOffset,g=u?.focusNode,h=u?.focusOffset,b(y,r),f()),t.dom.remove(y)}),0)));var u,m})(e,o,n)&&n.preventDefault()})})})(t)})(t),(s=>{const i="contenteditable",l=" "+ot.trim(WE(s))+" ",c=" "+ot.trim(Hh(s))+" ",d=T1(l),u=T1(c),m=KE(s);m.length>0&&s.on("BeforeSetContent",p=>{((g,h,f)=>{let b=h.length,y=f.content;if("raw"!==f.format){for(;b--;)y=y.replace(h[b],wA(g,y,Hh(g)));f.content=y}})(s,m,p)}),s.parser.addAttributeFilter("class",p=>{let g=p.length;for(;g--;){const h=p[g];d(h)?h.attr(i,"true"):u(h)&&h.attr(i,"false")}}),s.serializer.addAttributeFilter(i,p=>{let g=p.length;for(;g--;){const h=p[g];(d(h)||u(h))&&(m.length>0&&h.attr("data-mce-content")?(h.name="#text",h.type=3,h.raw=!0,h.value=h.attr("data-mce-content")):h.attr(i,null))}})})(t),dr(t)||((s=t).on("mousedown",i=>{i.detail>=3&&(i.preventDefault(),(t=>{const e=((n,o)=>{const r=$.fromRangeStart(n).getNode(),a=(d=o,Lr(A(r),u=>Po(u.dom)||eo(u),kx(d)).getOr(A(d)).dom),s=_x(r,a,!1),i=_x(r,a,!0),l=document.createRange();var d;return Ex(s,a).fold(()=>{Sx(s)?l.setStart(s,0):l.setStartBefore(s)},c=>l.setStartBefore(c.dom)),Ex(i,a).fold(()=>{Sx(i)?l.setEnd(i,i.data.length):l.setEndAfter(i)},c=>l.setEndAfter(c.dom)),l})(t.selection.getRng(),t.getBody());t.selection.setRng(ig(e))})(s))}),(s=>{(i=>{const l=[",",".",";",":","!","?"],c=[32],d=()=>{return p=qE(i),g=VE(i),{inlinePatterns:Eh(p),blockPatterns:kh(p),dynamicPatternsLookup:g};var p,g},u=()=>i.options.isSet("text_patterns_lookup");i.on("keydown",p=>{if(13===p.keyCode&&!wt.modifierPressed(p)&&i.selection.isCollapsed()){const g=d();(g.inlinePatterns.length>0||g.blockPatterns.length>0||u())&&((t,e)=>{const n=t.selection.getRng();return Kf(t,n).map(o=>{var r;const a=Math.max(0,n.startOffset),s=Hx(e,o,null!==(r=o.textContent)&&void 0!==r?r:""),i=Xx(t,o,n.startContainer,a,s,!0),l=((c,d,u)=>{var p;const g=c.dom,h=Bn(c);if(!g.is(d,h))return[];const f=null!==(p=d.textContent)&&void 0!==p?p:"";return((b,y)=>{const C=To(b,(N,R)=>R.start.length-N.start.length),k=y.replace(Ge," ");return he(C,E=>0===y.indexOf(E.start)||0===k.indexOf(E.start))})(u.blockPatterns,f).map(b=>ot.trim(f).length===b.start.length?[]:[{pattern:b,range:Wf(g,g.getRoot(),d,0,d,0,!0)}]).getOr([])})(t,o,s);return(l.length>0||i.length>0)&&(t.undoManager.add(),t.undoManager.extra(()=>{t.execCommand("mceInsertNewLine")},()=>{t.insertContent("\ufeff"),Qx(t,i),((u,m)=>{if(0===m.length)return;const p=u.selection.getBookmark();J(m,g=>((h,f)=>{const y=f.pattern,C=zx(h.dom.getRoot(),f.range).getOrDie("Unable to resolve path range");return Kf(h,C).each(k=>{"block-format"===y.type?((E,N)=>{const R=N.get(E);return Ee(R)&&We(R).exists(I=>It(I,"block"))})(y.format,h.formatter)&&h.undoManager.transact(()=>{qx(h.dom,k,y),h.formatter.apply(y.format)}):"block-command"===y.type&&h.undoManager.transact(()=>{qx(h.dom,k,y),h.execCommand(y.cmd,!1,y.value)})}),!0})(u,g)),u.selection.moveToBookmark(p)})(t,l);const c=t.selection.getRng(),d=ff(c.startContainer,c.startOffset,t.dom.getRoot());t.execCommand("mceInsertNewLine"),d.each(u=>{const m=u.container;"\ufeff"===m.data.charAt(u.offset-1)&&(m.deleteData(u.offset-1,1),di(t.dom,m.parentNode,p=>p===t.dom.getRoot()))})}),!0)}).getOr(!1)})(i,g)&&p.preventDefault()}},!0);const m=()=>{if(i.selection.isCollapsed()){const p=d();(p.inlinePatterns.length>0||u())&&((g,h)=>{const f=g.selection.getRng();Kf(g,f).map(b=>{const y=Math.max(0,f.startOffset-1),C=$x(g.dom,b,f.startContainer,y),k=Hx(h,b,C),E=Xx(g,b,f.startContainer,y,k,!1);E.length>0&&g.undoManager.transact(()=>{Qx(g,E)})})})(i,p)}};i.on("keyup",p=>{Jx(c,p,(g,h)=>g===h.keyCode&&!wt.modifierPressed(h))&&m()}),i.on("keypress",p=>{Jx(l,p,(g,h)=>g.charCodeAt(0)===h.charCode)&&so.setEditorTimeout(i,m)})})(s)})(t));const r=wT(t);((t,e)=>{t.addCommand("delete",()=>{var n;_1(n=t,e,!1).fold(()=>{Km(n),Gm(n)},En)}),t.addCommand("forwardDelete",()=>{var n;_1(n=t,e,!0).fold(()=>i0(n,"ForwardDelete"),En)})})(t,r),(s=>{s.on("NodeChange",Ct(CA,s))})(t),(s=>{var i;const l=s.dom,c=Bn(s),d=null!==(i=sE(s))&&void 0!==i?i:"",u=(m,p)=>{if((f=>{if(kC(f)){const b=f.keyCode;return!EC(f)&&(wt.metaKeyPressed(f)||f.altKey||b>=112&&b<=123||Lt(IR,b))}return!1})(m))return;const g=s.getBody(),h=!(kC(f=m)&&!(EC(f)||"keyup"===f.type&&229===f.keyCode))&&((f,b,y)=>{if(De(A(b),!1)){const C=b.firstElementChild;return!C||!f.getStyle(b.firstElementChild,"padding-left")&&!f.getStyle(b.firstElementChild,"padding-right")&&y===C.nodeName.toLowerCase()}return!1})(l,g,c);var f;(""!==l.getAttrib(g,SC)!==h||p)&&(l.setAttrib(g,SC,h?d:null),l.setAttrib(g,"aria-placeholder",h?d:null),((f,b)=>{f.dispatch("PlaceholderToggle",{state:b})})(s,h),s.on(h?"keydown":"keyup",u),s.off(h?"keyup":"keydown",u))};Qt(d)&&s.on("init",m=>{u(m,!0),s.on("change SetContent ExecCommand",u),s.on("paste",p=>so.setEditorTimeout(s,()=>u(p)))})})(t),(t=>{const e=Ye(!1),n=Ye(zE(t)?"text":"html"),o=(r=>{const a=Ye(null);return{create:()=>((s,i)=>{const{dom:l,selection:c}=s,d=s.getBody();i.set(c.getRng());const u=l.add(s.getBody(),"div",{id:"mcepastebin",class:"mce-pastebin",contentEditable:!0,"data-mce-bogus":"all",style:"position: fixed; top: 50%; width: 10px; height: 10px; overflow: hidden; opacity: 0"},tx);Jt.browser.isFirefox()&&l.setStyle(u,"left","rtl"===l.getStyle(d,"direction",!0)?65535:-65535),l.bind(u,"beforedeactivate focusin focusout",m=>{m.stopPropagation()}),u.focus(),c.select(u,!0)})(r,a),remove:()=>((s,i)=>{const l=s.dom;if(Uf(s)){let c;const d=i.get();for(;c=Uf(s);)l.remove(c),l.unbind(c);d&&s.selection.setRng(d)}i.set(null)})(r,a),getEl:()=>Uf(r),getHtml:()=>(s=>{const i=s.dom,l=(m,p)=>{m.appendChild(p),i.remove(p,!0)},[c,...d]=Ot(s.getBody().childNodes,TT);J(d,m=>{l(c,m)});const u=i.select("div[id=mcepastebin]",c);for(let m=u.length-1;m>=0;m--){const p=i.create("div");c.insertBefore(p,u[m]),l(p,u[m])}return c?c.innerHTML:""})(r),getLastRng:a.get}})(t);var r,a,s;r=t,(Jt.browser.isChromium()||Jt.browser.isSafari())&&(s=IT,(a=r).on("PastePreProcess",i=>{i.content=s(a,i.content,i.internal)})),((r,a)=>{r.addCommand("mceTogglePlainTextPaste",()=>{((s,i)=>{"text"===i.get()?(i.set("html"),Vb(s,!1)):(i.set("text"),Vb(s,!0)),s.focus()})(r,a)}),r.addCommand("mceInsertClipboardContent",(s,i)=>{i.html&&$d(r,i.html,i.internal,!1),i.text&&Hf(r,i.text,!1)})})(t,n),(r=>{const a=l=>c=>{l(r,c)},s=PE(r);Yt(s)&&r.on("PastePreProcess",a(s));const i=ME(r);Yt(i)&&r.on("PastePostProcess",a(i))})(t),t.on("PreInit",()=>{(r=>{r.on("cut",(a=>s=>{!s.isDefaultPrevented()&&mx(a)&&cx(s,ux(a),dx(a),()=>{if(Jt.browser.isChromium()||Jt.browser.isFirefox()){const i=a.selection.getRng();so.setEditorTimeout(a,()=>{a.selection.setRng(i),a.execCommand("Delete")},0)}else a.execCommand("Delete")})})(r)),r.on("copy",(a=>s=>{!s.isDefaultPrevented()&&mx(a)&&cx(s,ux(a),dx(a),jt)})(r))})(t),((r,a)=>{DE(r)&&r.on("dragend dragover draggesture dragdrop drop drag",s=>{s.preventDefault(),s.stopPropagation()}),Oc(r)||r.on("drop",s=>{const i=s.dataTransfer;i&&ie(i.files,c=>/^image\//.test(c.type))&&s.preventDefault()}),r.on("drop",s=>{if(s.isDefaultPrevented())return;const i=gx(r,s);if(pe(i))return;const l=sx(s.dataTransfer),c=Sa(l,Hd());if((!ix(l)||(p=>{const g=p["text/plain"];return!!g&&0===g.indexOf("file://")})(l))&&lx(r,s,i))return;const d=l[Hd()],u=d||l["text/html"]||l["text/plain"],m=((p,g,h,f)=>{const b=p.getParent(h,y=>Wa(g,y));if(b&&It(f,"text/html")){const y=(new DOMParser).parseFromString(f["text/html"],"text/html").body;return!Oe(y.querySelector(b.nodeName.toLowerCase()))}return!1})(r.dom,r.schema,i.startContainer,l);a.get()&&!m||u&&(s.preventDefault(),so.setEditorTimeout(r,()=>{r.undoManager.transact(()=>{d&&r.execCommand("Delete"),fx(r,i);const p=ox(u);l["text/html"]?$d(r,p,c,!0):Hf(r,p,!0)})}))}),r.on("dragstart",s=>{a.set(!0)}),r.on("dragover dragend",s=>{Oc(r)&&!a.get()&&(s.preventDefault(),fx(r,gx(r,s))),"dragend"===s.type&&a.set(!1)})})(t,e),((t,e,n)=>{((o,r,a)=>{let s;o.on("keydown",i=>{var l;(wt.metaKeyPressed(l=i)&&86===l.keyCode||l.shiftKey&&45===l.keyCode)&&!i.isDefaultPrevented()&&(s=i.shiftKey&&86===i.keyCode)}),o.on("paste",i=>{if(i.isDefaultPrevented()||(d=i,Jt.os.isAndroid()&&0===(null===(m=null===(u=d.clipboardData)||void 0===u?void 0:u.items)||void 0===m?void 0:m.length)))return;var d,u,m;const l="text"===a.get()||s;s=!1;const c=sx(i.clipboardData);!ix(c)&&lx(o,i,r.getLastRng()||o.selection.getRng())||(Sa(c,"text/html")?(i.preventDefault(),$f(o,c,c["text/html"],l,!0)):Sa(c,"text/plain")&&Sa(c,"text/uri-list")?(i.preventDefault(),$f(o,c,c["text/plain"],l,!0)):(r.create(),so.setEditorTimeout(o,()=>{const d=r.getHtml();r.remove(),$f(o,c,d,l,!1)},0)))})})(t,e,n),(o=>{const r=s=>ht(s,"webkit-fake-url"),a=s=>ht(s,"data:");o.parser.addNodeFilter("img",(s,i,l)=>{if(!Oc(o)&&!0===(null===(d=l.data)||void 0===d?void 0:d.paste))for(const c of s){const d=c.attr("src");Nt(d)&&!c.attr("data-mce-object")&&d!==Jt.transparentSrc&&(r(d)||!$E(o)&&a(d))&&c.remove()}var d})})(t)})(t,o,n)})})(t);const a=(s=>{const i=s;return(l=s,ce(l.plugins,"rtc").bind(c=>S.from(c.setup))).fold(()=>(i.rtcInstance=Jy(s),S.none()),l=>(i.rtcInstance=(()=>{const c=ut(null),d=ut("");return{init:{bindEvents:jt},undoManager:{beforeChange:jt,add:c,undo:c,redo:c,clear:jt,reset:jt,hasUndo:de,hasRedo:de,transact:c,ignore:jt,extra:jt},formatter:{match:de,matchAll:ut([]),matchNode:ut(void 0),canApply:de,closest:d,apply:jt,remove:jt,toggle:jt,formatChanged:ut({unbind:jt})},editor:{getContent:d,setContent:ut({content:"",html:""}),insertContent:ut(""),addVisual:jt},selection:{getContent:d},autocompleter:{addDecoration:jt,removeDecoration:jt},raw:{getModel:ut(S.none())}}})(),S.some(()=>l().then(c=>(i.rtcInstance=(d=>{const u=C=>At(C)?C:{},{init:m,undoManager:p,formatter:g,editor:h,selection:f,autocompleter:b,raw:y}=d;return{init:{bindEvents:m.bindEvents},undoManager:{beforeChange:p.beforeChange,add:p.add,undo:p.undo,redo:p.redo,clear:p.clear,reset:p.reset,hasUndo:p.hasUndo,hasRedo:p.hasRedo,transact:(C,k,E)=>p.transact(E),ignore:(C,k)=>p.ignore(k),extra:(C,k,E,N)=>p.extra(E,N)},formatter:{match:(C,k,E,N)=>g.match(C,u(k),N),matchAll:g.matchAll,matchNode:g.matchNode,canApply:C=>g.canApply(C),closest:C=>g.closest(C),apply:(C,k,E)=>g.apply(C,u(k)),remove:(C,k,E,N)=>g.remove(C,u(k)),toggle:(C,k,E)=>g.toggle(C,u(k)),formatChanged:(C,k,E,N,R)=>g.formatChanged(k,E,N,R)},editor:{getContent:C=>h.getContent(C),setContent:(C,k)=>({content:h.setContent(C,k),html:""}),insertContent:(C,k)=>(h.insertContent(C),""),addVisual:h.addVisual},selection:{getContent:(C,k)=>f.getContent(k)},autocompleter:{addDecoration:b.addDecoration,removeDecoration:b.removeDecoration},raw:{getModel:()=>S.some(y.getRawModel())}}})(c),c.rtc.isRemote)))));var l})(t);(s=>{const i=s.getDoc(),l=s.getBody();s.dispatch("PreInit"),OE(s)||(i.body.spellcheck=!1,Yf.setAttrib(l,"spellcheck","false")),s.quirks=(t=>{const e=ot.each,n=wt.BACKSPACE,o=wt.DELETE,r=t.dom,a=t.selection,s=t.parser,i=Jt.browser,l=i.isFirefox(),c=i.isChromium()||i.isSafari(),d=Jt.deviceType.isiPhone()||Jt.deviceType.isiPad(),u=Jt.os.isMacOS()||Jt.os.isiOS(),m=(T,P)=>{try{t.getDoc().execCommand(T,!1,String(P))}catch{}},p=T=>T.isDefaultPrevented(),g=()=>{t.shortcuts.add("meta+a",null,"SelectAll")},h=()=>{t.inline||r.bind(t.getDoc(),"mousedown mouseup",T=>{let P;if(T.target===t.getDoc().documentElement)if(P=a.getRng(),t.getBody().focus(),"mousedown"===T.type){if(no(P.startContainer))return;a.placeCaretAt(T.clientX,T.clientY)}else a.setRng(P)})},f=()=>{Range.prototype.getClientRects||t.on("mousedown",T=>{if(!p(T)&&"HTML"===T.target.nodeName){const P=t.getBody();P.blur(),so.setEditorTimeout(t,()=>{P.focus()})}})},b=()=>{const T=zh(t);t.on("click",P=>{const B=P.target;/^(IMG|HR)$/.test(B.nodeName)&&r.isEditable(B.parentNode)&&(P.preventDefault(),t.selection.select(B),t.nodeChanged()),"A"===B.nodeName&&r.hasClass(B,T)&&0===B.childNodes.length&&r.isEditable(B.parentNode)&&(P.preventDefault(),a.select(B))})},y=()=>{t.on("keydown",T=>{if(!p(T)&&T.keyCode===n&&a.isCollapsed()&&0===a.getRng().startOffset){const P=a.getNode().previousSibling;if(P&&P.nodeName&&"table"===P.nodeName.toLowerCase())return T.preventDefault(),!1}return!0})},C=()=>{Uh(t)||t.on("BeforeExecCommand mousedown",()=>{m("StyleWithCSS",!1),m("enableInlineTableEditing",!1),Ih(t)||m("enableObjectResizing",!1)})},k=()=>{t.contentStyles.push("img:-moz-broken {-moz-force-broken-image-icon:1;min-width:24px;min-height:24px}")},E=()=>{t.inline||t.on("keydown",()=>{document.activeElement===document.body&&t.getWin().focus()})},N=()=>{t.inline||(t.contentStyles.push("body {min-height: 150px}"),t.on("click",T=>{let P;"HTML"===T.target.nodeName&&(P=t.selection.getRng(),t.getBody().focus(),t.selection.setRng(P),t.selection.normalize(),t.nodeChanged())}))},R=()=>{u&&t.on("keydown",T=>{!wt.metaKeyPressed(T)||T.shiftKey||37!==T.keyCode&&39!==T.keyCode||(T.preventDefault(),t.selection.getSel().modify("move",37===T.keyCode?"backward":"forward","lineboundary"))})},I=()=>{t.on("click",T=>{let P=T.target;do{if("A"===P.tagName)return void T.preventDefault()}while(P=P.parentNode)}),t.contentStyles.push(".mce-content-body {-webkit-touch-callout: none}")},q=()=>{t.on("init",()=>{t.dom.bind(t.getBody(),"submit",T=>{T.preventDefault()})})},H=jt;return dr(t)?(c&&(h(),b(),q(),g(),d&&(E(),N(),I())),l&&(f(),C(),k(),R())):(t.on("keydown",T=>{if(p(T)||T.keyCode!==wt.BACKSPACE)return;let P=a.getRng();const B=P.startContainer,j=P.startOffset,lt=r.getRoot();let Z=B;if(P.collapsed&&0===j){for(;Z.parentNode&&Z.parentNode.firstChild===Z&&Z.parentNode!==lt;)Z=Z.parentNode;"BLOCKQUOTE"===Z.nodeName&&(t.formatter.toggle("blockquote",void 0,Z),P=r.createRng(),P.setStart(B,0),P.setEnd(B,0),a.setRng(P))}}),(()=>{const T=P=>{const B=r.create("body"),j=P.cloneContents();return B.appendChild(j),a.serializer.serialize(B,{format:"html"})};t.on("keydown",P=>{const B=P.keyCode;if(!p(P)&&(B===o||B===n)&&t.selection.isEditable()){const j=t.selection.isCollapsed(),lt=t.getBody();if(j&&!r.isEmpty(lt)||!j&&!(Z=>{const _t=T(Z),mt=r.createRng();return mt.selectNode(t.getBody()),_t===T(mt)})(t.selection.getRng()))return;P.preventDefault(),t.setContent(""),lt.firstChild&&r.isBlock(lt.firstChild)?t.selection.setCursorLocation(lt.firstChild,0):t.selection.setCursorLocation(lt,0),t.nodeChanged()}})})(),Jt.windowsPhone||t.on("keyup focusin mouseup",T=>{wt.modifierPressed(T)||(P=>{const B=P.getBody(),j=P.selection.getRng();return j.startContainer===j.endContainer&&j.startContainer===B&&0===j.startOffset&&j.endOffset===B.childNodes.length})(t)||a.normalize()},!0),c&&(h(),b(),t.on("init",()=>{m("DefaultParagraphSeparator",Bn(t))}),q(),y(),s.addNodeFilter("br",T=>{let P=T.length;for(;P--;)"Apple-interchange-newline"===T[P].attr("class")&&T[P].remove()}),d?(E(),N(),I()):g()),l&&(t.on("keydown",T=>{if(!p(T)&&T.keyCode===n){if(!t.getBody().getElementsByTagName("hr").length)return;if(a.isCollapsed()&&0===a.getRng().startOffset){const P=a.getNode(),B=P.previousSibling;if("HR"===P.nodeName)return r.remove(P),void T.preventDefault();B&&B.nodeName&&"hr"===B.nodeName.toLowerCase()&&(r.remove(B),T.preventDefault())}}}),f(),(()=>{const T=()=>{const B=r.getAttribs(a.getStart().cloneNode(!1));return()=>{const j=a.getStart();j!==t.getBody()&&(r.setAttrib(j,"style",null),e(B,lt=>{j.setAttributeNode(lt.cloneNode(!0))}))}},P=()=>!a.isCollapsed()&&r.getParent(a.getStart(),r.isBlock)!==r.getParent(a.getEnd(),r.isBlock);t.on("keypress",B=>{let j;return!(!(p(B)||8!==B.keyCode&&46!==B.keyCode)&&P()&&(j=T(),t.getDoc().execCommand("delete",!1),j(),B.preventDefault(),1))}),r.bind(t.getDoc(),"cut",B=>{if(!p(B)&&P()){const j=T();so.setEditorTimeout(t,()=>{j()})}})})(),C(),t.on("SetContent ExecCommand",T=>{"setcontent"!==T.type&&"mceInsertLink"!==T.command||e(r.select("a:not([data-mce-block])"),P=>{var B;let j=P.parentNode;const lt=r.getRoot();if(j?.lastChild===P){for(;j&&!r.isBlock(j);){if((null===(B=j.parentNode)||void 0===B?void 0:B.lastChild)!==j||j===lt)return;j=j.parentNode}r.add(j,"br",{"data-mce-bogus":1})}})}),k(),R(),y(),t.on("drop",T=>{var P;const B=null===(P=T.dataTransfer)||void 0===P?void 0:P.getData("text/html");Nt(B)&&/^<img[^>]*>$/.test(B)&&t.dispatch("dragend",new window.DragEvent("dragend",T))}))),{refreshContentEditable:H,isHidden:()=>{if(!l||t.removed)return!1;const T=t.selection.getSel();return!T||!T.rangeCount||0===T.rangeCount}}})(s),s.dispatch("PostRender");const c=oE(s);void 0!==c&&(l.dir=c);const d=BE(s);d&&s.on("BeforeSetContent",u=>{ot.each(d,m=>{u.content=u.content.replace(m,p=>"\x3c!--mce:protected "+escape(p)+"--\x3e")})}),s.on("SetContent",()=>{s.addVisual(s.getBody())}),s.on("compositionstart compositionend",u=>{s.composing="compositionstart"===u.type})})(t),a.fold(()=>{nS(t).then(()=>Qf(t))},s=>{t.setProgressState(!0),nS(t).then(()=>{s().then(i=>{t.setProgressState(!1),Qf(t),Zy(t)},i=>{t.notificationManager.open({type:"error",text:String(i)}),Qf(t),Zy(t)})})})},iO=ye,Jf=me.DOM,rS=me.DOM,aS=(t,e)=>({editorContainer:t,iframeContainer:e,api:{}}),sS=t=>{const e=t.getElement();return t.inline?aS(null):(n=>{const o=rS.create("div");return rS.insertAfter(o,n),aS(o,o)})(e)},lO=async t=>{t.dispatch("ScriptsLoaded"),(n=>{const o=ot.trim(Dh(n)),r=n.ui.registry.getAll().icons,a={...pd.get("default").icons,...pd.get(o).icons};le(a,(s,i)=>{It(r,i)||n.ui.registry.addIcon(i,s)})})(t),(n=>{const o=$s(n);if(Nt(o)){const r=fs.get(o);n.theme=r(n,fs.urls[o])||{},Yt(n.theme.init)&&n.theme.init(n,fs.urls[o]||n.documentBaseUrl.replace(/\/$/,""))}else n.theme={}})(t),(n=>{const o=Gu(n),r=ei.get(o);n.model=r(n,ei.urls[o])})(t),(n=>{const o=[];J(Tc(n),r=>{((a,s,i)=>{const l=ni.get(i),c=ni.urls[i]||a.documentBaseUrl.replace(/\/$/,"");if(i=ot.trim(i),l&&-1===ot.inArray(s,i)){if(a.plugins[i])return;try{const d=l(a,c)||{};a.plugins[i]=d,Yt(d.init)&&(d.init(a,c),s.push(i))}catch(d){((u,m,p)=>{const g=Mo.translate(["Failed to initialize plugin: {0}",m]);jb(u,"PluginLoadError",{message:g}),bd(g,p),hd(u,g)})(a,i,d)}}})(n,o,r.replace(/^\-/,""))})})(t);const e=await(n=>{const o=n.getElement();return n.orgDisplay=o.style.display,Nt($s(n))?(r=>{const a=r.theme.renderUI;return a?a():sS(r)})(n):Yt($s(n))?(r=>{const a=r.getElement(),s=$s(r)(r,a);return s.editorContainer.nodeType&&(s.editorContainer.id=s.editorContainer.id||r.id+"_parent"),s.iframeContainer&&s.iframeContainer.nodeType&&(s.iframeContainer.id=s.iframeContainer.id||r.id+"_iframecontainer"),s.height=s.iframeHeight?s.iframeHeight:a.offsetHeight,s})(n):sS(n)})(t);var n,o;((n,o)=>{const r={show:S.from(o.show).getOr(jt),hide:S.from(o.hide).getOr(jt),isEnabled:S.from(o.isEnabled).getOr(ye),setEnabled:a=>{n.mode.isReadOnly()||S.from(o.setEnabled).each(s=>s(a))}};n.ui={...n.ui,...r}})(t,S.from(e.api).getOr({})),t.editorContainer=e.editorContainer,(n=t).contentCSS=n.contentCSS.concat(fC(o=n,eE(o)),(o=>fC(o,Lh(o)))(n)),t.inline?oS(t):((n,o)=>{((r,a)=>{const s=r.translate("Rich Text Area"),i=Zn(A(r.getElement()),"tabindex").bind(we),l=((c,d,u,m)=>{const p=Be("iframe");return m.each(g=>Me(p,"tabindex",g)),Do(p,u),Do(p,{id:c+"_ifr",frameBorder:"0",allowTransparency:"true",title:d}),Jr(p,"tox-edit-area__iframe"),p})(r.id,s,Lk(r),i).dom;l.onload=()=>{l.onload=null,r.dispatch("load")},r.contentAreaContainer=a.iframeContainer,r.iframeElement=l,r.iframeHTML=(c=>{let d=Ik(c)+"<html><head>";Ah(c)!==c.documentBaseUrl&&(d+='<base href="'+c.documentBaseURI.getURI()+'" />'),d+='<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />';const u=Fk(c),m=Uk(c),p=c.translate(_E(c));return Th(c)&&(d+='<meta http-equiv="Content-Security-Policy" content="'+Th(c)+'" />'),d+=`</head><body id="${u}" class="mce-content-body ${m}" data-id="${c.id}" aria-label="${p}"><br></body></html>`,d})(r),Jf.add(a.iframeContainer,l)})(n,o),o.editorContainer&&(o.editorContainer.style.display=n.orgDisplay,n.hidden=Jf.isHidden(o.editorContainer)),n.getElement().style.display="none",Jf.setAttrib(n.id,"aria-hidden","true"),n.getElement().style.visibility=n.orgVisibility,(r=>{const a=r.iframeElement,s=()=>{r.contentDocument=a.contentDocument,oS(r)};if(XE(r)||Jt.browser.isFirefox()){const l=r.getDoc();l.open(),l.write(r.iframeHTML),l.close(),s()}else{const l=((t,e,n)=>((r,a,s,i)=>{const c=((d,u)=>m=>{d(m)&&u((p=>{const g=A(Bi(p).getOr(p.target)),h=()=>p.stopPropagation(),f=()=>p.preventDefault(),b=Fe(f,h);return((y,C,k,E,N,R,I)=>({target:y,x:C,y:k,stop:E,prevent:N,kill:R,raw:I}))(g,p.clientX,p.clientY,h,f,b,p)})(m))})(s,i);return r.dom.addEventListener(a,c,!1),{unbind:Ct(et,r,a,c,!1)}})(t,"load",n,()=>{l.unbind(),s()}))(A(a),0,iO);a.srcdoc=r.iframeHTML}})(n)})(t,{editorContainer:e.editorContainer,iframeContainer:e.iframeContainer})},ps=me.DOM,iS=t=>"-"===t.charAt(0),lS=(t,e,n)=>S.from(e).filter(o=>Qt(o)&&!pd.has(o)).map(o=>({url:`${t.editorManager.baseURL}/icons/${o}/icons${n}.js`,name:S.some(o)})),cS=po().deviceType,dS=cS.isPhone(),dO=cS.isTablet(),Ml=t=>{if(pe(t))return[];{const e=Ee(t)?t:t.split(/[ ,]/),n=te(e,Wt);return Ot(n,Qt)}},Zf=(t,e)=>It(t.sections(),e),mO=(t,e)=>({table_grid:!1,object_resizing:!1,resize:!1,toolbar_mode:ce(t,"toolbar_mode").getOr("scrolling"),toolbar_sticky:!1,...e?{menubar:!1}:{}}),gO=(t,e)=>{var n;const o=null!==(n=e.external_plugins)&&void 0!==n?n:{};return t&&t.external_plugins?ot.extend({},t.external_plugins,o):o},hO=(t,e)=>{const n=t.selection,o=t.dom;return/^ | $/.test(e)?((r,a,s)=>{const i=A(r.getRoot());return s=ml(i,$.fromRangeStart(a))?s.replace(/^ /,"&nbsp;"):s.replace(/^&nbsp;/," "),gl(i,$.fromRangeEnd(a))?s.replace(/(&nbsp;| )(<br( \/)>)?$/,"&nbsp;"):s.replace(/&nbsp;(<br( \/)?>)?$/," ")})(o,n.getRng(),e):e},Gd=(t,e)=>{if(t.selection.isEditable()){const{content:n,details:o}=(r=>{if("string"!=typeof r){const a=ot.extend({paste:r.paste,data:{paste:r.paste}},r);return{content:r.content,details:a}}return{content:r,details:{}}})(e);Pg(t,{...o,content:hO(t,n),format:"html",set:!1,selection:!0}).each(r=>{const a=(i=r.content,l=o,zg(t).editor.insertContent(i,l));var i,l;Mg(t,a,r),t.addVisual()})}},bO={"font-size":"size","font-family":"face"},vO=Pa("font"),uS=t=>(e,n)=>S.from(n).map(A).filter(fn).bind(o=>{return(r=t,a=e,s=o.dom,ug(A(s),i=>{return za(l=i,r).orThunk(()=>vO(l)?ce(bO,r).bind(c=>Zn(l,c)):S.none());var l},i=>re(A(a),i))).or(((r,a)=>S.from(me.DOM.getStyle(a,r,!0)))(t,o.dom));var r,a,s}).getOr(""),yO=uS("font-size"),CO=Fe(t=>t.replace(/[\'\"\\]/g,"").replace(/,\s+/g,","),uS("font-family")),wO=t=>Cn(t.getBody()).bind(e=>{const n=e.container();return S.from(tt(n)?n.parentNode:n)}),tp=(t,e)=>{return n=t,o=Aa(S.some,e),(r=n,S.from(r.selection.getRng()).bind(a=>{const s=r.getBody();return a.startContainer===s&&0===a.startOffset?S.none():S.from(r.selection.getStart(!0))})).orThunk(Ct(wO,n)).map(A).filter(fn).bind(o);var n,o,r},mS=(t,e)=>{if(/^[0-9.]+$/.test(e)){const n=parseInt(e,10);if(n>=1&&n<=7){const o=ot.explode(t.options.get("font_size_style_values")),r=ot.explode(t.options.get("font_size_classes"));return r.length>0?r[n-1]||e:o[n-1]||e}return e}return e},xO=t=>{const e=t.split(/\s*,\s*/);return te(e,n=>-1===n.indexOf(" ")||ht(n,'"')||ht(n,"'")?n:`'${n}'`).join(",")},kO=["toggleview"],gS=t=>Lt(kO,t.toLowerCase());class fS{constructor(e){this.commands={state:{},exec:{},value:{}},this.editor=e}execCommand(e,n=!1,o,r){const a=this.editor,s=e.toLowerCase(),i=r?.skip_focus;if(a.removed||("mcefocus"!==s&&(/^(mceAddUndoLevel|mceEndUndoLevel)$/i.test(s)||i?Am(c=a).each(d=>c.selection.setRng(d)):a.focus()),a.dispatch("BeforeExecCommand",{command:e,ui:n,value:o}).isDefaultPrevented()))return!1;var c;const l=this.commands.exec[s];return!!Yt(l)&&(l(s,n,o),a.dispatch("ExecCommand",{command:e,ui:n,value:o}),!0)}queryCommandState(e){if(!gS(e)&&this.editor.quirks.isHidden()||this.editor.removed)return!1;const n=e.toLowerCase(),o=this.commands.state[n];return!!Yt(o)&&o(n)}queryCommandValue(e){if(!gS(e)&&this.editor.quirks.isHidden()||this.editor.removed)return"";const n=e.toLowerCase(),o=this.commands.value[n];return Yt(o)?o(n):""}addCommands(e,n="exec"){const o=this.commands;le(e,(r,a)=>{J(a.toLowerCase().split(","),s=>{o[n][s]=r})})}addCommand(e,n,o){const r=e.toLowerCase();this.commands.exec[r]=(a,s,i)=>n.call(o??this.editor,s,i)}queryCommandSupported(e){const n=e.toLowerCase();return!!this.commands.exec[n]}addQueryStateHandler(e,n,o){this.commands.state[e.toLowerCase()]=()=>n.call(o??this.editor)}addQueryValueHandler(e,n,o){this.commands.value[e.toLowerCase()]=()=>n.call(o??this.editor)}}const hs="data-mce-contenteditable",ep=(t,e,n)=>{try{t.getDoc().execCommand(e,!1,String(n))}catch{}},Yd=(t,e)=>{t.dom.contentEditable=e?"true":"false"},bs=t=>t.readonly,pS=t=>{t.parser.addAttributeFilter("contenteditable",e=>{bs(t)&&J(e,n=>{n.attr(hs,n.attr("contenteditable")),n.attr("contenteditable","false")})}),t.serializer.addAttributeFilter(hs,e=>{bs(t)&&J(e,n=>{n.attr("contenteditable",n.attr(hs))})}),t.serializer.addTempAttr(hs)},_O=["copy"],NO=ot.makeMap("focus blur focusin focusout click dblclick mousedown mouseup mousemove mouseover beforepaste paste cut copy selectionchange mouseout mouseenter mouseleave wheel keydown keypress keyup input beforeinput contextmenu dragstart dragend dragover draggesture dragdrop drop drag submit compositionstart compositionend compositionupdate touchstart touchmove touchend touchcancel"," ");class np{static isNative(e){return!!NO[e.toLowerCase()]}constructor(e){this.bindings={},this.settings=e||{},this.scope=this.settings.scope||this,this.toggleEvent=this.settings.toggleEvent||de}fire(e,n){return this.dispatch(e,n)}dispatch(e,n){const o=e.toLowerCase(),r=Ou(o,n??{},this.scope);this.settings.beforeFire&&this.settings.beforeFire(r);const a=this.bindings[o];if(a)for(let s=0,i=a.length;s<i;s++){const l=a[s];if(!l.removed){if(l.once&&this.off(o,l.func),r.isImmediatePropagationStopped())return r;if(!1===l.func.call(this.scope,r))return r.preventDefault(),r}}return r}on(e,n,o,r){if(!1===n&&(n=de),n){const a={func:n,removed:!1};r&&ot.extend(a,r);const s=e.toLowerCase().split(" ");let i=s.length;for(;i--;){const l=s[i];let c=this.bindings[l];c||(c=[],this.toggleEvent(l,!0)),c=o?[a,...c]:[...c,a],this.bindings[l]=c}}return this}off(e,n){if(e){const o=e.toLowerCase().split(" ");let r=o.length;for(;r--;){const a=o[r];let s=this.bindings[a];if(!a)return le(this.bindings,(i,l)=>{this.toggleEvent(l,!1),delete this.bindings[l]}),this;if(s){if(n){const i=Ve(s,l=>l.func===n);s=i.fail,this.bindings[a]=s,J(i.pass,l=>{l.removed=!0})}else s.length=0;s.length||(this.toggleEvent(e,!1),delete this.bindings[a])}}}else le(this.bindings,(o,r)=>{this.toggleEvent(r,!1)}),this.bindings={};return this}once(e,n,o){return this.on(e,n,o,{once:!0})}has(e){e=e.toLowerCase();const n=this.bindings[e];return!(!n||0===n.length)}}const Ll=t=>(t._eventDispatcher||(t._eventDispatcher=new np({scope:t,toggleEvent:(e,n)=>{np.isNative(e)&&t.toggleNativeEvent&&t.toggleNativeEvent(e,n)}})),t._eventDispatcher),op={fire(t,e,n){return this.dispatch(t,e,n)},dispatch(t,e,n){const o=this;if(o.removed&&"remove"!==t&&"detach"!==t)return Ou(t.toLowerCase(),e??{},o);const r=Ll(o).dispatch(t,e);if(!1!==n&&o.parent){let a=o.parent();for(;a&&!r.isPropagationStopped();)a.dispatch(t,r,!1),a=a.parent?a.parent():void 0}return r},on(t,e,n){return Ll(this).on(t,e,n)},off(t,e){return Ll(this).off(t,e)},once(t,e){return Ll(this).once(t,e)},hasEventListeners(t){return Ll(this).has(t)}},Xd=me.DOM;let vs;const Qd=(t,e)=>{if("selectionchange"===e)return t.getDoc();if(!t.inline&&/^(?:mouse|touch|click|contextmenu|drop|dragover|dragend)/.test(e))return t.getDoc().documentElement;const n=Fh(t);return n?(t.eventRoot||(t.eventRoot=Xd.select(n)[0]),t.eventRoot):t.getBody()},hS=(t,e,n)=>{var o;(o=t).hidden||bs(o)?bs(t)&&((o,r)=>{if("click"!==r.type||wt.metaKeyPressed(r))Lt(_O,r.type)&&o.dispatch(r.type,r);else{const a=A(r.target);(s=o,i=a,er(i,"a",l=>re(l,A(s.getBody()))).bind(l=>Zn(l,"href"))).each(s=>{if(r.preventDefault(),/^#/.test(s)){const i=o.dom.select(`${s},[name="${Q(s,"#")}"]`);i.length&&o.selection.scrollIntoView(i[0],!0)}else window.open(s,"_blank","rel=noopener noreferrer,menubar=yes,toolbar=yes,location=yes,status=yes,resizable=yes,scrollbars=yes")})}var s,i})(t,n):t.dispatch(e,n)},bS=(t,e)=>{if(t.delegates||(t.delegates={}),t.delegates[e]||t.removed)return;const n=Qd(t,e);if(Fh(t)){if(vs||(vs={},t.editorManager.on("removeEditor",()=>{t.editorManager.activeEditor||vs&&(le(vs,(r,a)=>{t.dom.unbind(Qd(t,a))}),vs=null)})),vs[e])return;const o=r=>{const a=r.target,s=t.editorManager.get();let i=s.length;for(;i--;){const l=s[i].getBody();(l===a||Xd.isChildOf(a,l))&&hS(s[i],e,r)}};vs[e]=o,Xd.bind(n,e,o)}else{const o=r=>{hS(t,e,r)};Xd.bind(n,e,o),t.delegates[e]=o}},vS={...op,bindPendingEventDelegates(){const t=this;ot.each(t._pendingNativeEvents,e=>{bS(t,e)})},toggleNativeEvent(t,e){const n=this;"focus"!==t&&"blur"!==t&&(n.removed||(e?n.initialized?bS(n,t):n._pendingNativeEvents?n._pendingNativeEvents.push(t):n._pendingNativeEvents=[t]:n.initialized&&n.delegates&&(n.dom.unbind(Qd(n,t),t,n.delegates[t]),delete n.delegates[t])))},unbindAllNativeEvents(){const t=this,e=t.getBody(),n=t.dom;t.delegates&&(le(t.delegates,(o,r)=>{t.dom.unbind(Qd(t,r),r,o)}),delete t.delegates),!t.inline&&e&&n&&(e.onload=null,n.unbind(t.getWin()),n.unbind(t.getDoc())),n&&(n.unbind(e),n.unbind(t.getContainer()))}},RO=t=>Nt(t)?{value:t.split(/[ ,]/),valid:!0}:Sn(t,Nt)?{value:t,valid:!0}:{valid:!1,message:"The value must be a string[] or a comma/space separated string."},yS=(t,e)=>t+(Xt(e.message)?"":`. ${e.message}`),CS=t=>t.valid,rp=(t,e,n="")=>{const o=e(t);return xn(o)?o?{value:t,valid:!0}:{valid:!1,message:n}:o},AO=["design","readonly"],wS=(t,e,n,o)=>{const r=n[e.get()],a=n[o];try{a.activate()}catch(s){return void console.error(`problem while activating editor mode ${o}:`,s)}var s;r.deactivate(),r.editorReadOnly!==a.editorReadOnly&&((t,e)=>{const n=A(t.getBody());var o,r,a;a=e,Ds(o=n,r="mce-content-readonly")&&!a?Br(o,r):a&&Jr(o,r),e?(t.selection.controlSelection.hideResizeRect(),t._selectionOverrides.hideFakeCaret(),(o=>{S.from(o.selection.getNode()).each(r=>{r.removeAttribute("data-mce-selected")})})(t),t.readonly=!0,Yd(n,!1),J(Rt(n,'*[contenteditable="true"]'),o=>{Me(o,hs,"true"),Yd(o,!1)})):(t.readonly=!1,t.hasEditableRoot()&&Yd(n,!0),J(Rt(n,`*[${hs}="true"]`),o=>{Le(o,hs),Yd(o,!0)}),ep(t,"StyleWithCSS",!1),ep(t,"enableInlineTableEditing",!1),ep(t,"enableObjectResizing",!1),(o=>is(o)||(r=>{const a=Co(A(r.getElement()));return Nm(a).filter(s=>!Om(s.dom)&&Yc(r,s.dom)).isSome()})(o))(t)&&t.focus(),(o=>{o.selection.setRng(o.selection.getRng())})(t),t.nodeChanged())})(t,a.editorReadOnly),e.set(o),(s=t).dispatch("SwitchMode",{mode:o})},ap=ot.each,sp=ot.explode,TO={f1:112,f2:113,f3:114,f4:115,f5:116,f6:117,f7:118,f8:119,f9:120,f10:121,f11:122,f12:123},xS=ot.makeMap("alt,ctrl,shift,meta,access"),OO=t=>{const e={},n=Jt.os.isMacOS()||Jt.os.isiOS();ap(sp(t.toLowerCase(),"+"),a=>{a in xS?e[a]=!0:/^[0-9]{2,}$/.test(a)?e.keyCode=parseInt(a,10):(e.charCode=a.charCodeAt(0),e.keyCode=TO[a]||a.toUpperCase().charCodeAt(0))});const o=[e.keyCode];let r;for(r in xS)e[r]?o.push(r):e[r]=!1;return e.id=o.join(","),e.access&&(e.alt=!0,n?e.ctrl=!0:e.shift=!0),e.meta&&(n?e.meta=!0:(e.ctrl=!0,e.meta=!1)),e};class SS{constructor(e){this.shortcuts={},this.pendingPatterns=[],this.editor=e;const n=this;e.on("keyup keypress keydown",o=>{!n.hasModifier(o)&&!n.isFunctionKey(o)||o.isDefaultPrevented()||(ap(n.shortcuts,r=>{n.matchShortcut(o,r)&&(n.pendingPatterns=r.subpatterns.slice(0),"keydown"===o.type&&n.executeShortcutAction(r))}),n.matchShortcut(o,n.pendingPatterns[0])&&(1===n.pendingPatterns.length&&"keydown"===o.type&&n.executeShortcutAction(n.pendingPatterns[0]),n.pendingPatterns.shift()))})}add(e,n,o,r){const a=this,s=a.normalizeCommandFunc(o);return ap(sp(ot.trim(e)),i=>{const l=a.createShortcut(i,n,s,r);a.shortcuts[l.id]=l}),!0}remove(e){const n=this.createShortcut(e);return!!this.shortcuts[n.id]&&(delete this.shortcuts[n.id],!0)}normalizeCommandFunc(e){const n=this,o=e;return"string"==typeof o?()=>{n.editor.execCommand(o,!1,null)}:ot.isArray(o)?()=>{n.editor.execCommand(o[0],o[1],o[2])}:o}createShortcut(e,n,o,r){const a=ot.map(sp(e,">"),OO);return a[a.length-1]=ot.extend(a[a.length-1],{func:o,scope:r||this.editor}),ot.extend(a[0],{desc:this.editor.translate(n),subpatterns:a.slice(1)})}hasModifier(e){return e.altKey||e.ctrlKey||e.metaKey}isFunctionKey(e){return"keydown"===e.type&&e.keyCode>=112&&e.keyCode<=123}matchShortcut(e,n){return!!n&&n.ctrl===e.ctrlKey&&n.meta===e.metaKey&&n.alt===e.altKey&&n.shift===e.shiftKey&&!!(e.keyCode===n.keyCode||e.charCode&&e.charCode===n.charCode)&&(e.preventDefault(),!0)}executeShortcutAction(e){return e.func?e.func.call(e.scope):null}}const BO=()=>{const t=(()=>{const e={},n={},o={},r={},a={},s={},i={},l={},c=(d,u)=>(m,p)=>{d[m.toLowerCase()]={...p,type:u}};return{addButton:c(e,"button"),addGroupToolbarButton:c(e,"grouptoolbarbutton"),addToggleButton:c(e,"togglebutton"),addMenuButton:c(e,"menubutton"),addSplitButton:c(e,"splitbutton"),addMenuItem:c(n,"menuitem"),addNestedMenuItem:c(n,"nestedmenuitem"),addToggleMenuItem:c(n,"togglemenuitem"),addAutocompleter:c(o,"autocompleter"),addContextMenu:c(a,"contextmenu"),addContextToolbar:c(s,"contexttoolbar"),addContextForm:c(s,"contextform"),addSidebar:c(i,"sidebar"),addView:c(l,"views"),addIcon:(d,u)=>r[d.toLowerCase()]=u,getAll:()=>({buttons:e,menuItems:n,icons:r,popups:o,contextMenus:a,contextToolbars:s,sidebars:i,views:l})}})();return{addAutocompleter:t.addAutocompleter,addButton:t.addButton,addContextForm:t.addContextForm,addContextMenu:t.addContextMenu,addContextToolbar:t.addContextToolbar,addIcon:t.addIcon,addMenuButton:t.addMenuButton,addMenuItem:t.addMenuItem,addNestedMenuItem:t.addNestedMenuItem,addSidebar:t.addSidebar,addSplitButton:t.addSplitButton,addToggleButton:t.addToggleButton,addGroupToolbarButton:t.addGroupToolbarButton,addToggleMenuItem:t.addToggleMenuItem,addView:t.addView,getAll:t.getAll}},ys=me.DOM,kS=ot.extend,DO=ot.each;class Jd{constructor(e,n,o){this.plugins={},this.contentCSS=[],this.contentStyles=[],this.loadedCSS={},this.isNotDirty=!1,this.composing=!1,this.destroyed=!1,this.hasHiddenInput=!1,this.iframeElement=null,this.initialized=!1,this.readonly=!1,this.removed=!1,this.startContent="",this._pendingNativeEvents=[],this._skinLoaded=!1,this._editableRoot=!0,this.editorManager=o,this.documentBaseUrl=o.documentBaseURL,kS(this,vS);const r=this;this.id=e,this.hidden=!1;const a=((t,e,n,o,r)=>{var a;const s=t?{mobile:mO(null!==(a=r.mobile)&&void 0!==a?a:{},e)}:{},i=((t,e)=>{const n=(a=>{const i={},l={};return Sr(a,(c,d)=>Lt(t,d),Rn(i),Rn(l)),{t:i,f:l}})(e);return r=n.f,{sections:ut(n.t),options:ut(r)};var r})(["mobile"],Cf(s,r)),l=ot.extend(n,o,i.options(),t&&Zf(i,"mobile")?((c,d,u={})=>{const m=c.sections(),p=ce(m,d).getOr({});return ot.extend({},u,p)})(i,"mobile"):{},{external_plugins:gO(o,i.options())});return((c,d,u,m)=>{const p=Ml(u.forced_plugins),g=Ml(m.plugins),h=Zf(y=d,"mobile")?y.sections().mobile:{},f=((y,C,k,E)=>y&&Zf(C,"mobile")?E:k)(c,d,g,h.plugins?Ml(h.plugins):g),b=((y,C)=>[...Ml(y),...Ml(C)])(p,f);var y;return ot.extend(m,{forced_plugins:p,plugins:b})})(t,i,o,l)})(dS||dO,dS,g=n,o.defaultOptions,g);var g;this.options=((p,g)=>{const h={},f={},b=(C,k,E)=>{const N=rp(k,E);return CS(N)?(f[C]=N.value,!0):(console.warn(yS(`Invalid value passed for the ${C} option`,N)),!1)},y=C=>It(h,C);return{register:(C,k)=>{const E=Nt(k.processor)?(R=>{const I=(()=>{switch(R){case"array":return Ee;case"boolean":return xn;case"function":return Yt;case"number":return nn;case"object":return At;case"string":return Nt;case"string[]":return RO;case"object[]":return q=>Sn(q,At);case"regexp":return q=>Ce(q,RegExp);default:return ye}})();return q=>rp(q,I,`The value must be a ${R}.`)})(k.processor):k.processor,N=((R,I,q)=>{if(!dt(I)){const H=rp(I,q);if(CS(H))return H.value;console.error(yS(`Invalid default value passed for the "${R}" option`,H))}})(C,k.default,E);h[C]={...k,default:N,processor:E},ce(f,C).orThunk(()=>ce(g,C)).each(R=>b(C,R,E))},isRegistered:y,get:C=>ce(f,C).orThunk(()=>ce(h,C).map(k=>k.default)).getOrUndefined(),set:(C,k)=>{if(y(C)){const E=h[C];return E.immutable?(console.error(`"${C}" is an immutable option and cannot be updated`),!1):b(C,k,E.processor)}return console.warn(`"${C}" is not a registered option. Ensure the option has been registered before setting a value.`),!1},unset:C=>{const k=y(C);return k&&delete f[C],k},isSet:C=>It(f,C)}})(0,a),(p=>{const g=p.options.register;g("id",{processor:"string",default:p.id}),g("selector",{processor:"string"}),g("target",{processor:"object"}),g("suffix",{processor:"string"}),g("cache_suffix",{processor:"string"}),g("base_url",{processor:"string"}),g("referrer_policy",{processor:"string",default:""}),g("language_load",{processor:"boolean",default:!0}),g("inline",{processor:"boolean",default:!1}),g("iframe_attrs",{processor:"object",default:{}}),g("doctype",{processor:"string",default:"<!DOCTYPE html>"}),g("document_base_url",{processor:"string",default:p.documentBaseUrl}),g("body_id",{processor:Rh(p,"tinymce"),default:"tinymce"}),g("body_class",{processor:Rh(p),default:""}),g("content_security_policy",{processor:"string",default:""}),g("br_in_pre",{processor:"boolean",default:!0}),g("forced_root_block",{processor:h=>{const f=Nt(h)&&Qt(h);return f?{value:h,valid:f}:{valid:!1,message:"Must be a non-empty string."}},default:"p"}),g("forced_root_block_attrs",{processor:"object",default:{}}),g("newline_behavior",{processor:h=>{const f=Lt(["block","linebreak","invert","default"],h);return f?{value:h,valid:f}:{valid:!1,message:"Must be one of: block, linebreak, invert or default."}},default:"default"}),g("br_newline_selector",{processor:"string",default:".mce-toc h2,figcaption,caption"}),g("no_newline_selector",{processor:"string",default:""}),g("keep_styles",{processor:"boolean",default:!0}),g("end_container_on_empty_block",{processor:h=>xn(h)||Nt(h)?{valid:!0,value:h}:{valid:!1,message:"Must be boolean or a string"},default:"blockquote"}),g("font_size_style_values",{processor:"string",default:"xx-small,x-small,small,medium,large,x-large,xx-large"}),g("font_size_legacy_values",{processor:"string",default:"xx-small,small,medium,large,x-large,xx-large,300%"}),g("font_size_classes",{processor:"string",default:""}),g("automatic_uploads",{processor:"boolean",default:!0}),g("images_reuse_filename",{processor:"boolean",default:!1}),g("images_replace_blob_uris",{processor:"boolean",default:!0}),g("icons",{processor:"string",default:""}),g("icons_url",{processor:"string",default:""}),g("images_upload_url",{processor:"string",default:""}),g("images_upload_base_path",{processor:"string",default:""}),g("images_upload_credentials",{processor:"boolean",default:!1}),g("images_upload_handler",{processor:"function"}),g("language",{processor:"string",default:"en"}),g("language_url",{processor:"string",default:""}),g("entity_encoding",{processor:"string",default:"named"}),g("indent",{processor:"boolean",default:!0}),g("indent_before",{processor:"string",default:"p,h1,h2,h3,h4,h5,h6,blockquote,div,title,style,pre,script,td,th,ul,ol,li,dl,dt,dd,area,table,thead,tfoot,tbody,tr,section,details,summary,article,hgroup,aside,figure,figcaption,option,optgroup,datalist"}),g("indent_after",{processor:"string",default:"p,h1,h2,h3,h4,h5,h6,blockquote,div,title,style,pre,script,td,th,ul,ol,li,dl,dt,dd,area,table,thead,tfoot,tbody,tr,section,details,summary,article,hgroup,aside,figure,figcaption,option,optgroup,datalist"}),g("indent_use_margin",{processor:"boolean",default:!1}),g("indentation",{processor:"string",default:"40px"}),g("content_css",{processor:h=>{const f=!1===h||Nt(h)||Sn(h,Nt);return f?Nt(h)?{value:te(h.split(","),Wt),valid:f}:Ee(h)?{value:h,valid:f}:!1===h?{value:[],valid:f}:{value:h,valid:f}:{valid:!1,message:"Must be false, a string or an array of strings."}},default:Yu(p)?[]:["default"]}),g("content_style",{processor:"string"}),g("content_css_cors",{processor:"boolean",default:!1}),g("font_css",{processor:h=>{const f=Nt(h)||Sn(h,Nt);return f?{value:Ee(h)?h:te(h.split(","),Wt),valid:f}:{valid:!1,message:"Must be a string or an array of strings."}},default:[]}),g("inline_boundaries",{processor:"boolean",default:!0}),g("inline_boundaries_selector",{processor:"string",default:"a[href],code,span.mce-annotation"}),g("object_resizing",{processor:h=>{const f=xn(h)||Nt(h);return f?!1===h||Vu.isiPhone()||Vu.isiPad()?{value:"",valid:f}:{value:!0===h?"table,img,figure.image,div,video,iframe":h,valid:f}:{valid:!1,message:"Must be boolean or a string"}},default:!Pk}),g("resize_img_proportional",{processor:"boolean",default:!0}),g("event_root",{processor:"object"}),g("service_message",{processor:"string"}),g("theme",{processor:h=>!1===h||Nt(h)||Yt(h),default:"silver"}),g("theme_url",{processor:"string"}),g("formats",{processor:"object"}),g("format_empty_lines",{processor:"boolean",default:!1}),g("format_noneditable_selector",{processor:"string",default:""}),g("preview_styles",{processor:h=>{const f=!1===h||Nt(h);return f?{value:!1===h?"":h,valid:f}:{valid:!1,message:"Must be false or a string"}},default:"font-family font-size font-weight font-style text-decoration text-transform color background-color border border-radius outline text-shadow"}),g("custom_ui_selector",{processor:"string",default:""}),g("hidden_input",{processor:"boolean",default:!0}),g("submit_patch",{processor:"boolean",default:!0}),g("encoding",{processor:"string"}),g("add_form_submit_trigger",{processor:"boolean",default:!0}),g("add_unload_trigger",{processor:"boolean",default:!0}),g("custom_undo_redo_levels",{processor:"number",default:0}),g("disable_nodechange",{processor:"boolean",default:!1}),g("readonly",{processor:"boolean",default:!1}),g("editable_root",{processor:"boolean",default:!0}),g("plugins",{processor:"string[]",default:[]}),g("external_plugins",{processor:"object"}),g("forced_plugins",{processor:"string[]"}),g("model",{processor:"string",default:p.hasPlugin("rtc")?"plugin":"dom"}),g("model_url",{processor:"string"}),g("block_unsupported_drop",{processor:"boolean",default:!0}),g("visual",{processor:"boolean",default:!0}),g("visual_table_class",{processor:"string",default:"mce-item-table"}),g("visual_anchor_class",{processor:"string",default:"mce-item-anchor"}),g("iframe_aria_text",{processor:"string",default:"Rich Text Area. Press ALT-0 for help."}),g("setup",{processor:"function"}),g("init_instance_callback",{processor:"function"}),g("url_converter",{processor:"function",default:p.convertURL}),g("url_converter_scope",{processor:"object",default:p}),g("urlconverter_callback",{processor:"function"}),g("allow_conditional_comments",{processor:"boolean",default:!1}),g("allow_html_data_urls",{processor:"boolean",default:!1}),g("allow_svg_data_urls",{processor:"boolean"}),g("allow_html_in_named_anchor",{processor:"boolean",default:!1}),g("allow_script_urls",{processor:"boolean",default:!1}),g("allow_unsafe_link_target",{processor:"boolean",default:!1}),g("convert_fonts_to_spans",{processor:"boolean",default:!0,deprecated:!0}),g("fix_list_elements",{processor:"boolean",default:!1}),g("preserve_cdata",{processor:"boolean",default:!1}),g("remove_trailing_brs",{processor:"boolean",default:!0}),g("inline_styles",{processor:"boolean",default:!0,deprecated:!0}),g("element_format",{processor:"string",default:"html"}),g("entities",{processor:"string"}),g("schema",{processor:"string",default:"html5"}),g("convert_urls",{processor:"boolean",default:!0}),g("relative_urls",{processor:"boolean",default:!0}),g("remove_script_host",{processor:"boolean",default:!0}),g("custom_elements",{processor:"string"}),g("extended_valid_elements",{processor:"string"}),g("invalid_elements",{processor:"string"}),g("invalid_styles",{processor:Wu}),g("valid_children",{processor:"string"}),g("valid_classes",{processor:Wu}),g("valid_elements",{processor:"string"}),g("valid_styles",{processor:Wu}),g("verify_html",{processor:"boolean",default:!0}),g("auto_focus",{processor:h=>Nt(h)||!0===h}),g("browser_spellcheck",{processor:"boolean",default:!1}),g("protect",{processor:"array"}),g("images_file_types",{processor:"string",default:"jpeg,jpg,jpe,jfi,jif,jfif,png,gif,bmp,webp"}),g("deprecation_warnings",{processor:"boolean",default:!0}),g("a11y_advanced_options",{processor:"boolean",default:!1}),g("api_key",{processor:"string"}),g("paste_block_drop",{processor:"boolean",default:!1}),g("paste_data_images",{processor:"boolean",default:!0}),g("paste_preprocess",{processor:"function"}),g("paste_postprocess",{processor:"function"}),g("paste_webkit_styles",{processor:"string",default:"none"}),g("paste_remove_styles_if_webkit",{processor:"boolean",default:!0}),g("paste_merge_formats",{processor:"boolean",default:!0}),g("smart_paste",{processor:"boolean",default:!0}),g("paste_as_text",{processor:"boolean",default:!1}),g("paste_tab_spaces",{processor:"number",default:4}),g("text_patterns",{processor:h=>Sn(h,At)||!1===h?{value:_h(!1===h?[]:h),valid:!0}:{valid:!1,message:"Must be an array of objects or false."},default:[{start:"*",end:"*",format:"italic"},{start:"**",end:"**",format:"bold"},{start:"#",format:"h1"},{start:"##",format:"h2"},{start:"###",format:"h3"},{start:"####",format:"h4"},{start:"#####",format:"h5"},{start:"######",format:"h6"},{start:"1. ",cmd:"InsertOrderedList"},{start:"* ",cmd:"InsertUnorderedList"},{start:"- ",cmd:"InsertUnorderedList"}]}),g("text_patterns_lookup",{processor:h=>{return Yt(h)?{value:(f=h,b=>{const y=f(b);return _h(y)}),valid:!0}:{valid:!1,message:"Must be a single function"};var f},default:h=>[]}),g("noneditable_class",{processor:"string",default:"mceNonEditable"}),g("editable_class",{processor:"string",default:"mceEditable"}),g("noneditable_regexp",{processor:h=>Sn(h,Nh)?{value:h,valid:!0}:Nh(h)?{value:[h],valid:!0}:{valid:!1,message:"Must be a RegExp or an array of RegExp."},default:[]}),g("table_tab_navigation",{processor:"boolean",default:!0}),g("highlight_on_focus",{processor:"boolean",default:!1}),g("xss_sanitization",{processor:"boolean",default:!0}),g("details_initial_state",{processor:h=>{const f=Lt(["inherited","collapsed","expanded"],h);return f?{value:h,valid:f}:{valid:!1,message:"Must be one of: inherited, collapsed, or expanded."}},default:"inherited"}),g("details_serialized_state",{processor:h=>{const f=Lt(["inherited","collapsed","expanded"],h);return f?{value:h,valid:f}:{valid:!1,message:"Must be one of: inherited, collapsed, or expanded."}},default:"inherited"}),g("init_content_sync",{processor:"boolean",default:!1}),g("newdocument_content",{processor:"string",default:""}),p.on("ScriptsLoaded",()=>{g("directionality",{processor:"string",default:Mo.isRtl()?"rtl":void 0}),g("placeholder",{processor:"string",default:Mk.getAttrib(p.getElement(),"placeholder")})})})(r);const s=this.options.get;s("deprecation_warnings")&&((p,g)=>{((h,f)=>{const b=(t=>{const e=iC(t,bR),n=t.forced_root_block;return!1!==n&&""!==n||e.push("forced_root_block (false only)"),To(e)})(h),y=(t=>lC(t,yR))(f),C=y.length>0,k=b.length>0,E="mobile"===f.theme;if(C||k||E){const N="\n- ",R=E?`\n\nThemes:${N}mobile`:"",I=C?`\n\nPlugins:${N}${y.join(N)}`:"",q=k?`\n\nOptions:${N}${b.join(N)}`:"";console.warn("The following deprecated features are currently enabled and have been removed in TinyMCE 6.0. These features will no longer work and should be removed from the TinyMCE configuration. See https://www.tiny.cloud/docs/tinymce/6/migration-from-5x/ for more information."+R+I+q)}})(p,g),((h,f)=>{const b=(t=>iC(t,vR))(h),y=(t=>lC(t,sC.map(e=>e.name)))(f),C=y.length>0,k=b.length>0;if(C||k){const E="\n- ",N=C?`\n\nPlugins:${E}${y.map(kR).join(E)}`:"",R=k?`\n\nOptions:${E}${b.join(E)}`:"";console.warn("The following deprecated features are currently enabled but will be removed soon."+N+R)}})(p,g)})(n,a);const i=s("suffix");i&&(o.suffix=i),this.suffix=o.suffix;const l=s("base_url");l&&o._setBaseUrl(l),this.baseUri=o.baseURI;const c=Ku(r);c&&(Ur.ScriptLoader._setReferrerPolicy(c),me.DOM.styleSheetLoader._setReferrerPolicy(c));const d=jh(r);st(d)&&me.DOM.styleSheetLoader._setContentCssCors(d),vn.languageLoad=s("language_load"),vn.baseURL=o.baseURL,this.setDirty(!1),this.documentBaseURI=new Uo(Ah(r),{base_uri:this.baseUri}),this.baseURI=this.baseUri,this.inline=Yu(r),this.hasVisual=kE(r),this.shortcuts=new SS(this),this.editorCommands=new fS(this),(t=>{var e;(t=>{(e=>{const n=o=>()=>{J("left,center,right,justify".split(","),r=>{o!==r&&e.formatter.remove("align"+r)}),"none"!==o&&(e.formatter.toggle("align"+o,void 0),e.nodeChanged())};e.editorCommands.addCommands({JustifyLeft:n("left"),JustifyCenter:n("center"),JustifyRight:n("right"),JustifyFull:n("justify"),JustifyNone:n("none")})})(t),(e=>{const n=o=>()=>{const r=e.selection,a=r.isCollapsed()?[e.dom.getParent(r.getNode(),e.dom.isBlock)]:r.getSelectedBlocks();return ie(a,s=>st(e.formatter.matchNode(s,o)))};e.editorCommands.addCommands({JustifyLeft:n("alignleft"),JustifyCenter:n("aligncenter"),JustifyRight:n("alignright"),JustifyFull:n("alignjustify")},"state")})(t)})(t),(e=t).editorCommands.addCommands({"Cut,Copy,Paste":n=>{const o=e.getDoc();let r;try{o.execCommand(n)}catch{r=!0}if("paste"!==n||o.queryCommandEnabled(n)||(r=!0),r||!o.queryCommandSupported(n)){let a=e.translate("Your browser doesn't support direct access to the clipboard. Please use the Ctrl+X/C/V keyboard shortcuts instead.");(Jt.os.isMacOS()||Jt.os.isiOS())&&(a=a.replace(/Ctrl\+/g,"\u2318+")),e.notificationManager.open({text:a,type:"error"})}}}),(e=>{e.editorCommands.addCommands({mceAddUndoLevel:()=>{e.undoManager.add()},mceEndUndoLevel:()=>{e.undoManager.add()},Undo:()=>{e.undoManager.undo()},Redo:()=>{e.undoManager.redo()}})})(t),(e=>{e.editorCommands.addCommands({mceSelectNodeDepth:(n,o,r)=>{let a=0;e.dom.getParent(e.selection.getNode(),s=>!it(s)||a++!==r||(e.selection.select(s),!1),e.getBody())},mceSelectNode:(n,o,r)=>{e.selection.select(r)},selectAll:()=>{const n=e.dom.getParent(e.selection.getStart(),Po);if(n){const o=e.dom.createRng();o.selectNodeContents(n),e.selection.setRng(o)}}})})(t),(e=>{e.editorCommands.addCommands({mceCleanup:()=>{const n=e.selection.getBookmark();e.setContent(e.getContent()),e.selection.moveToBookmark(n)},insertImage:(n,o,r)=>{Gd(e,e.dom.createHTML("img",{src:r}))},insertHorizontalRule:()=>{e.execCommand("mceInsertContent",!1,"<hr>")},insertText:(n,o,r)=>{Gd(e,e.dom.encode(r))},insertHTML:(n,o,r)=>{Gd(e,r)},mceInsertContent:(n,o,r)=>{Gd(e,r)},mceSetContent:(n,o,r)=>{e.setContent(r)},mceReplaceContent:(n,o,r)=>{e.execCommand("mceInsertContent",!1,r.replace(/\{\$selection\}/g,e.selection.getContent({format:"text"})))},mceNewDocument:()=>{e.setContent(LE(e))}})})(t),(e=>{const n=(o,r,a)=>{const s=Nt(a)?{href:a}:a,i=e.dom.getParent(e.selection.getNode(),"a");At(s)&&Nt(s.href)&&(s.href=s.href.replace(/ /g,"%20"),i&&s.href||e.formatter.remove("link"),s.href&&e.formatter.apply("link",s,i))};e.editorCommands.addCommands({unlink:()=>{if(e.selection.isEditable()){if(e.selection.isCollapsed()){const o=e.dom.getParent(e.selection.getStart(),"a");return void(o&&e.dom.remove(o,!0))}e.formatter.remove("link")}},mceInsertLink:n,createLink:n})})(t),(e=>{e.editorCommands.addCommands({Indent:()=>{S1(e,"indent")},Outdent:()=>{k1(e)}}),e.editorCommands.addCommands({Outdent:()=>C1(e)},"state")})(t),(e=>{e.editorCommands.addCommands({insertParagraph:()=>{Ud(Rw,e)},mceInsertNewLine:(n,o,r)=>{Mw(e,r)},InsertLineBreak:(n,o,r)=>{Ud(Ow,e)}})})(t),(e=>{var n;(n=e).editorCommands.addCommands({"InsertUnorderedList,InsertOrderedList":o=>{n.getDoc().execCommand(o);const r=n.dom.getParent(n.selection.getNode(),"ol,ul");if(r){const a=r.parentNode;if(a&&/^(H[1-6]|P|ADDRESS|PRE)$/.test(a.nodeName)){const s=n.selection.getBookmark();n.dom.split(a,r),n.selection.moveToBookmark(s)}}}}),(n=>{n.editorCommands.addCommands({"InsertUnorderedList,InsertOrderedList":o=>{const r=n.dom.getParent(n.selection.getNode(),"ul,ol");return r&&("insertunorderedlist"===o&&"UL"===r.tagName||"insertorderedlist"===o&&"OL"===r.tagName)}},"state")})(e)})(t),(e=>{(n=>{const o=(r,a)=>{n.formatter.toggle(r,a),n.nodeChanged()};n.editorCommands.addCommands({"Bold,Italic,Underline,Strikethrough,Superscript,Subscript":r=>{o(r)},"ForeColor,HiliteColor":(r,a,s)=>{o(r,{value:s})},BackColor:(r,a,s)=>{o("hilitecolor",{value:s})},FontName:(r,a,s)=>{((i,l)=>{const c=mS(i,l);i.formatter.toggle("fontname",{value:xO(c)}),i.nodeChanged()})(n,s)},FontSize:(r,a,s)=>{var i;(i=n).formatter.toggle("fontsize",{value:mS(i,s)}),i.nodeChanged()},LineHeight:(r,a,s)=>{var i;(i=n).formatter.toggle("lineheight",{value:String(s)}),i.nodeChanged()},Lang:(r,a,s)=>{var i;o(r,{value:s.code,customValue:null!==(i=s.customCode)&&void 0!==i?i:null})},RemoveFormat:r=>{n.formatter.remove(r)},mceBlockQuote:()=>{o("blockquote")},FormatBlock:(r,a,s)=>{o(Nt(s)?s:"p")},mceToggleFormat:(r,a,s)=>{o(s)}})})(e),(n=>{const o=r=>n.formatter.match(r);n.editorCommands.addCommands({"Bold,Italic,Underline,Strikethrough,Superscript,Subscript":r=>o(r),mceBlockQuote:()=>o("blockquote")},"state"),n.editorCommands.addQueryValueHandler("FontName",()=>{return tp(r=n,a=>CO(r.getBody(),a.dom)).getOr("");var r}),n.editorCommands.addQueryValueHandler("FontSize",()=>{return tp(r=n,a=>yO(r.getBody(),a.dom)).getOr("");var r}),n.editorCommands.addQueryValueHandler("LineHeight",()=>{return tp(r=n,a=>{const s=A(r.getBody());return ug(a,l=>za(l,"line-height"),Ct(re,s)).getOrThunk(()=>{const l=parseFloat(xo(a,"line-height")),c=parseFloat(xo(a,"font-size"));return String(l/c)})}).getOr("");var r})})(e)})(t),(e=>{e.editorCommands.addCommands({mceRemoveNode:(n,o,r)=>{const a=r??e.selection.getNode();if(a!==e.getBody()){const s=e.selection.getBookmark();e.dom.remove(a,!0),e.selection.moveToBookmark(s)}},mcePrint:()=>{e.getWin().print()},mceFocus:(n,o,r)=>{var a;(a=e).removed||(!0===r?Pm(a):(i=>{const l=i.selection,c=i.getBody();let d=l.getRng();i.quirks.refreshContentEditable(),st(i.bookmark)&&!is(i)&&Am(i).each(m=>{i.selection.setRng(m),d=m});const u=(m=i,p=l.getNode(),m.dom.getParent(p,g=>"true"===m.dom.getContentEditable(g)));var m,p;if(u&&i.dom.isChildOf(u,c))return Dm(u),bv(i,d),void Pm(i);i.inline||(Jt.browser.isOpera()||Dm(c),i.getWin().focus()),(Jt.browser.isFirefox()||i.inline)&&(Dm(c),bv(i,d)),Pm(i)})(a))},mceToggleVisualAid:()=>{e.hasVisual=!e.hasVisual,e.addVisual()}})})(t)})(this);const u=s("cache_suffix");u&&(Jt.cacheSuffix=u.replace(/^[\?\&]+/,"")),this.ui={registry:BO(),styleSheetLoader:void 0,show:jt,hide:jt,setEnabled:jt,isEnabled:ye},this.mode=(p=>{const g=Ye("design"),h=Ye({design:{activate:jt,deactivate:jt,editorReadOnly:!1},readonly:{activate:jt,deactivate:jt,editorReadOnly:!0}});return(f=p).serializer?pS(f):f.on("PreInit",()=>{pS(f)}),(f=>{f.on("ShowCaret",b=>{bs(f)&&b.preventDefault()}),f.on("ObjectSelected",b=>{bs(f)&&b.preventDefault()})})(p),{isReadOnly:()=>bs(p),set:f=>((b,y,C,k)=>{if(k!==C.get()){if(!It(y,k))throw new Error(`Editor mode '${k}' is invalid`);b.initialized?wS(b,C,y,k):b.on("init",()=>wS(b,C,y,k))}})(p,h.get(),g,f),get:()=>g.get(),register:(f,b)=>{h.set(((y,C,k)=>{if(Lt(AO,C))throw new Error(`Cannot override default mode ${C}`);return{...y,[C]:{...k,deactivate:()=>{try{k.deactivate()}catch(E){console.error(`problem while deactivating editor mode ${C}:`,E)}}}}})(h.get(),f,b))}};var f})(r),o.dispatch("SetupEditor",{editor:this});const m=NE(r);Yt(m)&&m.call(r,r)}render(){(e=>{const n=e.id;Mo.setCode(Ph(e));const o=()=>{ps.unbind(window,"ready",o),e.render()};if(!Ga.Event.domLoaded)return void ps.bind(window,"ready",o);if(!e.getElement())return;const r=A(e.getElement()),a=Ql(r);var l;e.on("remove",()=>{Ta(r.dom.attributes,i=>Le(r,i.name)),Do(r,a)}),e.ui.styleSheetLoader=Ap.forElement(r,{contentCssCors:jh(l=e),referrerPolicy:Ku(l)}),Yu(e)?e.inline=!0:(e.orgVisibility=e.getElement().style.visibility,e.getElement().style.visibility="hidden");const s=e.getElement().form||ps.getParent(n,"form");s&&(e.formElement=s,pE(e)&&!mu(e.getElement())&&(ps.insertAfter(ps.create("input",{type:"hidden",name:n}),n),e.hasHiddenInput=!0),e.formEventDelegate=i=>{e.dispatch(i.type,i)},ps.bind(s,"submit reset",e.formEventDelegate),e.on("reset",()=>{e.resetContent()}),!hE(e)||s.submit.nodeType||s.submit.length||s._mceOldSubmit||(s._mceOldSubmit=s.submit,s.submit=()=>(e.editorManager.triggerSave(),e.setDirty(!1),s._mceOldSubmit(s)))),e.windowManager=mC(e),e.notificationManager=uC(e),"xml"===e.options.get("encoding")&&e.on("GetContent",i=>{i.save&&(i.content=ps.encode(i.content))}),bE(e)&&e.on("submit",()=>{e.initialized&&e.save()}),vE(e)&&(e._beforeUnload=()=>{!e.initialized||e.destroyed||e.isHidden()||e.save({format:"raw",no_events:!0,set_dirty:!1})},e.editorManager.on("BeforeUnload",e._beforeUnload)),e.editorManager.add(e),((t,e)=>{const n=Ur.ScriptLoader,o=()=>{!t.removed&&(r=>{const a=$s(r);return!Nt(a)||st(fs.get(a))})(t)&&(r=>{const a=Gu(r);return st(ei.get(a))})(t)&&lO(t)};((r,a)=>{const s=$s(r);if(Nt(s)&&!iS(s)&&!It(fs.urls,s)){const i=lE(r),l=i?r.documentBaseURI.toAbsolute(i):`themes/${s}/theme${a}.js`;fs.load(s,l).catch(()=>{yl(r,"ThemeLoadError",Cl("theme",l,s))})}})(t,e),((r,a)=>{const s=Gu(r);if("plugin"!==s&&!It(ei.urls,s)){const i=cE(r),l=Nt(i)?r.documentBaseURI.toAbsolute(i):`models/${s}/model${a}.js`;ei.load(s,l).catch(()=>{yl(r,"ModelLoadError",Cl("model",l,s))})}})(t,e),((r,a)=>{const s=Ph(a),i=Zk(a);if(!Mo.hasCode(s)&&"en"!==s){const l=Qt(i)?i:`${a.editorManager.baseURL}/langs/${s}.js`;r.add(l).catch(()=>{yl(a,"LanguageLoadError",Cl("language",l,s))})}})(n,t),((r,a,s)=>{const i=lS(a,"default",s),l=(c=a,S.from(Kk(c)).filter(Qt).map(d=>({url:d,name:S.none()}))).orThunk(()=>lS(a,Dh(a),""));var c;J((c=>{const d=[],u=m=>{d.push(m)};for(let m=0;m<c.length;m++)c[m].each(u);return d})([i,l]),c=>{r.add(c.url).catch(()=>{var d,u,m;d=a,u=c.url,m=c.name.getOrUndefined(),yl(d,"IconsLoadError",Cl("icons",u,m))})})})(n,t,e),((r,a)=>{const s=(i,l)=>{ni.load(i,l).catch(()=>{yl(r,"PluginLoadError",Cl("plugin",l,i))})};le(xE(r),(i,l)=>{s(l,i),r.options.set("plugins",Tc(r).concat(l))}),J(Tc(r),i=>{!(i=ot.trim(i))||ni.urls[i]||iS(i)||s(i,`plugins/${i}/plugin${a}.js`)})})(t,e),n.loadQueue().then(o,o)})(e,e.suffix)})(this)}focus(e){this.execCommand("mceFocus",!1,e)}hasFocus(){return is(this)}translate(e){return Mo.translate(e)}getParam(e,n,o){const r=this.options;return r.isRegistered(e)||(st(o)?r.register(e,{processor:o,default:n}):r.register(e,{processor:ye,default:n})),r.isSet(e)||dt(n)?r.get(e):n}hasPlugin(e,n){return!(!Lt(Tc(this),e)||n&&void 0===ni.get(e))}nodeChanged(e){this._nodeChangeDispatcher.nodeChanged(e)}addCommand(e,n,o){this.editorCommands.addCommand(e,n,o)}addQueryStateHandler(e,n,o){this.editorCommands.addQueryStateHandler(e,n,o)}addQueryValueHandler(e,n,o){this.editorCommands.addQueryValueHandler(e,n,o)}addShortcut(e,n,o,r){this.shortcuts.add(e,n,o,r)}execCommand(e,n,o,r){return this.editorCommands.execCommand(e,n,o,r)}queryCommandState(e){return this.editorCommands.queryCommandState(e)}queryCommandValue(e){return this.editorCommands.queryCommandValue(e)}queryCommandSupported(e){return this.editorCommands.queryCommandSupported(e)}show(){const e=this;e.hidden&&(e.hidden=!1,e.inline?e.getBody().contentEditable="true":(ys.show(e.getContainer()),ys.hide(e.id)),e.load(),e.dispatch("show"))}hide(){const e=this;e.hidden||(e.save(),e.inline?(e.getBody().contentEditable="false",e===e.editorManager.focusedEditor&&(e.editorManager.focusedEditor=null)):(ys.hide(e.getContainer()),ys.setStyle(e.id,"display",e.orgDisplay)),e.hidden=!0,e.dispatch("hide"))}isHidden(){return this.hidden}setProgressState(e,n){this.dispatch("ProgressState",{state:e,time:n})}load(e={}){const n=this,o=n.getElement();if(n.removed)return"";if(o){const r={...e,load:!0},a=mu(o)?o.value:o.innerHTML,s=n.setContent(a,r);return r.no_events||n.dispatch("LoadContent",{...r,element:o}),s}return""}save(e={}){const n=this;let o=n.getElement();if(!o||!n.initialized||n.removed)return"";const r={...e,save:!0,element:o};let a=n.getContent(r);const s={...r,content:a};if(s.no_events||n.dispatch("SaveContent",s),"raw"===s.format&&n.dispatch("RawSaveContent",s),a=s.content,mu(o))o.value=a;else{!e.is_removing&&n.inline||(o.innerHTML=a);const i=ys.getParent(n.id,"form");i&&DO(i.elements,l=>l.name!==n.id||(l.value=a,!1))}return s.element=r.element=o=null,!1!==s.set_dirty&&n.setDirty(!1),a}setContent(e,n){return Hg(this,e,n)}getContent(e){return((n,o={})=>{const r=(a=o,s=o.format?o.format:"html",{...a,format:s,get:!0,getInner:!0});var a,s;return jy(n,r).fold(qe,a=>{const s=(l=a,zg(n).editor.getContent(l));var l;return zy(n,s,a)})})(this,e)}insertContent(e,n){n&&(e=kS({content:e},n)),this.execCommand("mceInsertContent",!1,e)}resetContent(e){void 0===e?Hg(this,this.startContent,{format:"raw"}):Hg(this,e),this.undoManager.reset(),this.setDirty(!1),this.nodeChanged()}isDirty(){return!this.isNotDirty}setDirty(e){const n=!this.isNotDirty;this.isNotDirty=!e,e&&e!==n&&this.dispatch("dirty")}getContainer(){const e=this;return e.container||(e.container=e.editorContainer||ys.get(e.id+"_parent")),e.container}getContentAreaContainer(){return this.contentAreaContainer}getElement(){return this.targetElm||(this.targetElm=ys.get(this.id)),this.targetElm}getWin(){const e=this;if(!e.contentWindow){const n=e.iframeElement;n&&(e.contentWindow=n.contentWindow)}return e.contentWindow}getDoc(){const e=this;if(!e.contentDocument){const n=e.getWin();n&&(e.contentDocument=n.document)}return e.contentDocument}getBody(){var e,n;const o=this.getDoc();return null!==(n=null!==(e=this.bodyElement)&&void 0!==e?e:o?.body)&&void 0!==n?n:null}convertURL(e,n,o){const r=this,a=r.options.get,s=AE(r);return Yt(s)?s.call(r,e,o,!0,n):!a("convert_urls")||"link"===o||At(o)&&"LINK"===o.nodeName||0===e.indexOf("file:")||0===e.length?e:a("relative_urls")?r.documentBaseURI.toRelative(e):e=r.documentBaseURI.toAbsolute(e,a("remove_script_host"))}addVisual(e){((n,o)=>{var a;a=o,Te(this).editor.addVisual(a)})(0,e)}setEditableRoot(e){var n,o;(n=this)._editableRoot!==(o=e)&&(n._editableRoot=o,n.readonly||(n.getBody().contentEditable=String(n.hasEditableRoot()),n.nodeChanged()),n.dispatch("EditableRootStateChange",{state:o}))}hasEditableRoot(){return this._editableRoot}remove(){(e=>{if(!e.removed){const{_selectionOverrides:n,editorUpload:o}=e,r=e.getBody(),a=e.getElement();r&&e.save({is_removing:!0}),e.removed=!0,e.unbindAllNativeEvents(),e.hasHiddenInput&&st(a?.nextSibling)&&gd.remove(a.nextSibling),e.dispatch("remove"),e.editorManager.remove(e),!e.inline&&r&&gd.setStyle((s=e).id,"display",s.orgDisplay),(s=>{s.dispatch("detach")})(e),gd.remove(e.getContainer()),fd(n),fd(o),e.destroy()}var s})(this)}destroy(e){((n,o)=>{const{selection:r,dom:a}=n;n.destroyed||(o||n.removed?(o||(n.editorManager.off("beforeunload",n._beforeUnload),n.theme&&n.theme.destroy&&n.theme.destroy(),fd(r),fd(a)),(s=>{const i=s.formElement;i&&(i._mceOldSubmit&&(i.submit=i._mceOldSubmit,delete i._mceOldSubmit),gd.unbind(i,"submit reset",s.formEventDelegate))})(n),(s=>{const i=s;i.contentAreaContainer=i.formElement=i.container=i.editorContainer=null,i.bodyElement=i.contentDocument=i.contentWindow=null,i.iframeElement=i.targetElm=null;const l=s.selection;l&&(i.selection=l.win=l.dom=l.dom.doc=null)})(n),n.destroyed=!0):n.remove())})(this,e)}uploadImages(){return this.editorUpload.uploadImages()}_scanForImages(){return this.editorUpload.scanForImages()}}const ui=me.DOM,Zd=ot.each;let tu,ES=!1,Mn=[];const eu=t=>{const e=t.type;Zd(ka.get(),n=>{switch(e){case"scroll":n.dispatch("ScrollWindow",t);break;case"resize":n.dispatch("ResizeWindow",t)}})},_S=t=>{if(t!==ES){const e=me.DOM;t?(e.bind(window,"resize",eu),e.bind(window,"scroll",eu)):(e.unbind(window,"resize",eu),e.unbind(window,"scroll",eu)),ES=t}},NS=t=>{const e=Mn;return Mn=Ot(Mn,n=>t!==n),ka.activeEditor===t&&(ka.activeEditor=Mn.length>0?Mn[0]:null),ka.focusedEditor===t&&(ka.focusedEditor=null),e.length!==Mn.length},PO="CSS1Compat"!==document.compatMode,ka={...op,baseURI:null,baseURL:null,defaultOptions:{},documentBaseURL:null,suffix:null,majorVersion:"6",minorVersion:"6.0",releaseDate:"2023-07-12",i18n:Mo,activeEditor:null,focusedEditor:null,setup(){const t=this;let e="",n="",o=Uo.getDocumentBaseUrl(document.location);/^[^:]+:\/\/\/?[^\/]+\//.test(o)&&(o=o.replace(/[\?#].*$/,"").replace(/[\/\\][^\/]+$/,""),/[\/\\]$/.test(o)||(o+="/"));const r=window.tinymce||window.tinyMCEPreInit;if(r)e=r.base||r.baseURL,n=r.suffix;else{const s=document.getElementsByTagName("script");for(let i=0;i<s.length;i++){const l=s[i].src||"";if(""===l)continue;const c=l.substring(l.lastIndexOf("/"));if(/tinymce(\.full|\.jquery|)(\.min|\.dev|)\.js/.test(l)){-1!==c.indexOf(".min")&&(n=".min"),e=l.substring(0,l.lastIndexOf("/"));break}}if(!e&&document.currentScript){const i=document.currentScript.src;-1!==i.indexOf(".min")&&(n=".min"),e=i.substring(0,i.lastIndexOf("/"))}}var a;t.baseURL=new Uo(o).toAbsolute(e),t.documentBaseURL=o,t.baseURI=new Uo(t.baseURL),t.suffix=n,(a=t).on("AddEditor",Ct(z_,a)),a.on("RemoveEditor",Ct(H_,a))},overrideDefaults(t){const e=t.base_url;e&&this._setBaseUrl(e);const n=t.suffix;n&&(this.suffix=n),this.defaultOptions=t;const o=t.plugin_base_urls;void 0!==o&&le(o,(r,a)=>{vn.PluginManager.urls[a]=r})},init(t){const e=this;let n;const o=ot.makeMap("area base basefont br col frame hr img input isindex link meta param embed source wbr track colgroup option table tbody tfoot thead tr th td script noscript style textarea video audio iframe object menu"," ");let r=s=>{n=s};const a=()=>{let s=0;const i=[];let l;var c;ui.unbind(window,"ready",a),(()=>{const d=t.onpageload;d&&d.apply(e,[])})(),l=((c,d)=>{const u=[],m=Yt(d)?p=>ie(u,g=>d(g,p)):p=>Lt(u,p);for(let p=0,g=c.length;p<g;p++){const h=c[p];m(h)||u.push(h)}return u})((c=t,Jt.browser.isIE()||Jt.browser.isEdge()?(bd("TinyMCE does not support the browser you are using. For a list of supported browsers please see: https://www.tiny.cloud/docs/tinymce/6/support/#supportedwebbrowsers"),[]):PO?(bd("Failed to initialize the editor as the document is not in standards mode. TinyMCE requires standards mode."),[]):Nt(c.selector)?ui.select(c.selector):st(c.target)?[c.target]:[])),ot.each(l,c=>{var d;(d=e.get(c.id))&&d.initialized&&!(d.getContainer()||d.getBody()).parentNode&&(NS(d),d.unbindAllNativeEvents(),d.destroy(!0),d.removed=!0)}),l=ot.grep(l,c=>!e.get(c.id)),0===l.length?r([]):Zd(l,c=>{t.inline&&c.tagName.toLowerCase()in o?bd("Could not initialize inline editor on invalid inline target element",c):((d,u,m)=>{const p=new Jd(d,u,e);i.push(p),p.on("init",()=>{++s===l.length&&r(i)}),p.targetElm=p.targetElm||m,p.render()})((d=>{let u=d.id;return u||(u=ce(d,"name").filter(m=>!ui.get(m)).getOrThunk(ui.uniqueId),d.setAttribute("id",u)),u})(c),t,c)})};return ui.bind(window,"ready",a),new Promise(s=>{n?s(n):r=i=>{s(i)}})},get(t){return 0===arguments.length?Mn.slice(0):Nt(t)?he(Mn,e=>e.id===t).getOr(null):nn(t)&&Mn[t]?Mn[t]:null},add(t){const e=this,n=e.get(t.id);return n===t||(null===n&&Mn.push(t),_S(!0),e.activeEditor=t,e.dispatch("AddEditor",{editor:t}),tu||(tu=o=>{const r=e.dispatch("BeforeUnload");if(r.returnValue)return o.preventDefault(),o.returnValue=r.returnValue,r.returnValue},window.addEventListener("beforeunload",tu))),t},createEditor(t,e){return this.add(new Jd(t,e,this))},remove(t){const e=this;let n;if(t){if(!Nt(t))return n=t,Oe(e.get(n.id))?null:(NS(n)&&e.dispatch("RemoveEditor",{editor:n}),0===Mn.length&&window.removeEventListener("beforeunload",tu),n.remove(),_S(Mn.length>0),n);Zd(ui.select(t),o=>{n=e.get(o.id),n&&e.remove(n)})}else for(let o=Mn.length-1;o>=0;o--)e.remove(Mn[o])},execCommand(t,e,n){var o;const r=this,a=At(n)?null!==(o=n.id)&&void 0!==o?o:n.index:n;switch(t){case"mceAddEditor":return r.get(a)||new Jd(a,n.options,r).render(),!0;case"mceRemoveEditor":{const s=r.get(a);return s&&s.remove(),!0}case"mceToggleEditor":{const s=r.get(a);return s?(s.isHidden()?s.show():s.hide(),!0):(r.execCommand("mceAddEditor",!1,n),!0)}}return!!r.activeEditor&&r.activeEditor.execCommand(t,e,n)},triggerSave:()=>{Zd(Mn,t=>{t.save()})},addI18n:(t,e)=>{Mo.add(t,e)},translate:t=>Mo.translate(t),setActive(t){const e=this.activeEditor;this.activeEditor!==t&&(e&&e.dispatch("deactivate",{relatedTarget:t}),t.dispatch("activate",{relatedTarget:e})),this.activeEditor=t},_setBaseUrl(t){this.baseURL=new Uo(this.documentBaseURL).toAbsolute(t.replace(/\/+$/,"")),this.baseURI=new Uo(this.baseURL)}};ka.setup();const MO=(()=>{const t=ma();return{FakeClipboardItem:e=>({items:e,types:tn(e),getType:n=>ce(e,n).getOrUndefined()}),write:e=>{t.set(e)},read:()=>t.get().getOrUndefined(),clear:t.clear}})(),RS=Math.min,mi=Math.max,nu=Math.round,AS=(t,e,n)=>{let o=e.x,r=e.y;const a=t.w,s=t.h,i=e.w,l=e.h,c=(n||"").split("");return"b"===c[0]&&(r+=l),"r"===c[1]&&(o+=i),"c"===c[0]&&(r+=nu(l/2)),"c"===c[1]&&(o+=nu(i/2)),"b"===c[3]&&(r-=s),"r"===c[4]&&(o-=a),"c"===c[3]&&(r-=nu(s/2)),"c"===c[4]&&(o-=nu(a/2)),gi(o,r,a,s)},gi=(t,e,n,o)=>({x:t,y:e,w:n,h:o}),LO={inflate:(t,e,n)=>gi(t.x-e,t.y-n,t.w+2*e,t.h+2*n),relativePosition:AS,findBestRelativePosition:(t,e,n,o)=>{for(let r=0;r<o.length;r++){const a=AS(t,e,o[r]);if(a.x>=n.x&&a.x+a.w<=n.w+n.x&&a.y>=n.y&&a.y+a.h<=n.h+n.y)return o[r]}return null},intersect:(t,e)=>{const n=mi(t.x,e.x),o=mi(t.y,e.y),r=RS(t.x+t.w,e.x+e.w),a=RS(t.y+t.h,e.y+e.h);return r-n<0||a-o<0?null:gi(n,o,r-n,a-o)},clamp:(t,e,n)=>{let o=t.x,r=t.y,a=t.x+t.w,s=t.y+t.h;const i=e.x+e.w,l=e.y+e.h,c=mi(0,e.x-o),d=mi(0,e.y-r),u=mi(0,a-i),m=mi(0,s-l);return o+=c,r+=d,n&&(a+=c,s+=d,o-=u,r-=m),a-=u,s-=m,gi(o,r,a-o,s-r)},create:gi,fromClientRect:t=>gi(t.left,t.top,t.width,t.height)},IO=(()=>{const t={},e={};return{load:(n,o)=>{const r=`Script at URL "${o}" failed to load`,a=`Script at URL "${o}" did not call \`tinymce.Resource.add('${n}', data)\` within 1 second`;if(void 0!==t[n])return t[n];{const s=new Promise((i,l)=>{const c=((d,u,m=1e3)=>{let p=!1,g=null;const h=y=>(...C)=>{p||(p=!0,null!==g&&(clearTimeout(g),g=null),y.apply(null,C))},f=h(d),b=h(u);return{start:(...y)=>{p||null!==g||(g=setTimeout(()=>b.apply(null,y),m))},resolve:f,reject:b}})(i,l);e[n]=c.resolve,Ur.ScriptLoader.loadScript(o).then(()=>c.start(a),()=>c.reject(r))});return t[n]=s,s}},add:(n,o)=>{void 0!==e[n]&&(e[n](o),delete e[n]),t[n]=Promise.resolve(o)},unload:n=>{delete t[n]}}})();let Il;try{const t="__storage_test__";Il=window.localStorage,Il.setItem(t,t),Il.removeItem(t)}catch{Il=(()=>{let e={},n=[];const o={getItem:r=>e[r]||null,setItem:(r,a)=>{n.push(r),e[r]=String(a)},key:r=>n[r],removeItem:r=>{n=n.filter(a=>a===r),delete e[r]},clear:()=>{n=[],e={}},length:0};return Object.defineProperty(o,"length",{get:()=>n.length,configurable:!1,enumerable:!1}),o})()}const FO={geom:{Rect:LO},util:{Delay:so,Tools:ot,VK:wt,URI:Uo,EventDispatcher:np,Observable:op,I18n:Mo,LocalStorage:Il,ImageUploader:t=>{const e=pC(),n=bC(t,e);return{upload:(o,r=!0)=>n.upload(o,r?hC(t):void 0)}}},dom:{EventUtils:Ga,TreeWalker:Kt,TextSeeker:Qa,DOMUtils:me,ScriptLoader:Ur,RangeUtils:ss,Serializer:aC,StyleSheetLoader:Rp,ControlSelection:Kb,BookmarkManager:il,Selection:rC,Event:Ga.Event},html:{Styles:Tu,Entities:da,Node:Pn,Schema:ua,DomParser:ti,Writer:Ov,Serializer:ba},Env:Jt,AddOnManager:vn,Annotator:Fb,Formatter:CC,UndoManager:xC,EditorCommands:fS,WindowManager:mC,NotificationManager:uC,EditorObservable:vS,Shortcuts:SS,Editor:Jd,FocusManager:hv,EditorManager:ka,DOM:me.DOM,ScriptLoader:Ur.ScriptLoader,PluginManager:ni,ThemeManager:fs,ModelManager:ei,IconManager:pd,Resource:IO,FakeClipboard:MO,trim:ot.trim,isArray:ot.isArray,is:ot.is,toArray:ot.toArray,makeMap:ot.makeMap,each:ot.each,map:ot.map,grep:ot.grep,inArray:ot.inArray,extend:ot.extend,walk:ot.walk,resolve:ot.resolve,explode:ot.explode,_addCacheSuffix:ot._addCacheSuffix},TS=ot.extend(ka,FO);(t=>{window.tinymce=t,window.tinyMCE=t})(TS),(t=>{if("object"==typeof module)try{module.exports=t}catch{}})(TS)})(),function(){"use strict";var xs=tinymce.util.Tools.resolve("tinymce.PluginManager");const mo=U=>L=>(G=>{const Q=typeof G;return null===G?"null":"object"===Q&&Array.isArray(G)?"array":"object"===Q&&(ct=ht=G,(pt=String).prototype.isPrototypeOf(ct)||(null===(Ft=ht.constructor)||void 0===Ft?void 0:Ft.name)===pt.name)?"string":Q;var ct,ht,pt,Ft})(L)===U,Wr=U=>L=>typeof L===U,Wn=mo("string"),hr=mo("object"),br=mo("array"),vr=Wr("boolean"),sn=U=>!(null==U),Kr=Wr("function"),Ra=(U,L)=>{if(br(U)){for(let G=0,Q=U.length;G<Q;++G)if(!L(U[G]))return!1;return!0}return!1},Ce=()=>{},Nt=(U,L)=>U===L;class At{constructor(L,G){this.tag=L,this.value=G}static some(L){return new At(!0,L)}static none(){return At.singletonNone}fold(L,G){return this.tag?G(this.value):L()}isSome(){return this.tag}isNone(){return!this.tag}map(L){return this.tag?At.some(L(this.value)):At.none()}bind(L){return this.tag?L(this.value):At.none()}exists(L){return this.tag&&L(this.value)}forall(L){return!this.tag||L(this.value)}filter(L){return!this.tag||L(this.value)?this:At.none()}getOr(L){return this.tag?this.value:L}or(L){return this.tag?this:L}getOrThunk(L){return this.tag?this.value:L()}orThunk(L){return this.tag?this:L()}getOrDie(L){if(this.tag)return this.value;throw new Error(L??"Called getOrDie on None")}static from(L){return sn(L)?At.some(L):At.none()}getOrNull(){return this.tag?this.value:null}getOrUndefined(){return this.value}each(L){this.tag&&L(this.value)}toArray(){return this.tag?[this.value]:[]}toString(){return this.tag?`some(${this.value})`:"none()"}}At.singletonNone=new At(!1);const ln=Array.prototype.indexOf,Ee=Array.prototype.push,Oe=U=>{const L=[];for(let G=0,Q=U.length;G<Q;++G){if(!br(U[G]))throw new Error("Arr.flatten item "+G+" was not an array, input: "+U);Ee.apply(L,U[G])}return L},xn=(U,L)=>{for(let G=0;G<U.length;G++){const Q=L(U[G],G);if(Q.isSome())return Q}return At.none()},dt=(U,L,G=Nt)=>U.exists(Q=>G(Q,L)),pe=U=>{const L=[],G=Q=>{L.push(Q)};for(let Q=0;Q<U.length;Q++)U[Q].each(G);return L},st=(U,L)=>U?At.some(L):At.none(),Yt=U=>L=>L.options.get(U),nn=Yt("link_assume_external_targets"),Sn=Yt("link_context_toolbar"),jt=Yt("link_list"),Fe=Yt("link_default_target"),Aa=Yt("link_default_protocol"),ut=Yt("link_target_list"),qe=Yt("link_rel_list"),kn=Yt("link_class_list"),Ct=Yt("link_title"),$o=Yt("allow_unsafe_link_target"),Ue=Yt("link_quicklink");var je=tinymce.util.Tools.resolve("tinymce.util.Tools");const En=U=>Wn(U.value)?U.value:"",de=(U,L)=>{const G=[];return je.each(U,Q=>{const ct=Wn((ht=Q).text)?ht.text:Wn(ht.title)?ht.title:"";var ht;if(void 0!==Q.menu){const ht=de(Q.menu,L);G.push({text:ct,items:ht})}else{const ht=L(Q);G.push({text:ct,value:ht})}}),G},ye=(U=En)=>L=>At.from(L).map(G=>de(G,U)),S=U=>ye(En)(U),Ro=ye,qo=(U,L)=>G=>({name:U,type:"listbox",label:L,items:G}),Ss=En,Vo=Object.keys,Lt=Object.hasOwnProperty,ie=(U,L)=>Lt.call(U,L);var te=tinymce.util.Tools.resolve("tinymce.dom.TreeWalker"),J=tinymce.util.Tools.resolve("tinymce.util.URI");const Ta=U=>sn(U)&&"a"===U.nodeName.toLowerCase(),Ve=U=>Ta(U)&&!!Re(U),Ot=(U,L)=>{if(U.collapsed)return[];{const G=U.cloneContents(),Q=G.firstChild,ct=new te(Q,G),ht=[];let pt=Q;do{L(pt)&&ht.push(pt)}while(pt=ct.next());return ht}},Kn=U=>/^\w+:/i.test(U),Re=U=>{var L,G;return null!==(G=null!==(L=U.getAttribute("data-mce-href"))&&void 0!==L?L:U.getAttribute("href"))&&void 0!==G?G:""},yr=(U,L)=>{const G=["noopener"],Q=U?U.split(/\s+/):[],ct=pt=>pt.filter(Ft=>-1===je.inArray(G,Ft)),ht=L?(pt=ct(pt=Q)).length>0?pt.concat(G):G:ct(Q);var pt;return ht.length>0?(pt=>je.trim(pt.sort().join(" ")))(ht):""},he=(U,L)=>(L=L||gn(U.selection.getRng())[0]||U.selection.getNode(),wr(L)?At.from(U.dom.select("a[href]",L)[0]):At.from(U.dom.getParent(L,"a[href]"))),go=(U,L)=>he(U,L).isSome(),Cr=(U,L)=>L.fold(()=>U.getContent({format:"text"}),G=>G.innerText||G.textContent||"").replace(/\uFEFF/g,""),gn=U=>Ot(U,Ve),_n=U=>je.grep(U,Ve),Ao=U=>_n(U).length>0,Gn=U=>{const L=U.schema.getTextInlineElements();if(he(U).exists(Q=>Q.hasAttribute("data-mce-block")))return!1;const G=U.selection.getRng();return!!G.collapsed||0===Ot(G,Q=>1===Q.nodeType&&!Ta(Q)&&!ie(L,Q.nodeName.toLowerCase())).length},wr=U=>sn(U)&&"FIGURE"===U.nodeName&&/\bimage\b/i.test(U.className),We=(U,L,G)=>{const Q=((ct,ht)=>{const pt=ct.options.get,Ft={allow_html_data_urls:pt("allow_html_data_urls"),allow_script_urls:pt("allow_script_urls"),allow_svg_data_urls:pt("allow_svg_data_urls")},Wt=ht.href;return{...ht,href:J.isDomSafe(Wt,"a",Ft)?Wt:""}})(U,G);U.hasPlugin("rtc",!0)?U.execCommand("createlink",!1,(U=>{const{class:L,href:G,rel:Q,target:ct,text:ht,title:pt}=U;return(Ft=>{const ne={};var oe,we,Ke;return oe=ne,we=(Qt,Xt)=>{oe[Xt]=Qt},Ke=Ce,((ue,Qo)=>{const Rr=Vo(ue);for(let Xn=0,Jo=Rr.length;Xn<Jo;Xn++){const Ar=Rr[Xn];Qo(ue[Ar],Ar)}})(Ft,(ue,Qo)=>{(!1===(U=>null===U)(ue)?we:Ke)(ue,Qo)}),ne})({class:L.getOrNull(),href:G,rel:Q.getOrNull(),target:ct.getOrNull(),text:ht.getOrNull(),title:pt.getOrNull()})})(Q)):((U,L,G)=>{const Q=U.selection.getNode(),ct=he(U,Q),ht=((pt,Ft)=>{const Wt={...Ft};if(0===qe(pt).length&&!$o(pt)){const ne=yr(Wt.rel,"_blank"===Wt.target);Wt.rel=ne||null}return At.from(Wt.target).isNone()&&!1===ut(pt)&&(Wt.target=Fe(pt)),Wt.href=(ne=Wt.href,"http"!==(oe=nn(pt))&&"https"!==oe||Kn(ne)?ne:oe+"://"+ne),Wt;var ne,oe})(U,(Wt=(oe,Qt)=>(pt[Qt].each(Xt=>{oe[Qt]=Xt.length>0?Xt:null}),oe),ne={href:(pt=G).href},((oe,Qt)=>{for(let Xt=0,we=oe.length;Xt<we;Xt++)Qt(oe[Xt])})(["title","rel","class","target"],(oe,Qt)=>{ne=Wt(ne,oe)}),ne));var pt,Wt,ne;U.undoManager.transact(()=>{G.href===L.href&&L.attach(),ct.fold(()=>{((pt,Ft,Wt,ne)=>{const oe=pt.dom;wr(Ft)?Ko(oe,Ft,ne):Wt.fold(()=>{pt.execCommand("mceInsertLink",!1,ne)},Qt=>{pt.insertContent(oe.createHTML("a",ne,oe.encode(Qt)))})})(U,Q,G.text,ht)},pt=>{U.focus(),((Ft,Wt,ne,oe)=>{ne.each(Qt=>{ie(Wt,"innerText")?Wt.innerText=Qt:Wt.textContent=Qt}),Ft.dom.setAttribs(Wt,oe),Ft.selection.select(Wt)})(U,pt,G.text,ht)})})})(U,L,Q)},Nn=U=>{var L;U.hasPlugin("rtc",!0)?U.execCommand("unlink"):(L=U).undoManager.transact(()=>{const G=L.selection.getNode();wr(G)?Ae(L,G):(Q=>{const ct=Q.dom,ht=Q.selection,pt=ht.getBookmark(),Ft=ht.getRng().cloneRange(),Wt=ct.getParent(Ft.startContainer,"a[href]",Q.getBody()),ne=ct.getParent(Ft.endContainer,"a[href]",Q.getBody());Wt&&Ft.setStartBefore(Wt),ne&&Ft.setEndAfter(ne),ht.setRng(Ft),Q.execCommand("unlink"),ht.moveToBookmark(pt)})(L),L.focus()})},Ae=(U,L)=>{var G;const Q=U.dom.select("img",L)[0];if(Q){const ct=U.dom.getParents(Q,"a[href]",L)[0];ct&&(null===(G=ct.parentNode)||void 0===G||G.insertBefore(Q,ct),U.dom.remove(ct))}},Ko=(U,L,G)=>{var Q;const ct=U.select("img",L)[0];if(ct){const ht=U.create("a",G);null===(Q=ct.parentNode)||void 0===Q||Q.insertBefore(ht,ct),ht.appendChild(ct)}},tn=(U,L)=>xn(L,G=>{return ie(ct=G,"items")&&null!=ct.items?tn(U,G.items):st(G.value===U,G);var ct});var le=tinymce.util.Tools.resolve("tinymce.util.Delay");const Yr=U=>{const L=U.href;return L.indexOf("@")>0&&-1===L.indexOf("/")&&-1===L.indexOf("mailto:")?At.some({message:"The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?",preprocess:G=>({...G,href:"mailto:"+L})}):At.none()},xr=(U,L)=>G=>{const Q=G.href;return 1===U&&!Kn(Q)||0===U&&/^\s*www(\.|\d\.)/i.test(Q)?At.some({message:`The URL you entered seems to be an external link. Do you want to add the required ${L}:// prefix?`,preprocess:ct=>({...ct,href:L+"://"+Q})}):At.none()},Rn=U=>{const L=U.dom.select("a:not([href])"),G=Oe(((Q,ct)=>{const ht=Q.length,pt=new Array(ht);for(let Ft=0;Ft<ht;Ft++)pt[Ft]=ct(Q[Ft]);return pt})(L,Q=>{const ct=Q.name||Q.id;return ct?[{text:ct,value:"#"+ct}]:[]}));return G.length>0?At.some([{text:"None",value:""}].concat(G)):At.none()},Sr=U=>{const L=kn(U);return L.length>0?S(L):At.none()},Go=U=>{try{return At.some(JSON.parse(U))}catch{return At.none()}},kr=(U,L)=>{const G=qe(U);if(G.length>0){const Q=dt(L,"_blank"),ct=ht=>yr(Ss(ht),Q);return(!1===$o(U)?Ro(ct):S)(G)}return At.none()},Er=[{text:"Current window",value:""},{text:"New window",value:"_blank"}],ce=U=>{const L=ut(U);return br(L)?S(L).orThunk(()=>At.some(Er)):!1===L?At.none():At.some(Er)},It=(U,L,G)=>{const Q=U.getAttrib(L,G);return null!==Q&&Q.length>0?At.some(Q):At.none()},Oa=U=>{var G;(G=U,((U,L)=>(G=>{const Q=ht=>G.convertURL(ht.value||ht.url||"","href"),ct=jt(G);return new Promise(ht=>{Wn(ct)?fetch(ct).then(pt=>pt.ok?pt.text().then(Go):Promise.reject()).then(ht,()=>ht(At.none())):Kr(ct)?ct(pt=>ht(At.some(pt))):ht(At.from(ct))}).then(ht=>ht.bind(Ro(Q)).map(pt=>pt.length>0?[{text:"None",value:""}].concat(pt):pt))})(U).then(G=>{const Q=((ct,ht)=>{const pt=ct.dom,Ft=Gn(ct)?At.some(Cr(ct.selection,ht)):At.none(),Wt=ht.bind(Xt=>At.from(pt.getAttrib(Xt,"href"))),ne=ht.bind(Xt=>At.from(pt.getAttrib(Xt,"target"))),oe=ht.bind(Xt=>It(pt,Xt,"rel")),Qt=ht.bind(Xt=>It(pt,Xt,"class"));return{url:Wt,text:Ft,title:ht.bind(Xt=>It(pt,Xt,"title")),target:ne,rel:oe,linkClass:Qt}})(U,L);return{anchor:Q,catalogs:{targets:ce(U),rels:kr(U,Q.target),classes:Sr(U),anchor:Rn(U),link:G},optNode:L,flags:{titleEnabled:Ct(U)}}}))(G,he(G))).then(G=>((ct,ht,pt)=>{const Ft=ct.anchor.text.map(()=>({name:"text",type:"input",label:"Text to display"})).toArray(),Wt=ct.flags.titleEnabled?[{name:"title",type:"input",label:"Title"}]:[],ne=((Xt,we)=>{const Ke=Xt.anchor,ue=Ke.url.getOr("");return{url:{value:ue,meta:{original:{value:ue}}},text:Ke.text.getOr(""),title:Ke.title.getOr(""),anchor:ue,link:ue,rel:Ke.rel.getOr(""),target:Ke.target.or(we).getOr(""),linkClass:Ke.linkClass.getOr("")}})(ct,At.from(Fe(pt))),oe=ct.catalogs,Qt=((U,L)=>{const G={text:U.text,title:U.title};return{onChange:(ct,ht)=>{const pt=ht.name;return"url"===pt?(Ft=>{const Wt=st(G.text.length<=0,At.from(null===(oe=(ne=Ft.url).meta)||void 0===oe?void 0:oe.text).getOr(ne.value));var ne,oe;const Qt=st(G.title.length<=0,At.from(null===(we=Ft.url.meta)||void 0===we?void 0:we.title).getOr(""));var we;return Wt.isSome()||Qt.isSome()?At.some({...Wt.map(Xt=>({text:Xt})).getOr({}),...Qt.map(Xt=>({title:Xt})).getOr({})}):At.none()})(ct()):ln.call(["anchor","link"],pt)>-1?((ct,ht)=>{const pt=(Ft=L,Wt=ht,"link"===Wt?Ft.link:"anchor"===Wt?Ft.anchor:At.none()).getOr([]);var Ft,Wt;return((ne,oe,Qt,Xt)=>{const we=Xt[oe],Ke=ne.length>0;return void 0!==we?tn(we,Qt).map(ue=>({url:{value:ue.value,meta:{text:Ke?ne:ue.text,attach:Ce}},text:Ke?ne:ue.text})):At.none()})(G.text,ht,pt,ct)})(ct(),pt):(("text"===pt||"title"===pt)&&(G[pt]=ct()[pt]),At.none())}}})(ne,oe);return{title:"Insert/Edit Link",size:"normal",body:{type:"panel",items:Oe([[{name:"url",type:"urlinput",filetype:"file",label:"URL"}],Ft,Wt,pe([oe.anchor.map(qo("anchor","Anchors")),oe.rels.map(qo("rel","Rel")),oe.targets.map(qo("target","Open link in...")),oe.link.map(qo("link","Link list")),oe.classes.map(qo("linkClass","Class"))])])},buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:ne,onChange:(Xt,{name:we})=>{Qt.onChange(Xt.getData,{name:we}).each(Ke=>{Xt.setData(Ke)})},onSubmit:ht}})(G,((ct,ht)=>pt=>{const Ft=pt.getData();if(!Ft.url.value)return Nn(ct),void pt.close();const Wt=Qt=>At.from(Ft[Qt]).filter(Xt=>!dt(ht.anchor[Qt],Xt)),ne={href:Ft.url.value,text:Wt("text"),target:Wt("target"),rel:Wt("rel"),class:Wt("linkClass"),title:Wt("title")},oe={href:Ft.url.value,attach:void 0!==Ft.url.meta&&Ft.url.meta.attach?Ft.url.meta.attach:Ce};var Qt,Xt;(Qt=ct,Xt=ne,xn([Yr,xr(nn(Qt),Aa(Qt))],we=>we(Xt)).fold(()=>Promise.resolve(Xt),we=>new Promise(Ke=>{((ue,Qo)=>{const Xn=ue.selection.getRng();le.setEditorTimeout(ue,()=>{ue.windowManager.confirm(Qo,Jo=>{ue.selection.setRng(Xn),(ue=>{Ke(ue?we.preprocess(Xt):Xt)})(Jo)})})})(Qt,we.message)}))).then(Qt=>{We(ct,oe,Qt)}),pt.close()})(U,G),U)).then(G=>{U.windowManager.open(G)})};var Ba=tinymce.util.Tools.resolve("tinymce.util.VK");const _r=(U,L)=>U.dom.getParent(L,"a[href]"),Yo=U=>_r(U,U.selection.getStart()),Nr=(U,L)=>{if(L){const G=Re(L);if(/^#/.test(G)){const Q=U.dom.select(G);Q.length&&U.selection.scrollIntoView(Q[0],!0)}else(Q=>{const ct=document.createElement("a");ct.target="_blank",ct.href=Q,ct.rel="noreferrer noopener";const ht=document.createEvent("MouseEvents");var pt,Ft;ht.initMouseEvent("click",!0,!0,window,0,0,0,0,0,!1,!1,!1,!1,0,null),pt=ct,Ft=ht,document.body.appendChild(pt),pt.dispatchEvent(Ft),document.body.removeChild(pt)})(L.href)}},Oo=U=>()=>{U.execCommand("mceLink",!1,{dialog:!0})},Bo=U=>()=>{Nr(U,Yo(U))},Xo=(U,L)=>(U.on("NodeChange",L),()=>U.off("NodeChange",L)),Fn=U=>L=>{const G=()=>{L.setActive(!U.mode.isReadOnly()&&go(U,U.selection.getNode())),L.setEnabled(U.selection.isEditable())};return G(),Xo(U,G)},fo=U=>L=>{const G=()=>{L.setEnabled(U.selection.isEditable())};return G(),Xo(U,G)},Xr=U=>L=>{const G=()=>{return L.setEnabled(1===((Q=U).selection.isCollapsed()?_n(Q.dom.getParents(Q.selection.getStart())):gn(Q.selection.getRng())).length);var Q};return G(),Xo(U,G)},Yn=U=>L=>{const G=U.dom.getParents(U.selection.getStart()),Q=ct=>{var pt;L.setEnabled((Ao(ct)||(pt=U.selection.getRng(),gn(pt).length>0))&&U.selection.isEditable())};return Q(G),Xo(U,ct=>Q(ct.parents))};xs.add("link",U=>{var L;(L=>{const G=L.options.register;G("link_assume_external_targets",{processor:Q=>{const ct=Wn(Q)||vr(Q);return ct?!0===Q?{value:1,valid:ct}:"http"===Q||"https"===Q?{value:Q,valid:ct}:{value:0,valid:ct}:{valid:!1,message:"Must be a string or a boolean."}},default:!1}),G("link_context_toolbar",{processor:"boolean",default:!1}),G("link_list",{processor:Q=>Wn(Q)||Kr(Q)||Ra(Q,hr)}),G("link_default_target",{processor:"string"}),G("link_default_protocol",{processor:"string",default:"https"}),G("link_target_list",{processor:Q=>vr(Q)||Ra(Q,hr),default:!0}),G("link_rel_list",{processor:"object[]",default:[]}),G("link_class_list",{processor:"object[]",default:[]}),G("link_title",{processor:"boolean",default:!0}),G("allow_unsafe_link_target",{processor:"boolean",default:!1}),G("link_quicklink",{processor:"boolean",default:!1})})(U),(L=U).ui.registry.addToggleButton("link",{icon:"link",tooltip:"Insert/edit link",onAction:Oo(L),onSetup:Fn(L)}),L.ui.registry.addButton("openlink",{icon:"new-tab",tooltip:"Open link",onAction:Bo(L),onSetup:Xr(L)}),L.ui.registry.addButton("unlink",{icon:"unlink",tooltip:"Remove link",onAction:()=>Nn(L),onSetup:Yn(L)}),(L=>{L.ui.registry.addMenuItem("openlink",{text:"Open link",icon:"new-tab",onAction:Bo(L),onSetup:Xr(L)}),L.ui.registry.addMenuItem("link",{icon:"link",text:"Link...",shortcut:"Meta+K",onSetup:fo(L),onAction:Oo(L)}),L.ui.registry.addMenuItem("unlink",{icon:"unlink",text:"Remove link",onAction:()=>Nn(L),onSetup:Yn(L)})})(U),(L=>{L.ui.registry.addContextMenu("link",{update:G=>L.dom.isEditable(G)?Ao(L.dom.getParents(G,"a"))?"link unlink openlink":"link":""})})(U),(L=>{const G=Q=>{const ct=L.selection.getNode();return Q.setEnabled(go(L,ct)),Ce};L.ui.registry.addContextForm("quicklink",{launch:{type:"contextformtogglebutton",icon:"link",tooltip:"Link",onSetup:Fn(L)},label:"Link",predicate:Q=>Sn(L)&&go(L,Q),initValue:()=>he(L).fold(()=>"",Re),commands:[{type:"contextformtogglebutton",icon:"link",tooltip:"Link",primary:!0,onSetup:Q=>{const ct=L.selection.getNode();return Q.setActive(go(L,ct)),Fn(L)(Q)},onAction:Q=>{const ct=Q.getValue(),ht=(pt=>{const Ft=he(L),Wt=Gn(L);if(Ft.isNone()&&Wt){const ne=Cr(L.selection,Ft);return st(0===ne.length,pt)}return At.none()})(ct);We(L,{href:ct,attach:Ce},{href:ct,text:ht,title:At.none(),rel:At.none(),target:At.none(),class:At.none()}),L.selection.collapse(!1),Q.hide()}},{type:"contextformbutton",icon:"unlink",tooltip:"Remove link",onSetup:G,onAction:Q=>{Nn(L),Q.hide()}},{type:"contextformbutton",icon:"new-tab",tooltip:"Open link",onSetup:G,onAction:Q=>{Bo(L)(),Q.hide()}}]})})(U),(L=>{L.on("click",G=>{const Q=_r(L,G.target);Q&&Ba.metaKeyPressed(G)&&(G.preventDefault(),Nr(L,Q))}),L.on("keydown",G=>{if(!G.isDefaultPrevented()&&13===G.keyCode&&!0===(Q=G).altKey&&!1===Q.shiftKey&&!1===Q.ctrlKey&&!1===Q.metaKey){const Q=Yo(L);Q&&(G.preventDefault(),Nr(L,Q))}var Q})})(U),(L=>{L.addCommand("mceLink",(G,Q)=>{!0!==Q?.dialog&&Ue(L)?L.dispatch("contexttoolbar-show",{toolbarKey:"quicklink"}):Oa(L)})})(U),(L=>{L.addShortcut("Meta+K","",()=>{L.execCommand("mceLink")})})(U)})}(),function(){"use strict";var xs=tinymce.util.Tools.resolve("tinymce.PluginManager");const mo=v=>x=>(w=>{const _=typeof w;return null===w?"null":"object"===_&&Array.isArray(w)?"array":"object"===_&&(M=F=w,(X=String).prototype.isPrototypeOf(M)||(null===(rt=F.constructor)||void 0===rt?void 0:rt.name)===X.name)?"string":_;var M,F,X,rt})(x)===v,Wr=v=>x=>typeof x===v,Wn=mo("string"),hr=mo("array"),br=Wr("boolean"),Na=v=>void 0===v,vr=v=>!(null==v),sn=Wr("function"),Kr=Wr("number"),Ra=()=>{},Ce=v=>()=>v,Nt=v=>v,At=(v,x)=>v===x;function ln(v,...x){return(...w)=>{const _=x.concat(w);return v.apply(null,_)}}const Ee=v=>{v()},Oe=Ce(!1),xn=Ce(!0);class dt{constructor(x,w){this.tag=x,this.value=w}static some(x){return new dt(!0,x)}static none(){return dt.singletonNone}fold(x,w){return this.tag?w(this.value):x()}isSome(){return this.tag}isNone(){return!this.tag}map(x){return this.tag?dt.some(x(this.value)):dt.none()}bind(x){return this.tag?x(this.value):dt.none()}exists(x){return this.tag&&x(this.value)}forall(x){return!this.tag||x(this.value)}filter(x){return!this.tag||x(this.value)?this:dt.none()}getOr(x){return this.tag?this.value:x}or(x){return this.tag?this:x}getOrThunk(x){return this.tag?this.value:x()}orThunk(x){return this.tag?this:x()}getOrDie(x){if(this.tag)return this.value;throw new Error(x??"Called getOrDie on None")}static from(x){return vr(x)?dt.some(x):dt.none()}getOrNull(){return this.tag?this.value:null}getOrUndefined(){return this.value}each(x){this.tag&&x(this.value)}toArray(){return this.tag?[this.value]:[]}toString(){return this.tag?`some(${this.value})`:"none()"}}dt.singletonNone=new dt(!1);const pe=Object.keys,st=Object.hasOwnProperty,Yt=(v,x)=>{const w=pe(v);for(let _=0,M=w.length;_<M;_++){const F=w[_];x(v[F],F)}},nn=(v,x)=>{const w={};var _,F,X,rt;return F=x,_=w,X=(M,F)=>{_[F]=M},rt=Ra,Yt(v,(et,nt)=>{(F(et,nt)?X:rt)(et,nt)}),w},Sn=v=>pe(v).length,jt=(v,x)=>Fe(v,x)?dt.from(v[x]):dt.none(),Fe=(v,x)=>st.call(v,x),Aa=(v,x)=>Fe(v,x)&&null!=v[x],ut=Array.prototype.indexOf,qe=Array.prototype.push,kn=(v,x)=>ut.call(v,x)>-1,Ct=(v,x)=>{for(let w=0,_=v.length;w<_;w++)if(x(v[w],w))return!0;return!1},$o=(v,x)=>{const w=[];for(let _=0;_<v;_++)w.push(x(_));return w},Ue=(v,x)=>{const w=v.length,_=new Array(w);for(let M=0;M<w;M++)_[M]=x(v[M],M);return _},je=(v,x)=>{for(let w=0,_=v.length;w<_;w++)x(v[w],w)},En=(v,x)=>{const w=[];for(let _=0,M=v.length;_<M;_++){const F=v[_];x(F,_)&&w.push(F)}return w},de=(v,x,w)=>(je(v,(_,M)=>{w=x(w,_,M)}),w),ye=(v,x)=>((w,_,M)=>{for(let F=0,X=w.length;F<X;F++){const rt=w[F];if(_(rt,F))return dt.some(rt);if(M(rt,F))break}return dt.none()})(v,x,Oe),S=(v,x)=>(w=>{const _=[];for(let M=0,F=w.length;M<F;++M){if(!hr(w[M]))throw new Error("Arr.flatten item "+M+" was not an array, input: "+w);qe.apply(_,w[M])}return _})(Ue(v,x)),Ro=(v,x)=>{for(let w=0,_=v.length;w<_;++w)if(!0!==x(v[w],w))return!1;return!0},qo=(v,x)=>x>=0&&x<v.length?dt.some(v[x]):dt.none(),Ss=(v,x)=>{for(let w=0;w<v.length;w++){const _=x(v[w],w);if(_.isSome())return _}return dt.none()},Vo=v=>{if(null==v)throw new Error("Node cannot be null or undefined");return{dom:v}},Lt={fromHtml:(v,x)=>{const w=(x||document).createElement("div");if(w.innerHTML=v,!w.hasChildNodes()||w.childNodes.length>1){const _="HTML does not have a single root node";throw console.error(_,v),new Error(_)}return Vo(w.childNodes[0])},fromTag:(v,x)=>{const w=(x||document).createElement(v);return Vo(w)},fromText:(v,x)=>{const w=(x||document).createTextNode(v);return Vo(w)},fromDom:Vo,fromPoint:(v,x,w)=>dt.from(v.dom.elementFromPoint(x,w)).map(Vo)},ie=(v,x)=>{const w=v.dom;if(1!==w.nodeType)return!1;{const _=w;if(void 0!==_.matches)return _.matches(x);if(void 0!==_.msMatchesSelector)return _.msMatchesSelector(x);if(void 0!==_.webkitMatchesSelector)return _.webkitMatchesSelector(x);if(void 0!==_.mozMatchesSelector)return _.mozMatchesSelector(x);throw new Error("Browser lacks native selectors")}},te=v=>1!==v.nodeType&&9!==v.nodeType&&11!==v.nodeType||0===v.childElementCount,J=(v,x)=>v.dom===x.dom,Ta=ie;typeof window<"u"||Function("return this;")();const Ve=v=>v.dom.nodeName.toLowerCase(),Ot=v=>v.dom.nodeType,Kn=v=>x=>Ot(x)===v,Re=Kn(1),yr=Kn(3),he=Kn(9),go=Kn(11),Cr=v=>x=>Re(x)&&Ve(x)===v,gn=v=>he(v)?v:Lt.fromDom(v.dom.ownerDocument),_n=v=>dt.from(v.dom.parentNode).map(Lt.fromDom),Ao=v=>dt.from(v.dom.nextSibling).map(Lt.fromDom),Gn=v=>Ue(v.dom.childNodes,Lt.fromDom),wr=sn(Element.prototype.attachShadow)&&sn(Node.prototype.getRootNode)?v=>Lt.fromDom(v.dom.getRootNode()):gn,To=v=>Lt.fromDom(v.dom.host),Wo=v=>{const x=yr(v)?v.dom.parentNode:v.dom;if(null==x||null===x.ownerDocument)return!1;const w=x.ownerDocument;return(F=>{const X=wr(F);return go(rt=X)&&vr(rt.dom.host)?dt.some(X):dt.none();var rt})(Lt.fromDom(x)).fold(()=>w.body.contains(x),(_=Wo,M=To,F=>_(M(F))));var _,M};var We=(v,x,w,_,M)=>v(w,_)?dt.some(w):sn(M)&&M(w)?dt.none():x(w,_,M);const Nn=(v,x,w)=>{let _=v.dom;const M=sn(w)?w:Oe;for(;_.parentNode;){_=_.parentNode;const F=Lt.fromDom(_);if(x(F))return dt.some(F);if(M(F))break}return dt.none()},Ae=(v,x,w)=>Nn(v,_=>ie(_,x),w),tn=(v,x)=>((w,_)=>{const M=void 0===_?document:_.dom;return te(M)?dt.none():dt.from(M.querySelector(w)).map(Lt.fromDom)})(x,v),Gr=(v,x,w)=>We((_,M)=>ie(_,M),Ae,v,x,w),le=(v,x=!1)=>{return Wo(v)?v.dom.isContentEditable:(w=v,Gr(w,"[contenteditable]")).fold(Ce(x),_=>"true"===Yr(_));var w},Yr=v=>v.dom.contentEditable,xr=v=>x=>J(x,Lt.fromDom(v.getBody())),Rn=v=>/^\d+(\.\d+)?$/.test(v)?v+"px":v,Sr=v=>Lt.fromDom(v.selection.getStart()),Go=(v,x)=>{let w=[];return je(Gn(v),_=>{x(_)&&(w=w.concat([_])),w=w.concat(Go(_,x))}),w},kr=(v,x)=>En(Gn(v),M=>ie(M,x)),Er=(v,x)=>((w,_)=>{const M=void 0===_?document:_.dom;return te(M)?[]:Ue(M.querySelectorAll(w),Lt.fromDom)})(x,v),ce=(v,x,w)=>{if(!(Wn(w)||br(w)||Kr(w)))throw console.error("Invalid call to Attribute.set. Key ",x,":: Value ",w,":: Element ",v),new Error("Attribute value was not simple");v.setAttribute(x,w+"")},It=(v,x)=>{const w=v.dom.getAttribute(x);return null===w?void 0:w},Hn=(v,x)=>dt.from(It(v,x)),Oa=(v,x)=>{v.dom.removeAttribute(x)},Ba=(v,x,w=At)=>v.exists(_=>w(_,x)),_r=(v,x,w)=>v.isSome()&&x.isSome()?dt.some(w(v.getOrDie(),x.getOrDie())):dt.none(),Yo=(v,x)=>{return w=v,""===(_=x)||w.length>=_.length&&w.substr(0,0+_.length)===_;var w,_},Nr=(Oo=/^\s+|\s+$/g,v=>v.replace(Oo,""));var Oo;const Bo=v=>v.length>0,Xo=(v,x=10)=>{const w=parseInt(v,x);return isNaN(w)?dt.none():dt.some(w)},Fn=v=>void 0!==v.style&&sn(v.style.getPropertyValue),fo=(v,x)=>{const w=v.dom,_=window.getComputedStyle(w).getPropertyValue(x);return""!==_||Wo(v)?_:Xr(w,x)},Xr=(v,x)=>Fn(v)?v.style.getPropertyValue(x):"",Yn=(v,x)=>{const _=Xr(v.dom,x);return dt.from(_).filter(M=>M.length>0)},U=(v,x,w=0)=>Hn(v,x).map(_=>parseInt(_,10)).getOr(w),L=(v,x)=>G(v,x,xn),G=(v,x,w)=>S(Gn(v),_=>ie(_,x)?w(_)?[_]:[]:G(_,x,w)),Q=["tfoot","thead","tbody","colgroup"],ct=(v,x,w)=>({element:v,rowspan:x,colspan:w}),ht=(v,x,w)=>({element:v,cells:x,section:w}),pt=(v,x)=>Gr(v,"table",x),Ft=v=>L(v,"tr"),Wt=v=>pt(v).fold(Ce([]),x=>kr(x,"colgroup")),ne=v=>_n(v).map(x=>{const w=Ve(x);return kn(Q,w)?w:"tbody"}).getOr("tbody"),oe=v=>Hn(v,"data-snooker-locked-cols").bind(x=>dt.from(x.match(/\d+/g))).map(x=>((w,_)=>{const M={};for(let F=0,X=w.length;F<X;F++){const rt=w[F];M[String(rt)]=_(rt,F)}return M})(x,xn)),Qt=(v,x)=>v+","+x,we=v=>(v=>{const x={},w=[];var _;const M=(_=v,qo(_,0)).map(Pt=>Pt.element).bind(pt).bind(oe).getOr({});let F=0,X=0,rt=0;const{pass:et,fail:nt}=(Pt=>{const ee=[],Et=[];for(let Rt=0,Zt=Pt.length;Rt<Zt;Rt++){const Kt=Pt[Rt];($t=Kt,"colgroup"===$t.section?ee:Et).push(Kt)}var $t;return{pass:ee,fail:Et}})(v);je(nt,Pt=>{const Gt=[];je(Pt.cells,ee=>{let Et=0;for(;void 0!==x[Qt(rt,Et)];)Et++;const $t=Aa(M,Et.toString()),Rt=((Zt,Kt,be,He,it,rn)=>({element:Zt,rowspan:Kt,colspan:be,row:He,column:it,isLocked:rn}))(ee.element,ee.rowspan,ee.colspan,rt,Et,$t);for(let Zt=0;Zt<ee.colspan;Zt++)for(let Kt=0;Kt<ee.rowspan;Kt++){const be=Et+Zt,He=Qt(rt+Kt,be);x[He]=Rt,X=Math.max(X,be+1)}Gt.push(Rt)}),F++,w.push(ht(Pt.element,Gt,Pt.section)),rt++});const{columns:bt,colgroups:xt}=(Pt=et,qo(Pt,Pt.length-1)).map(Pt=>{const Gt=(Et=>{const $t={};let Rt=0;return je(Et.cells,Zt=>{const Kt=Zt.colspan;$o(Kt,be=>{const He=Rt+be;$t[He]=((it,rn,ke)=>({element:it,colspan:rn,column:ke}))(Zt.element,Kt,He)}),Rt+=Kt}),$t})(Pt);return{colgroups:[((Et,$t)=>({element:Et,columns:$t}))(Pt.element,((Et,$t)=>{const Rt=[];return Yt(Et,(Zt,Kt)=>{Rt.push($t(Zt,Kt))}),Rt})(Gt,Nt))],columns:Gt}}).getOrThunk(()=>({colgroups:[],columns:{}}));var Pt;return{grid:((Pt,Gt)=>({rows:Pt,columns:Gt}))(F,X),access:x,all:w,columns:bt,colgroups:xt}})((w=>{const _=Ft(w);return M=[...Wt(w),..._],F=ne,Ue(M,X=>{if("colgroup"===Ve(X)){const rt=Ue(ie(et=X,"colgroup")?kr(et,"col"):S(Wt(et),nt=>kr(nt,"col")),et=>{const nt=U(et,"span",1);return ct(et,1,nt)});return ht(X,rt,"colgroup")}var et;{const rt=Ue((et=>L(et,"th,td"))(X),et=>{const nt=U(et,"rowspan",1),bt=U(et,"colspan",1);return ct(et,nt,bt)});return ht(X,rt,F(X))}});var M,F})(v)),Ke=(v,x,w)=>dt.from(v.access[Qt(x,w)]),ue=(v,x,w)=>{const _=(M=>{const X=S(M.all,rt=>rt.cells);return En(X,M=>w(x,M.element))})(v);return _.length>0?dt.some(_[0]):dt.none()},Qo=(v,x)=>dt.from(v.columns[x]);var Rr=tinymce.util.Tools.resolve("tinymce.util.Tools");const Xn=(v,x,w)=>{const _=v.select("td,th",x);let M;for(let F=0;F<_.length;F++){const X=v.getStyle(_[F],w);if(Na(M)&&(M=X),M!==X)return""}return M},Jo=(v,x,w)=>{Rr.each("left center right".split(" "),_=>{_!==w&&v.formatter.remove("align"+_,{},x)}),w&&v.formatter.apply("align"+w,{},x)},Ar=(v,x,w)=>{v.dispatch("TableModified",{...w,table:x})},Da=(v,x,w)=>{return M=w,(F=>{const X=parseFloat(F);return isNaN(X)?dt.none():dt.some(X)})(fo(v,x)).getOr(M);var M};var zl=tinymce.util.Tools.resolve("tinymce.Env");const Hl=$o(5,v=>{const x=`${v+1}px`;return{title:x,value:x}}),su=Ue(["Solid","Dotted","Dashed","Double","Groove","Ridge","Inset","Outset","None","Hidden"],v=>({title:v,value:v.toLowerCase()})),pi=v=>{var x;const w=v.dom,_=null!==(x=w.getParent(v.selection.getStart(),w.isBlock))&&void 0!==x?x:v.getBody();return(v=>(x=>{const _=x.dom;return(_.getBoundingClientRect().width||_.offsetWidth)-Da(F=x,"padding-left",0)-Da(F,"padding-right",0)-Da(F,"border-left-width",0)-Da(F,"border-right-width",0);var F})(v))(Lt.fromDom(_))+"px"},cn=v=>x=>x.options.get(v),hi=cn("table_sizing_mode"),bi=cn("table_border_widths"),ks=cn("table_border_styles"),Es=cn("table_cell_advtab"),_s=cn("table_row_advtab"),Tr=cn("table_advtab"),iu=cn("table_appearance_options"),lu=cn("table_grid"),Or=cn("table_style_by_css"),vi=cn("table_cell_class_list"),po=cn("table_row_class_list"),yi=cn("table_class_list"),Ci=cn("table_toolbar"),Qn=cn("table_background_color_map"),Jn=cn("table_border_color_map"),Zo=v=>"fixed"===hi(v),ql=v=>"responsive"===hi(v),Jt=v=>{const x=v.options,w=x.get("table_default_styles");return x.isSet("table_default_styles")?w:(M=w,ql(_=v)||!Or(_)?M:Zo(_)?{...M,width:pi(_)}:{...M,width:"100%"});var _,M},Vl=v=>{const x=v.options,w=x.get("table_default_attributes");return x.isSet("table_default_attributes")?w:(M=w,ql(_=v)||Or(_)?M:Zo(_)?{...M,width:pi(_)}:{...M,width:"100%"});var _,M},Wl=(v,x)=>x.column>=v.startCol&&x.column+x.colspan-1<=v.finishCol&&x.row>=v.startRow&&x.row+x.rowspan-1<=v.finishRow,ot=we,tr=(v,x)=>{_n(v).each(w=>{w.dom.insertBefore(x.dom,v.dom)})},on=(v,x)=>{Ao(v).fold(()=>{_n(v).each(w=>{ho(w,x)})},w=>{tr(w,x)})},ho=(v,x)=>{v.dom.appendChild(x.dom)},Gl=(v,x)=>{je(x,(w,_)=>{on(0===_?v:x[_-1],w)})},wi=v=>{const x=v.dom;null!==x.parentNode&&x.parentNode.removeChild(x)},Yl=(v=>{const w=_=>v(_)?dt.from(_.dom.nodeValue):dt.none();return{get:_=>{if(!v(_))throw new Error("Can only get text value of a text node");return w(_).getOr("")},getOption:w,set:(_,M)=>{if(!v(_))throw new Error("Can only set raw text value of a text node");_.dom.nodeValue=M}}})(yr);var cu=["body","p","div","article","aside","figcaption","figure","footer","header","nav","section","ol","ul","li","table","thead","tbody","tfoot","caption","tr","td","th","h1","h2","h3","h4","h5","h6","blockquote","pre","address"];const Xl=(v,x,w)=>x.bind(_=>w.filter(ln(v.eq,_))),Ns={up:Ce({selector:Ae,closest:Gr,predicate:Nn,all:(v,x)=>{const w=sn(x)?x:Oe;let _=v.dom;const M=[];for(;null!=_.parentNode;){const F=_.parentNode,X=Lt.fromDom(F);if(M.push(X),!0===w(X))break;_=F}return M}}),down:Ce({selector:Er,predicate:Go}),styles:Ce({get:fo,getRaw:Yn,set:(v,x,w)=>{((_,M,F)=>{if(!Wn(F))throw console.error("Invalid call to CSS.set. Property ",M,":: Value ",F,":: Element ",_),new Error("CSS value must be a string: "+F);Fn(_)&&_.style.setProperty(M,F)})(v.dom,x,w)},remove:(v,x)=>{var w,_;_=x,Fn(w=v.dom)&&w.style.removeProperty(_),Ba(Hn(v,"style").map(Nr),"")&&Oa(v,"style")}}),attrs:Ce({get:It,set:(v,x,w)=>{ce(v.dom,x,w)},remove:Oa,copyTo:(v,x)=>{((w,_)=>{const M=w.dom;Yt(_,(F,X)=>{ce(M,X,F)})})(x,de(v.dom.attributes,(w,_)=>(w[_.name]=_.value,w),{}))}}),insert:Ce({before:tr,after:on,afterAll:Gl,append:ho,appendAll:(v,x)=>{je(x,w=>{ho(v,w)})},prepend:(v,x)=>{var w;(w=v,dt.from(w.dom.childNodes[0]).map(Lt.fromDom)).fold(()=>{ho(v,x)},w=>{v.dom.insertBefore(x.dom,w.dom)})},wrap:(v,x)=>{tr(v,x),ho(x,v)}}),remove:Ce({unwrap:v=>{const x=Gn(v);x.length>0&&Gl(v,x),wi(v)},remove:wi}),create:Ce({nu:Lt.fromTag,clone:v=>Lt.fromDom(v.dom.cloneNode(!1)),text:Lt.fromText}),query:Ce({comparePosition:(v,x)=>v.dom.compareDocumentPosition(x.dom),prevSibling:v=>dt.from(v.dom.previousSibling).map(Lt.fromDom),nextSibling:Ao}),property:Ce({children:Gn,name:Ve,parent:_n,document:v=>gn(v).dom,isText:yr,isComment:v=>8===Ot(v)||"#comment"===Ve(v),isElement:Re,isSpecial:v=>{const x=Ve(v);return kn(["script","noscript","iframe","noframes","noembed","title","style","textarea","xmp"],x)},getLanguage:v=>Re(v)?Hn(v,"lang"):dt.none(),getText:v=>Yl.get(v),setText:(v,x)=>Yl.set(v,x),isBoundary:v=>!!Re(v)&&("body"===Ve(v)||kn(cu,Ve(v))),isEmptyTag:v=>!!Re(v)&&kn(["br","img","hr","input"],Ve(v)),isNonEditable:v=>Re(v)&&"false"===It(v,"contenteditable")}),eq:J,is:Ta},xi=v=>Ae(v,"table"),fn=(v,x,w)=>tn(v,x).bind(_=>tn(v,w).bind(M=>{return(F=xi,X=[_,M],rt=Ns,nt=X,nt.length>0?(xt=(rt,et)=>F(et),((v,x,w,_)=>{const M=x(v,w);return F=(rt,et)=>{const nt=x(v,et);return Xl(v,rt,nt)},X=M,((rt,et)=>{for(let nt=rt.length-1;nt>=0;nt--)et(rt[nt])})(_,(rt,et)=>{X=F(X,rt)}),X;var F,X})(rt,xt,(ae=nt)[0],ae.slice(1))):dt.none()).map(rt=>({first:_,last:M,table:rt}));var rt,nt,xt,ae,F,X})),pn=v=>Ue(v,Lt.fromDom),Si="data-mce-selected",ki="data-mce-first-selected",Pa="data-mce-last-selected",Rs={selected:Si,selectedSelector:"td["+Si+"],th["+Si+"]",firstSelected:ki,firstSelectedSelector:"td["+ki+"],th["+ki+"]",lastSelected:Pa,lastSelectedSelector:"td["+Pa+"],th["+Pa+"]"},Me=v=>(x,w)=>{const _=Ve(x),M="col"===_||"colgroup"===_?pt(F=x).bind(X=>((nt,bt)=>{const xt=Er(nt,bt);return xt.length>0?dt.some(xt):dt.none()})(X,Rs.firstSelectedSelector)).fold(Ce(F),X=>X[0]):x;var F;return Gr(M,v,w)},Do=Me("th,td,caption"),An=Me("th,td"),Zn=v=>pn(v.model.table.getSelectedCells()),Le=[{text:"None",value:""},{text:"Top",value:"top"},{text:"Middle",value:"middle"},{text:"Bottom",value:"bottom"}],Ql=/^#?([a-f\d])([a-f\d])([a-f\d])$/i,Ei=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i,Qr=v=>{return(x=v,Yo(x,"#")?(w=x,w.substring(1)):x).toUpperCase();var w,x},Ts=v=>{const x=v.toString(16);return(1===x.length?"0"+x:x).toUpperCase()},Os=v=>{return x=Ts(v.red)+Ts(v.green)+Ts(v.blue),{value:Qr(x)};var x},Jl=/^\s*rgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)\s*$/i,Jr=/^\s*rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d?(?:\.\d+)?)\s*\)\s*$/i,Bs=(v,x,w,_)=>({red:v,green:x,blue:w,alpha:_}),Br=(v,x,w,_)=>{const M=parseInt(v,10),F=parseInt(x,10),X=parseInt(w,10),rt=parseFloat(_);return Bs(M,F,X,rt)},Ds=v=>{if("transparent"===v)return dt.some(Bs(0,0,0,0));const x=Jl.exec(v);if(null!==x)return dt.some(Br(x[1],x[2],x[3],"1"));const w=Jr.exec(v);return null!==w?dt.some(Br(w[1],w[2],w[3],w[4])):dt.none()},Dr=v=>{let x=v;return{get:()=>x,set:w=>{x=w}}},Ma=(v,x,w)=>_=>{const M=(rt=>{const et=Dr(dt.none()),nt=()=>et.get().each(rt);return{clear:()=>{nt(),et.set(dt.none())},isSet:()=>et.get().isSome(),get:()=>et.get(),set:bt=>{nt(),et.set(dt.some(bt))}}})(rt=>rt.unbind()),F=!Bo(w),X=()=>{const rt=Zn(v),et=nt=>v.formatter.match(x,{value:w},nt.dom,F);F?(_.setActive(!Ct(rt,et)),M.set(v.formatter.formatChanged(x,nt=>_.setActive(!nt),!0))):(_.setActive(Ro(rt,et)),M.set(v.formatter.formatChanged(x,_.setActive,!1,{value:w})))};return v.initialized?X():v.on("init",X),M.clear},Be=v=>Aa(v,"menu"),bo=v=>Ue(v,x=>{const w=x.text||x.title||"";return Be(x)?{text:w,items:bo(x.menu)}:{text:w,value:x.value}}),A=(v,x,w,_)=>Ue(x,M=>{const F=M.text||M.title;return Be(M)?{type:"nestedmenuitem",text:F,getSubmenuItems:()=>A(v,M.menu,w,_)}:{text:F,type:"togglemenuitem",onAction:()=>_(M.value),onSetup:Ma(v,w,M.value)}}),Zr=(v,x)=>w=>{v.execCommand("mceTableApplyCellStyle",!1,{[x]:w})},ta=v=>S(v,x=>Be(x)?[{...x,menu:ta(x.menu)}]:Bo(x.value)?[x]:[]),hn=(v,x,w,_)=>M=>M(A(v,x,w,_)),La=(v,x,w)=>{const _=Ue(x,M=>{return{text:M.title,value:"#"+(X=M.value,(F=X,rt=F,Ql.test(rt)||Ei.test(rt)?dt.some({value:Qr(F)}):dt.none()).orThunk(()=>Ds(X).map(Os)).getOrThunk(()=>{const rt=document.createElement("canvas");rt.height=1,rt.width=1;const et=rt.getContext("2d");et.clearRect(0,0,rt.width,rt.height),et.fillStyle="#FFFFFF",et.fillStyle=X,et.fillRect(0,0,1,1);const nt=et.getImageData(0,0,1,1).data;return Os(Bs(nt[0],nt[1],nt[2],nt[3]))})).value,type:"choiceitem"};var rt,F,X});return[{type:"fancymenuitem",fancytype:"colorswatch",initData:{colors:_.length>0?_:void 0,allowCustomColors:!1},onAction:M=>{const F="remove"===M.value?"":M.value;v.execCommand("mceTableApplyCellStyle",!1,{[w]:F})}}]},re=v=>()=>{const x="header"===v.queryCommandValue("mceTableRowType")?"body":"header";v.execCommand("mceTableRowType",!1,{type:x})},vo=v=>()=>{const x="th"===v.queryCommandValue("mceTableColType")?"td":"th";v.execCommand("mceTableColType",!1,{type:x})},Zl=[{name:"width",type:"input",label:"Width"},{name:"height",type:"input",label:"Height"},{name:"celltype",type:"listbox",label:"Cell type",items:[{text:"Cell",value:"td"},{text:"Header cell",value:"th"}]},{name:"scope",type:"listbox",label:"Scope",items:[{text:"None",value:""},{text:"Row",value:"row"},{text:"Column",value:"col"},{text:"Row group",value:"rowgroup"},{text:"Column group",value:"colgroup"}]},{name:"halign",type:"listbox",label:"Horizontal align",items:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]},{name:"valign",type:"listbox",label:"Vertical align",items:Le}],yo=v=>Zl.concat((x=>{const w=bo(vi(x));return w.length>0?dt.some({name:"class",type:"listbox",label:"Class",items:w}):dt.none()})(v).toArray()),Pr=(v,x)=>{const w=[{name:"borderstyle",type:"listbox",label:"Border style",items:[{text:"Select...",value:""}].concat(bo(ks(v)))},{name:"bordercolor",type:"colorinput",label:"Border color"},{name:"backgroundcolor",type:"colorinput",label:"Background color"}];return{title:"Advanced",name:"advanced",items:"cell"===x?[{name:"borderwidth",type:"input",label:"Border width"}].concat(w):w}},Tn=(v,x)=>{const w=v.dom;return{setAttrib:(_,M)=>{w.setAttrib(x,_,M)},setStyle:(_,M)=>{w.setStyle(x,_,M)},setFormat:(_,M)=>{""===M?v.formatter.remove(_,{value:null},x,!0):v.formatter.apply(_,{value:M},x)}}},_i=Cr("th"),tc=(v,x)=>v&&x?"sectionCells":v?"section":"cells",ea=(v,x)=>Ss(v.all,w=>ye(w.cells,_=>J(x,_.element))),Ni=(v,x)=>((v,x,w)=>{const _=(F=>{const X=[],rt=et=>{X.push(et)};for(let et=0;et<F.length;et++)F[et].each(rt);return X})(Ue(x.selection,F=>{return(X=F,((et,nt,bt=Oe)=>bt(nt)?dt.none():kn(et,Ve(nt))?dt.some(nt):Ae(nt,et.join(","),xt=>ie(xt,"table")||bt(xt)))(["td","th"],X,void 0)).bind(et=>ea(v,et)).filter(w);var X}));return _.length>0?dt.some(_):dt.none()})(v,x,xn),ze=(v,x)=>Ro(x,w=>ea(v,w).exists(F=>!F.isLocked)),na=(v,x)=>{return(_=x,_.mergable).filter(w=>ze(v,w.cells));var _},Ri=(v,x)=>{return(_=x,_.unmergable).filter(w=>ze(v,w));var _},Ai=((v=>{if(!hr(v))throw new Error("cases must be an array");if(0===v.length)throw new Error("there must be at least one case");const x=[],w={};je(v,(_,M)=>{const F=pe(_);if(1!==F.length)throw new Error("one and only one name per case");const X=F[0],rt=_[X];if(void 0!==w[X])throw new Error("duplicate key detected:"+X);if("cata"===X)throw new Error("cannot have a case named cata (sorry)");if(!hr(rt))throw new Error("case arguments must be an array");x.push(X),w[X]=(...et)=>{const nt=et.length;if(nt!==rt.length)throw new Error("Wrong number of arguments to case "+X+". Expected "+rt.length+" ("+rt+"), got "+nt);return{fold:(...bt)=>{if(bt.length!==v.length)throw new Error("Wrong number of arguments to fold. Expected "+v.length+", got "+bt.length);return bt[M].apply(null,et)},match:bt=>{const xt=pe(bt);if(x.length!==xt.length)throw new Error("Wrong number of arguments to match. Expected: "+x.join(",")+"\nActual: "+xt.join(","));if(!Ro(x,ae=>kn(xt,ae)))throw new Error("Not all branches were specified when using match. Specified: "+xt.join(", ")+"\nRequired: "+x.join(", "));return bt[X].apply(null,et)},log:bt=>{console.log(bt,{constructors:x,constructor:X,params:et})}}}})})([{none:[]},{only:["index"]},{left:["index","next"]},{middle:["prev","index","next"]},{right:["prev","index"]}]),(v,x)=>{const w=we(v);return Ni(w,x).bind(_=>{const M=_[_.length-1];return(v=>{const x=Ue(v,M=>(F=>{const X="thead"===F.section,rt=Ba((et=>{const nt=En(et,bt=>_i(bt.element));return 0===nt.length?dt.some("td"):nt.length===et.length?dt.some("th"):dt.none()})(F.cells),"th");return"tfoot"===F.section?{type:"footer"}:X||rt?{type:"header",subType:tc(X,rt)}:{type:"body"}})(M).type),w=kn(x,"header"),_=kn(x,"footer");if(w||_){const M=kn(x,"body");return!w||M||_?w||M||!_?dt.none():dt.some("footer"):dt.some("header")}return dt.some("body")})(w.all.slice(_[0].row,M.row+M.rowspan))}).getOr("")}),oa=v=>{return Yo(v,"rgb")?Ds(x=v).map(Os).map(w=>"#"+w.value).getOr(x):v;var x},Ia=v=>{const x=Lt.fromDom(v);return{borderwidth:Yn(x,"border-width").getOr(""),borderstyle:Yn(x,"border-style").getOr(""),bordercolor:Yn(x,"border-color").map(oa).getOr(""),backgroundcolor:Yn(x,"background-color").map(oa).getOr("")}},Ti=v=>{const x=v[0],w=v.slice(1);return je(w,_=>{je(pe(x),M=>{Yt(_,(F,X)=>{const rt=x[M];""!==rt&&M===X&&rt!==F&&(x[M]="")})})}),x},nc=(v,x,w,_)=>ye(v,M=>!Na(w.formatter.matchNode(_,x+M))).getOr(""),Co=ln(nc,["left","center","right"],"align"),Oi=ln(nc,["top","middle","bottom"],"valign"),du=v=>pt(Lt.fromDom(v)).map(x=>{const w={selection:pn(v.cells)};return Ai(x,w)}).getOr(""),Bi=(v,x)=>{const w=we(v),_=S(w.all,X=>X.cells),M=En(_,F=>Ct(x,X=>J(F.element,X)));return Ue(M,F=>({element:F.element.dom,column:Qo(w,F.column).map(X=>X.element.dom)}))},uu=(v,x,w,_)=>{const M=_.getData();_.close(),v.undoManager.transact(()=>{((F,X,rt,et)=>{const nt=nn(et,(bt,xt)=>rt[xt]!==bt);Sn(nt)>0&&X.length>=1&&pt(X[0]).each(bt=>{const xt=Bi(bt,X),ae=Sn(nn(nt,(Gt,ee)=>"scope"!==ee&&"celltype"!==ee))>0,Pt=Fe(nt,"celltype");(ae||Fe(nt,"scope"))&&((Gt,ee,Et,$t)=>{const Rt=1===ee.length;je(ee,Zt=>{const Kt=Zt.element,be=Rt?xn:$t,He=Tn(Gt,Kt);var it,rn,ke,So;it=He,rn=Zt.column.map(it=>Tn(Gt,it)).getOr(He),ke=Et,(So=be)("scope")&&it.setAttrib("scope",ke.scope),So("class")&&it.setAttrib("class",ke.class),So("height")&&it.setStyle("height",Rn(ke.height)),So("width")&&rn.setStyle("width",Rn(ke.width)),Es(Gt)&&((it,rn,ke)=>{ke("backgroundcolor")&&it.setFormat("tablecellbackgroundcolor",rn.backgroundcolor),ke("bordercolor")&&it.setFormat("tablecellbordercolor",rn.bordercolor),ke("borderstyle")&&it.setFormat("tablecellborderstyle",rn.borderstyle),ke("borderwidth")&&it.setFormat("tablecellborderwidth",Rn(rn.borderwidth))})(He,Et,be),$t("halign")&&Jo(Gt,Kt,Et.halign),$t("valign")&&((it,rn,ke)=>{Rr.each("top middle bottom".split(" "),So=>{So!==ke&&it.formatter.remove("valign"+So,{},rn)}),ke&&it.formatter.apply("valign"+ke,{},rn)})(Gt,Kt,Et.valign)})})(F,xt,et,ln(Fe,nt)),Pt&&F.execCommand("mceTableCellType",!1,{type:et.celltype,no_events:!0}),Ar(F,bt.dom,{structure:Pt,style:ae})})})(v,x,w,M),v.focus()})},ra=v=>{const x=Zn(v);if(0===x.length)return;const w=((F,X)=>{const rt=pt(X[0]).map(et=>Ue(Bi(et,X),nt=>((bt,xt,ae,Pt)=>{const Gt=bt.dom,ee=($t,Rt)=>Gt.getStyle($t,Rt)||Gt.getAttrib($t,Rt);return{width:ee(Pt.getOr(xt),"width"),height:ee(xt,"height"),scope:Gt.getAttrib(xt,"scope"),celltype:(Et=xt,Et.nodeName.toLowerCase()),class:Gt.getAttrib(xt,"class",""),halign:Co(bt,xt),valign:Oi(bt,xt),...ae?Ia(xt):{}};var Et})(F,nt.element,Es(F),nt.column)));return Ti(rt.getOrDie())})(v,x),_={type:"tabpanel",tabs:[{title:"General",name:"general",items:yo(v)},Pr(v,"cell")]},M={type:"panel",items:[{type:"grid",columns:2,items:yo(v)}]};v.windowManager.open({title:"Cell Properties",size:"normal",body:Es(v)?_:M,buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:w,onSubmit:ln(uu,v,x,w)})},oc=[{type:"listbox",name:"type",label:"Row type",items:[{text:"Header",value:"header"},{text:"Body",value:"body"},{text:"Footer",value:"footer"}]},{type:"listbox",name:"align",label:"Alignment",items:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]},{label:"Height",name:"height",type:"input"}],wo=v=>oc.concat((x=>{const w=bo(po(x));return w.length>0?dt.some({name:"class",type:"listbox",label:"Class",items:w}):dt.none()})(v).toArray()),Lr=(v,x,w,_)=>{const M=_.getData();_.close(),v.undoManager.transact(()=>{((F,X,rt,et)=>{const nt=nn(et,(bt,xt)=>rt[xt]!==bt);if(Sn(nt)>0){const bt=Fe(nt,"type"),xt=!bt||Sn(nt)>1;xt&&((ae,Pt,Gt,ee)=>{const Et=1===Pt.length?xn:ee;je(Pt,$t=>{const Rt=Tn(ae,$t);var Zt,Kt,be;Zt=Rt,Kt=Gt,(be=Et)("class")&&Zt.setAttrib("class",Kt.class),be("height")&&Zt.setStyle("height",Rn(Kt.height)),_s(ae)&&((Zt,Kt,be)=>{be("backgroundcolor")&&Zt.setStyle("background-color",Kt.backgroundcolor),be("bordercolor")&&Zt.setStyle("border-color",Kt.bordercolor),be("borderstyle")&&Zt.setStyle("border-style",Kt.borderstyle)})(Rt,Gt,Et),ee("align")&&Jo(ae,$t,Gt.align)})})(F,X,et,ln(Fe,nt)),bt&&F.execCommand("mceTableRowType",!1,{type:et.type,no_events:!0}),pt(Lt.fromDom(X[0])).each(ae=>Ar(F,ae.dom,{structure:bt,style:xt}))}})(v,x,w,M),v.focus()})},Fa=v=>{const x=((v,x)=>{const w=An(v),_=w.bind(M=>pt(M)).map(M=>Ft(M));return _r(w,_,(M,F)=>En(F,X=>Ct(pn(X.dom.cells),rt=>"1"===It(rt,x)||J(rt,M)))).getOr([])})(Sr(v),Rs.selected);if(0===x.length)return;const w=Ue(x,X=>((rt,et,nt)=>{const bt=rt.dom;return{height:bt.getStyle(et,"height")||bt.getAttrib(et,"height"),class:bt.getAttrib(et,"class",""),type:du(et),align:Co(rt,et),...nt?Ia(et):{}}})(v,X.dom,_s(v))),_=Ti(w),M={type:"tabpanel",tabs:[{title:"General",name:"general",items:wo(v)},Pr(v,"row")]},F={type:"panel",items:[{type:"grid",columns:2,items:wo(v)}]};v.windowManager.open({title:"Row Properties",size:"normal",body:_s(v)?M:F,buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:_,onSubmit:ln(Lr,v,Ue(x,X=>X.dom),_)})},Ua=(v,x,w)=>{const _=w?[{type:"input",name:"cols",label:"Cols",inputMode:"numeric"},{type:"input",name:"rows",label:"Rows",inputMode:"numeric"}]:[],M=iu(v)?[{type:"input",name:"cellspacing",label:"Cell spacing",inputMode:"numeric"},{type:"input",name:"cellpadding",label:"Cell padding",inputMode:"numeric"},{type:"input",name:"border",label:"Border width"},{type:"label",label:"Caption",items:[{type:"checkbox",name:"caption",label:"Show caption"}]}]:[],F=x.length>0?[{type:"listbox",name:"class",label:"Class",items:x}]:[];return _.concat([{type:"input",name:"width",label:"Width"},{type:"input",name:"height",label:"Height"}]).concat(M).concat([{type:"listbox",name:"align",label:"Alignment",items:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]}]).concat(F)},er=(v,x,w,_)=>{if("TD"===x.tagName||"TH"===x.tagName)Wn(w)&&vr(_)?v.setStyle(x,w,_):v.setStyles(x,w);else if(x.children)for(let M=0;M<x.children.length;M++)er(v,x.children[M],w,_)},aa=(v,x,w,_)=>{const M=v.dom,F=_.getData(),X=nn(F,(rt,et)=>w[et]!==rt);_.close(),""===F.class&&delete F.class,v.undoManager.transact(()=>{if(!x){const rt=Xo(F.cols).getOr(1),et=Xo(F.rows).getOr(1);v.execCommand("mceInsertTable",!1,{rows:et,columns:rt}),x=An(Sr(v),xr(v)).bind(nt=>pt(nt,xr(v))).map(nt=>nt.dom).getOrDie()}if(Sn(X)>0){const rt={border:Fe(X,"border"),bordercolor:Fe(X,"bordercolor"),cellpadding:Fe(X,"cellpadding")};((nt,bt,xt,ae)=>{const Pt=nt.dom,Gt={},ee={},Et=Or(nt),$t=Tr(nt);if(Na(xt.class)||(Gt.class=xt.class),ee.height=Rn(xt.height),Et?ee.width=Rn(xt.width):Pt.getAttrib(bt,"width")&&(Gt.width=(Rt=xt.width)?Rt.replace(/px$/,""):""),Et?(ee["border-width"]=Rn(xt.border),ee["border-spacing"]=Rn(xt.cellspacing)):(Gt.border=xt.border,Gt.cellpadding=xt.cellpadding,Gt.cellspacing=xt.cellspacing),Et&&bt.children){const Rt={};if(ae.border&&(Rt["border-width"]=Rn(xt.border)),ae.cellpadding&&(Rt.padding=Rn(xt.cellpadding)),$t&&ae.bordercolor&&(Rt["border-color"]=xt.bordercolor),!(Zt=>{for(const Kt in Zt)if(st.call(Zt,Kt))return!1;return!0})(Rt))for(let Zt=0;Zt<bt.children.length;Zt++)er(Pt,bt.children[Zt],Rt)}var Rt;if($t){const Rt=xt;ee["background-color"]=Rt.backgroundcolor,ee["border-color"]=Rt.bordercolor,ee["border-style"]=Rt.borderstyle}Gt.style=Pt.serializeStyle({...Jt(nt),...ee}),Pt.setAttribs(bt,{...Vl(nt),...Gt})})(v,x,F,rt);const et=M.select("caption",x)[0];(et&&!F.caption||!et&&F.caption)&&v.execCommand("mceTableToggleCaption"),Jo(v,x,F.align)}if(v.focus(),v.addVisual(),Sn(X)>0){const rt=Fe(X,"caption"),et=!rt||Sn(X)>1;Ar(v,x,{structure:rt,style:et})}})},rc=(v,x)=>{const w=v.dom;let _,M=((et,nt)=>{const bt=Jt(et),xt=Vl(et),ae=nt?{borderstyle:jt(bt,"border-style").getOr(""),bordercolor:oa(jt(bt,"border-color").getOr("")),backgroundcolor:oa(jt(bt,"background-color").getOr(""))}:{};return{height:"",width:"100%",cellspacing:"",cellpadding:"",caption:!1,class:"",align:"",border:"",...bt,...xt,...ae,...(()=>{const Pt=bt["border-width"];return Or(et)&&Pt?{border:Pt}:jt(xt,"border").fold(()=>({}),Gt=>({border:Gt}))})(),...jt(bt,"border-spacing").or(jt(xt,"cellspacing")).fold(()=>({}),Pt=>({cellspacing:Pt})),...jt(bt,"border-padding").or(jt(xt,"cellpadding")).fold(()=>({}),Pt=>({cellpadding:Pt}))}})(v,Tr(v));x?(M.cols="1",M.rows="1",Tr(v)&&(M.borderstyle="",M.bordercolor="",M.backgroundcolor="")):(_=w.getParent(v.selection.getStart(),"table",v.getBody()),_?M=((et,nt,bt)=>{const xt=et.dom,ae=Or(et)?xt.getStyle(nt,"border-spacing")||xt.getAttrib(nt,"cellspacing"):xt.getAttrib(nt,"cellspacing")||xt.getStyle(nt,"border-spacing"),Pt=Or(et)?Xn(xt,nt,"padding")||xt.getAttrib(nt,"cellpadding"):xt.getAttrib(nt,"cellpadding")||Xn(xt,nt,"padding");return{width:xt.getStyle(nt,"width")||xt.getAttrib(nt,"width"),height:xt.getStyle(nt,"height")||xt.getAttrib(nt,"height"),cellspacing:ae??"",cellpadding:Pt??"",border:((Gt,ee)=>{const Et=Yn(Lt.fromDom(ee),"border-width");return Or(et)&&Et.isSome()?Et.getOr(""):Gt.getAttrib(ee,"border")||Xn(et.dom,ee,"border-width")||Xn(et.dom,ee,"border")||""})(xt,nt),caption:!!xt.select("caption",nt)[0],class:xt.getAttrib(nt,"class",""),align:Co(et,nt),...bt?Ia(nt):{}}})(v,_,Tr(v)):Tr(v)&&(M.borderstyle="",M.bordercolor="",M.backgroundcolor=""));const F=bo(yi(v));F.length>0&&M.class&&(M.class=M.class.replace(/\s*mce\-item\-table\s*/g,""));const X={type:"grid",columns:2,items:Ua(v,F,x)},rt=Tr(v)?{type:"tabpanel",tabs:[{title:"General",name:"general",items:[X]},Pr(v,"table")]}:{type:"panel",items:[X]};v.windowManager.open({title:"Table Properties",size:"normal",body:rt,onSubmit:ln(aa,v,_,M),buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:M})},ac=Nt,sc=v=>{const x=(w,_)=>Hn(w,_).exists(M=>parseInt(M,10)>1);return v.length>0&&Ro(v,w=>x(w,"rowspan")||x(w,"colspan"))?dt.some(v):dt.none()},Ps=(v,x,w)=>{return x.length<=1?dt.none():(_=v,M=w.firstSelectedSelector,F=w.lastSelectedSelector,fn(_,M,F).bind(X=>{const rt=xt=>J(_,xt),et="thead,tfoot,tbody,table",nt=Ae(X.first,et,rt),bt=Ae(X.last,et,rt);return nt.bind(xt=>bt.bind(ae=>{return J(xt,ae)?(Gt=X.first,ee=X.last,((v,x,w)=>((_,M,F)=>{const X=ue(_,M,J),rt=ue(_,F,J);return X.bind(et=>rt.map(nt=>{return bt=et,xt=nt,{startRow:Math.min(bt.row,xt.row),startCol:Math.min(bt.column,xt.column),finishRow:Math.max(bt.row+bt.rowspan-1,xt.row+xt.rowspan-1),finishCol:Math.max(bt.column+bt.colspan-1,xt.column+xt.colspan-1)};var bt,xt}))})(v,x,w).bind(_=>((M,F)=>{let X=!0;const rt=ln(Wl,F);for(let et=F.startRow;et<=F.finishRow;et++)for(let nt=F.startCol;nt<=F.finishCol;nt++)X=X&&Ke(M,et,nt).exists(rt);return X?dt.some(F):dt.none()})(v,_)))(ot(X.table),Gt,ee)):dt.none();var Gt,ee}))})).map(X=>({bounds:X,cells:x}));var _,M,F};var ic=tinymce.util.Tools.resolve("tinymce.FakeClipboard");const za="x-tinymce/dom-table-",lc=za+"rows",cc=za+"columns",bn=v=>{var x;const w=null!==(x=ic.read())&&void 0!==x?x:[];return Ss(w,_=>dt.from(_.getType(v)))},to=()=>bn(lc),Ha=()=>bn(cc),Se=v=>x=>{const w=()=>{x.setEnabled(v.selection.isEditable())};return v.on("NodeChange",w),w(),()=>{v.off("NodeChange",w)}},Ms=v=>x=>{const w=()=>{x.setEnabled(v.selection.isEditable())};return v.on("NodeChange",w),w(),()=>{v.off("NodeChange",w)}};xs.add("table",v=>{const x=(v=>{const x=Dr(dt.none()),w=Dr([]);let _=dt.none();const M=Cr("caption"),F=Et=>_.forall($t=>!$t[Et]),X=()=>Do(Sr(v),xr(v)).bind(Et=>{return _r(pt(Et),Do((Rt=v,Lt.fromDom(Rt.selection.getEnd())),xr(v)).bind(pt),(Rt,Zt)=>{return J(Rt,Zt)?M(Et)?dt.some({element:Kt=Et,mergable:dt.none(),unmergable:dt.none(),selection:[Kt]}):dt.some(((Kt,be,He)=>({element:He,mergable:Ps(be,Kt,Rs),unmergable:sc(Kt),selection:ac(Kt)}))(Zn(v),Rt,Et)):dt.none();var Kt}).bind(Nt);var Rt}),rt=Et=>pt(Et.element).map($t=>{const Rt=we($t),Zt=Ni(Rt,Et).getOr([]),Kt=de(Zt,(be,He)=>(He.isLocked&&(be.onAny=!0,0===He.column?be.onFirst=!0:He.column+He.colspan>=Rt.grid.columns&&(be.onLast=!0)),be),{onAny:!1,onFirst:!1,onLast:!1});return{mergeable:na(Rt,Et).isSome(),unmergeable:Ri(Rt,Et).isSome(),locked:Kt}}),et=()=>{x.set((Et=>{let $t,Rt=!1;return(...Zt)=>(Rt||(Rt=!0,$t=Et.apply(null,Zt)),$t)})(X)()),_=x.get().bind(rt),je(w.get(),Ee)},nt=Et=>(Et(),w.set(w.get().concat([Et])),()=>{w.set(En(w.get(),$t=>$t!==Et))}),bt=(Et,$t)=>nt(()=>x.get().fold(()=>{Et.setEnabled(!1)},Rt=>{Et.setEnabled(!$t(Rt)&&v.selection.isEditable())})),xt=(Et,$t,Rt)=>nt(()=>x.get().fold(()=>{Et.setEnabled(!1),Et.setActive(!1)},Zt=>{Et.setEnabled(!$t(Zt)&&v.selection.isEditable()),Et.setActive(Rt(Zt))})),ae=Et=>_.exists($t=>$t.locked[Et]),Pt=(Et,$t)=>Rt=>xt(Rt,Zt=>M(Zt.element),()=>v.queryCommandValue(Et)===$t),Gt=Pt("mceTableRowType","header"),ee=Pt("mceTableColType","th");return v.on("NodeChange ExecCommand TableSelectorChange",et),{onSetupTable:Et=>bt(Et,$t=>!1),onSetupCellOrRow:Et=>bt(Et,$t=>M($t.element)),onSetupColumn:Et=>$t=>bt($t,Rt=>M(Rt.element)||ae(Et)),onSetupPasteable:Et=>$t=>bt($t,Rt=>M(Rt.element)||Et().isNone()),onSetupPasteableColumn:(Et,$t)=>Rt=>bt(Rt,Zt=>M(Zt.element)||Et().isNone()||ae($t)),onSetupMergeable:Et=>bt(Et,$t=>F("mergeable")),onSetupUnmergeable:Et=>bt(Et,$t=>F("unmergeable")),resetTargets:et,onSetupTableWithCaption:Et=>xt(Et,Oe,$t=>pt($t.element,xr(v)).exists(Rt=>(v=>ye(v.dom.childNodes,M=>{return F=Lt.fromDom(M),ie(F,"caption");var F}).map(Lt.fromDom))(Rt).isSome())),onSetupTableRowHeaders:Gt,onSetupTableColumnHeaders:ee,targets:x.get}})(v);(w=>{const _=w.options.register;_("table_border_widths",{processor:"object[]",default:Hl}),_("table_border_styles",{processor:"object[]",default:su}),_("table_cell_advtab",{processor:"boolean",default:!0}),_("table_row_advtab",{processor:"boolean",default:!0}),_("table_advtab",{processor:"boolean",default:!0}),_("table_appearance_options",{processor:"boolean",default:!0}),_("table_grid",{processor:"boolean",default:!zl.deviceType.isTouch()}),_("table_cell_class_list",{processor:"object[]",default:[]}),_("table_row_class_list",{processor:"object[]",default:[]}),_("table_class_list",{processor:"object[]",default:[]}),_("table_toolbar",{processor:"string",default:"tableprops tabledelete | tableinsertrowbefore tableinsertrowafter tabledeleterow | tableinsertcolbefore tableinsertcolafter tabledeletecol"}),_("table_background_color_map",{processor:"object[]",default:[]}),_("table_border_color_map",{processor:"object[]",default:[]})})(v),(v=>{Yt({mceTableProps:ln(rc,v,!1),mceTableRowProps:ln(Fa,v),mceTableCellProps:ln(ra,v),mceInsertTableDialog:ln(rc,v,!0)},(x,w)=>v.addCommand(w,()=>{return _=x,void((F=Sr(v),X=Cr("table"),We((rt,et)=>et(rt),Nn,F,X,void 0)).forall(le)&&_());var F,X,_}))})(v),((w,_)=>{const M=Pt=>()=>w.execCommand(Pt),F=(Pt,Gt)=>!!w.queryCommandSupported(Gt.command)&&(w.ui.registry.addMenuItem(Pt,{...Gt,onAction:sn(Gt.onAction)?Gt.onAction:M(Gt.command)}),!0),X=(Pt,Gt)=>{w.queryCommandSupported(Gt.command)&&w.ui.registry.addToggleMenuItem(Pt,{...Gt,onAction:sn(Gt.onAction)?Gt.onAction:M(Gt.command)})},rt=Pt=>{w.execCommand("mceInsertTable",!1,{rows:Pt.numRows,columns:Pt.numColumns})},et=[F("tableinsertrowbefore",{text:"Insert row before",icon:"table-insert-row-above",command:"mceTableInsertRowBefore",onSetup:_.onSetupCellOrRow}),F("tableinsertrowafter",{text:"Insert row after",icon:"table-insert-row-after",command:"mceTableInsertRowAfter",onSetup:_.onSetupCellOrRow}),F("tabledeleterow",{text:"Delete row",icon:"table-delete-row",command:"mceTableDeleteRow",onSetup:_.onSetupCellOrRow}),F("tablerowprops",{text:"Row properties",icon:"table-row-properties",command:"mceTableRowProps",onSetup:_.onSetupCellOrRow}),F("tablecutrow",{text:"Cut row",icon:"cut-row",command:"mceTableCutRow",onSetup:_.onSetupCellOrRow}),F("tablecopyrow",{text:"Copy row",icon:"duplicate-row",command:"mceTableCopyRow",onSetup:_.onSetupCellOrRow}),F("tablepasterowbefore",{text:"Paste row before",icon:"paste-row-before",command:"mceTablePasteRowBefore",onSetup:_.onSetupPasteable(to)}),F("tablepasterowafter",{text:"Paste row after",icon:"paste-row-after",command:"mceTablePasteRowAfter",onSetup:_.onSetupPasteable(to)})],nt=[F("tableinsertcolumnbefore",{text:"Insert column before",icon:"table-insert-column-before",command:"mceTableInsertColBefore",onSetup:_.onSetupColumn("onFirst")}),F("tableinsertcolumnafter",{text:"Insert column after",icon:"table-insert-column-after",command:"mceTableInsertColAfter",onSetup:_.onSetupColumn("onLast")}),F("tabledeletecolumn",{text:"Delete column",icon:"table-delete-column",command:"mceTableDeleteCol",onSetup:_.onSetupColumn("onAny")}),F("tablecutcolumn",{text:"Cut column",icon:"cut-column",command:"mceTableCutCol",onSetup:_.onSetupColumn("onAny")}),F("tablecopycolumn",{text:"Copy column",icon:"duplicate-column",command:"mceTableCopyCol",onSetup:_.onSetupColumn("onAny")}),F("tablepastecolumnbefore",{text:"Paste column before",icon:"paste-column-before",command:"mceTablePasteColBefore",onSetup:_.onSetupPasteableColumn(Ha,"onFirst")}),F("tablepastecolumnafter",{text:"Paste column after",icon:"paste-column-after",command:"mceTablePasteColAfter",onSetup:_.onSetupPasteableColumn(Ha,"onLast")})],bt=[F("tablecellprops",{text:"Cell properties",icon:"table-cell-properties",command:"mceTableCellProps",onSetup:_.onSetupCellOrRow}),F("tablemergecells",{text:"Merge cells",icon:"table-merge-cells",command:"mceTableMergeCells",onSetup:_.onSetupMergeable}),F("tablesplitcells",{text:"Split cell",icon:"table-split-cells",command:"mceTableSplitCells",onSetup:_.onSetupUnmergeable})];lu(w)?w.ui.registry.addNestedMenuItem("inserttable",{text:"Table",icon:"table",getSubmenuItems:()=>[{type:"fancymenuitem",fancytype:"inserttable",onAction:rt}],onSetup:Ms(w)}):w.ui.registry.addMenuItem("inserttable",{text:"Table",icon:"table",onAction:M("mceInsertTableDialog"),onSetup:Ms(w)}),w.ui.registry.addMenuItem("inserttabledialog",{text:"Insert table",icon:"table",onAction:M("mceInsertTableDialog"),onSetup:Ms(w)}),F("tableprops",{text:"Table properties",onSetup:_.onSetupTable,command:"mceTableProps"}),F("deletetable",{text:"Delete table",icon:"table-delete-table",onSetup:_.onSetupTable,command:"mceTableDelete"}),kn(et,!0)&&w.ui.registry.addNestedMenuItem("row",{type:"nestedmenuitem",text:"Row",getSubmenuItems:Ce("tableinsertrowbefore tableinsertrowafter tabledeleterow tablerowprops | tablecutrow tablecopyrow tablepasterowbefore tablepasterowafter")}),kn(nt,!0)&&w.ui.registry.addNestedMenuItem("column",{type:"nestedmenuitem",text:"Column",getSubmenuItems:Ce("tableinsertcolumnbefore tableinsertcolumnafter tabledeletecolumn | tablecutcolumn tablecopycolumn tablepastecolumnbefore tablepastecolumnafter")}),kn(bt,!0)&&w.ui.registry.addNestedMenuItem("cell",{type:"nestedmenuitem",text:"Cell",getSubmenuItems:Ce("tablecellprops tablemergecells tablesplitcells")}),w.ui.registry.addContextMenu("table",{update:()=>(_.resetTargets(),_.targets().fold(Ce(""),Pt=>"caption"===Ve(Pt.element)?"tableprops deletetable":"cell row column | advtablesort | tableprops deletetable"))});const xt=ta(yi(w));0!==xt.length&&w.queryCommandSupported("mceTableToggleClass")&&w.ui.registry.addNestedMenuItem("tableclass",{icon:"table-classes",text:"Table styles",getSubmenuItems:()=>A(w,xt,"tableclass",Pt=>w.execCommand("mceTableToggleClass",!1,Pt)),onSetup:_.onSetupTable});const ae=ta(vi(w));0!==ae.length&&w.queryCommandSupported("mceTableCellToggleClass")&&w.ui.registry.addNestedMenuItem("tablecellclass",{icon:"table-cell-classes",text:"Cell styles",getSubmenuItems:()=>A(w,ae,"tablecellclass",Pt=>w.execCommand("mceTableCellToggleClass",!1,Pt)),onSetup:_.onSetupCellOrRow}),w.queryCommandSupported("mceTableApplyCellStyle")&&(w.ui.registry.addNestedMenuItem("tablecellvalign",{icon:"vertical-align",text:"Vertical align",getSubmenuItems:()=>A(w,Le,"tablecellverticalalign",Zr(w,"vertical-align")),onSetup:_.onSetupCellOrRow}),w.ui.registry.addNestedMenuItem("tablecellborderwidth",{icon:"border-width",text:"Border width",getSubmenuItems:()=>A(w,bi(w),"tablecellborderwidth",Zr(w,"border-width")),onSetup:_.onSetupCellOrRow}),w.ui.registry.addNestedMenuItem("tablecellborderstyle",{icon:"border-style",text:"Border style",getSubmenuItems:()=>A(w,ks(w),"tablecellborderstyle",Zr(w,"border-style")),onSetup:_.onSetupCellOrRow}),w.ui.registry.addNestedMenuItem("tablecellbackgroundcolor",{icon:"cell-background-color",text:"Background color",getSubmenuItems:()=>La(w,Qn(w),"background-color"),onSetup:_.onSetupCellOrRow}),w.ui.registry.addNestedMenuItem("tablecellbordercolor",{icon:"cell-border-color",text:"Border color",getSubmenuItems:()=>La(w,Jn(w),"border-color"),onSetup:_.onSetupCellOrRow})),X("tablecaption",{icon:"table-caption",text:"Table caption",command:"mceTableToggleCaption",onSetup:_.onSetupTableWithCaption}),X("tablerowheader",{text:"Row header",icon:"table-top-header",command:"mceTableRowType",onAction:re(w),onSetup:_.onSetupTableRowHeaders}),X("tablecolheader",{text:"Column header",icon:"table-left-header",command:"mceTableColType",onAction:vo(w),onSetup:_.onSetupTableRowHeaders})})(v,x),((w,_)=>{w.ui.registry.addMenuButton("table",{tooltip:"Table",icon:"table",onSetup:Se(w),fetch:nt=>nt("inserttable | cell row column | advtablesort | tableprops deletetable")});const M=nt=>()=>w.execCommand(nt),F=(nt,bt)=>{w.queryCommandSupported(bt.command)&&w.ui.registry.addButton(nt,{...bt,onAction:sn(bt.onAction)?bt.onAction:M(bt.command)})},X=(nt,bt)=>{w.queryCommandSupported(bt.command)&&w.ui.registry.addToggleButton(nt,{...bt,onAction:sn(bt.onAction)?bt.onAction:M(bt.command)})};F("tableprops",{tooltip:"Table properties",command:"mceTableProps",icon:"table",onSetup:_.onSetupTable}),F("tabledelete",{tooltip:"Delete table",command:"mceTableDelete",icon:"table-delete-table",onSetup:_.onSetupTable}),F("tablecellprops",{tooltip:"Cell properties",command:"mceTableCellProps",icon:"table-cell-properties",onSetup:_.onSetupCellOrRow}),F("tablemergecells",{tooltip:"Merge cells",command:"mceTableMergeCells",icon:"table-merge-cells",onSetup:_.onSetupMergeable}),F("tablesplitcells",{tooltip:"Split cell",command:"mceTableSplitCells",icon:"table-split-cells",onSetup:_.onSetupUnmergeable}),F("tableinsertrowbefore",{tooltip:"Insert row before",command:"mceTableInsertRowBefore",icon:"table-insert-row-above",onSetup:_.onSetupCellOrRow}),F("tableinsertrowafter",{tooltip:"Insert row after",command:"mceTableInsertRowAfter",icon:"table-insert-row-after",onSetup:_.onSetupCellOrRow}),F("tabledeleterow",{tooltip:"Delete row",command:"mceTableDeleteRow",icon:"table-delete-row",onSetup:_.onSetupCellOrRow}),F("tablerowprops",{tooltip:"Row properties",command:"mceTableRowProps",icon:"table-row-properties",onSetup:_.onSetupCellOrRow}),F("tableinsertcolbefore",{tooltip:"Insert column before",command:"mceTableInsertColBefore",icon:"table-insert-column-before",onSetup:_.onSetupColumn("onFirst")}),F("tableinsertcolafter",{tooltip:"Insert column after",command:"mceTableInsertColAfter",icon:"table-insert-column-after",onSetup:_.onSetupColumn("onLast")}),F("tabledeletecol",{tooltip:"Delete column",command:"mceTableDeleteCol",icon:"table-delete-column",onSetup:_.onSetupColumn("onAny")}),F("tablecutrow",{tooltip:"Cut row",command:"mceTableCutRow",icon:"cut-row",onSetup:_.onSetupCellOrRow}),F("tablecopyrow",{tooltip:"Copy row",command:"mceTableCopyRow",icon:"duplicate-row",onSetup:_.onSetupCellOrRow}),F("tablepasterowbefore",{tooltip:"Paste row before",command:"mceTablePasteRowBefore",icon:"paste-row-before",onSetup:_.onSetupPasteable(to)}),F("tablepasterowafter",{tooltip:"Paste row after",command:"mceTablePasteRowAfter",icon:"paste-row-after",onSetup:_.onSetupPasteable(to)}),F("tablecutcol",{tooltip:"Cut column",command:"mceTableCutCol",icon:"cut-column",onSetup:_.onSetupColumn("onAny")}),F("tablecopycol",{tooltip:"Copy column",command:"mceTableCopyCol",icon:"duplicate-column",onSetup:_.onSetupColumn("onAny")}),F("tablepastecolbefore",{tooltip:"Paste column before",command:"mceTablePasteColBefore",icon:"paste-column-before",onSetup:_.onSetupPasteableColumn(Ha,"onFirst")}),F("tablepastecolafter",{tooltip:"Paste column after",command:"mceTablePasteColAfter",icon:"paste-column-after",onSetup:_.onSetupPasteableColumn(Ha,"onLast")}),F("tableinsertdialog",{tooltip:"Insert table",command:"mceInsertTableDialog",icon:"table",onSetup:Se(w)});const rt=ta(yi(w));0!==rt.length&&w.queryCommandSupported("mceTableToggleClass")&&w.ui.registry.addMenuButton("tableclass",{icon:"table-classes",tooltip:"Table styles",fetch:hn(w,rt,"tableclass",nt=>w.execCommand("mceTableToggleClass",!1,nt)),onSetup:_.onSetupTable});const et=ta(vi(w));0!==et.length&&w.queryCommandSupported("mceTableCellToggleClass")&&w.ui.registry.addMenuButton("tablecellclass",{icon:"table-cell-classes",tooltip:"Cell styles",fetch:hn(w,et,"tablecellclass",nt=>w.execCommand("mceTableCellToggleClass",!1,nt)),onSetup:_.onSetupCellOrRow}),w.queryCommandSupported("mceTableApplyCellStyle")&&(w.ui.registry.addMenuButton("tablecellvalign",{icon:"vertical-align",tooltip:"Vertical align",fetch:hn(w,Le,"tablecellverticalalign",Zr(w,"vertical-align")),onSetup:_.onSetupCellOrRow}),w.ui.registry.addMenuButton("tablecellborderwidth",{icon:"border-width",tooltip:"Border width",fetch:hn(w,bi(w),"tablecellborderwidth",Zr(w,"border-width")),onSetup:_.onSetupCellOrRow}),w.ui.registry.addMenuButton("tablecellborderstyle",{icon:"border-style",tooltip:"Border style",fetch:hn(w,ks(w),"tablecellborderstyle",Zr(w,"border-style")),onSetup:_.onSetupCellOrRow}),w.ui.registry.addMenuButton("tablecellbackgroundcolor",{icon:"cell-background-color",tooltip:"Background color",fetch:nt=>nt(La(w,Qn(w),"background-color")),onSetup:_.onSetupCellOrRow}),w.ui.registry.addMenuButton("tablecellbordercolor",{icon:"cell-border-color",tooltip:"Border color",fetch:nt=>nt(La(w,Jn(w),"border-color")),onSetup:_.onSetupCellOrRow})),X("tablecaption",{tooltip:"Table caption",icon:"table-caption",command:"mceTableToggleCaption",onSetup:_.onSetupTableWithCaption}),X("tablerowheader",{tooltip:"Row header",icon:"table-top-header",command:"mceTableRowType",onAction:re(w),onSetup:_.onSetupTableRowHeaders}),X("tablecolheader",{tooltip:"Column header",icon:"table-left-header",command:"mceTableColType",onAction:vo(w),onSetup:_.onSetupTableColumnHeaders})})(v,x),(w=>{const _=Ci(w);_.length>0&&w.ui.registry.addContextToolbar("table",{predicate:M=>w.dom.is(M,"table")&&w.getBody().contains(M)&&w.dom.isEditable(M.parentNode),items:_,scope:"node",position:"node"})})(v)})}();