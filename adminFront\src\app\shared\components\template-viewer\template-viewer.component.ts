import { Component, EventEmitter, Input, Output, OnInit, OnChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { NbCardModule, NbButtonModule } from '@nebular/theme';
import { TemplateService } from 'src/services/api/services/template.service';
import { TemplateGetListArgs, GetTemplateByIdArgs, GetTemplateDetailByIdArgs } from 'src/services/api/models';

@Component({
  selector: 'app-template-viewer',
  templateUrl: './template-viewer.component.html',
  styleUrls: ['./template-viewer.component.scss'],
  standalone: true,
  imports: [CommonModule, FormsModule, NbCardModule, NbButtonModule],
})
export class TemplateViewerComponent implements OnInit, OnChanges {
  @Input() templateType: number = 1; // 模板類型，1=客變需求，從父元件傳入不可異動
  @Output() selectTemplate = new EventEmitter<Template>();
  @Output() close = new EventEmitter<void>(); // 關閉事件

  // 公開Math對象供模板使用
  Math = Math;

  // 內部管理的模板資料
  templates: Template[] = [];
  templateDetails: TemplateDetail[] = []; // 保留用於向後相容
  selectedTemplate: Template | null = null;

  // 新的詳情資料管理
  currentTemplateDetailsData: TemplateDetailItem[] = [];
  detailSearchKeyword = ''; // 明細專用搜尋關鍵字

  // 查詢功能
  searchKeyword = '';
  filteredTemplates: Template[] = [];

  // 分頁功能 - 模板列表
  templatePagination = {
    currentPage: 1,
    pageSize: 10,
    totalItems: 0,
    totalPages: 0
  };
  paginatedTemplates: Template[] = [];

  // 分頁功能 - 模板詳情
  detailPagination = {
    currentPage: 1,
    pageSize: 5,
    totalItems: 0,
    totalPages: 0
  };
  paginatedDetails: TemplateDetail[] = [];



  constructor(private templateService: TemplateService) { }

  ngOnInit() {
    this.loadTemplates();
    this.updateFilteredTemplates();
  }

  ngOnChanges() {
    this.updateFilteredTemplates();
  }

  // 載入模板列表 - 使用真實的 API 調用
  loadTemplates() {
    // 準備 API 請求參數
    const getTemplateListArgs: TemplateGetListArgs = {
      CTemplateType: this.templateType, // 1=客變需求
      PageIndex: 1, // 暫時載入第一頁
      PageSize: 100, // 暫時載入較多資料
      CTemplateName: null // 不篩選名稱
    };

    // 調用 GetTemplateList API
    this.templateService.apiTemplateGetTemplateListPost$Json({
      body: getTemplateListArgs
    }).subscribe({
      next: (response) => {
        if (response.StatusCode === 0 && response.Entries) {
          // API 調用成功

          // 轉換 API 回應為內部格式
          this.templates = response.Entries.map(item => ({
            TemplateID: item.CTemplateId,
            TemplateName: item.CTemplateName || '',
            Description: item.CTemplateName || '',
            CreateTime: item.CCreateDt,
            UpdateTime: item.CUpdateDt,
            Creator: item.CCreator || undefined,
            Updator: item.CUpdator || undefined
          }));

          // 更新過濾列表
          this.updateFilteredTemplates();

          // 清空詳情資料，改為按需載入
          this.templateDetails = [];
          this.currentTemplateDetailsData = [];
        } else {
          // API 返回錯誤
          this.templates = [];
          this.templateDetails = [];
          this.updateFilteredTemplates();
        }
      },
      error: () => {
        // HTTP 請求錯誤
        this.templates = [];
        this.templateDetails = [];
        this.updateFilteredTemplates();
      }
    });
  }

  // 更新過濾後的模板列表
  updateFilteredTemplates() {
    if (!this.searchKeyword.trim()) {
      this.filteredTemplates = [...this.templates];
    } else {
      const keyword = this.searchKeyword.toLowerCase();
      this.filteredTemplates = this.templates.filter(template =>
        template.TemplateName.toLowerCase().includes(keyword) ||
        (template.Description && template.Description.toLowerCase().includes(keyword))
      );
    }
    this.updateTemplatePagination();
  }

  // 更新模板分頁
  updateTemplatePagination() {
    this.templatePagination.totalItems = this.filteredTemplates.length;
    this.templatePagination.totalPages = Math.ceil(this.templatePagination.totalItems / this.templatePagination.pageSize);

    // 確保當前頁面不超過總頁數
    if (this.templatePagination.currentPage > this.templatePagination.totalPages) {
      this.templatePagination.currentPage = Math.max(1, this.templatePagination.totalPages);
    }

    this.updatePaginatedTemplates();
  }

  // 更新分頁後的模板列表
  updatePaginatedTemplates() {
    const startIndex = (this.templatePagination.currentPage - 1) * this.templatePagination.pageSize;
    const endIndex = startIndex + this.templatePagination.pageSize;
    this.paginatedTemplates = this.filteredTemplates.slice(startIndex, endIndex);
  }

  // 模板分頁導航
  goToTemplatePage(page: number) {
    if (page >= 1 && page <= this.templatePagination.totalPages) {
      this.templatePagination.currentPage = page;
      this.updatePaginatedTemplates();
    }
  }

  // 獲取模板分頁頁碼數組
  getTemplatePageNumbers(): number[] {
    const pages: number[] = [];
    const totalPages = this.templatePagination.totalPages;
    const currentPage = this.templatePagination.currentPage;

    // 顯示當前頁面前後各2頁
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    return pages;
  }

  // 搜尋模板
  onSearch() {
    this.updateFilteredTemplates();
  }

  // 清除搜尋
  clearSearch() {
    this.searchKeyword = '';
    this.updateFilteredTemplates();
  }







  // 查看模板
  onSelectTemplate(template: Template) {
    this.selectedTemplate = template;
    this.selectTemplate.emit(template);

    // 按需載入模板詳情
    if (template.TemplateID) {
      this.loadTemplateDetails(template.TemplateID);
    }

    this.updateDetailPagination();
  }

  // 載入模板詳情 - 使用真實的 GetTemplateDetailById API
  loadTemplateDetails(templateId: number, pageIndex: number = 1, searchKeyword?: string) {
    const args: GetTemplateDetailByIdArgs = {
      templateId: templateId
    };

    // 調用真實的 GetTemplateDetailById API
    this.templateService.apiTemplateGetTemplateDetailByIdPost$Json({
      body: args
    }).subscribe({
      next: (response) => {
        if (response.StatusCode === 0 && response.Entries) {

          // 轉換 API 回應為內部格式，並處理搜尋和分頁
          let allDetails = response.Entries.map(item => ({
            CTemplateDetailId: item.CTemplateDetailId || 0,
            CTemplateId: item.CTemplateId || templateId,
            CReleateId: item.CReleateId || 0,
            CPart: item.CPart || '',
            CLocation: item.CLocation || '', // 使用 CLocation 替代 CLocation
            CSort: undefined,
            CRemark: undefined,
            CCreateDt: new Date().toISOString(),
            CCreator: '系統',
            CCategory: undefined,
            CUnitPrice: undefined,
            CQuantity: undefined,
            CUnit: undefined
          } as TemplateDetailItem));

          // 前端搜尋篩選 (因為 API 不支援搜尋參數)
          if (searchKeyword && searchKeyword.trim()) {
            allDetails = allDetails.filter(detail =>
              detail.CPart.toLowerCase().includes(searchKeyword.toLowerCase()) ||
              (detail.CLocation && detail.CLocation.toLowerCase().includes(searchKeyword.toLowerCase()))
            );
          }

          // 前端分頁處理 (因為 API 不支援分頁參數)
          const startIndex = (pageIndex - 1) * this.detailPagination.pageSize;
          const endIndex = startIndex + this.detailPagination.pageSize;
          const pagedDetails = allDetails.slice(startIndex, endIndex);

          // 更新詳情資料和分頁資訊
          this.currentTemplateDetailsData = pagedDetails;
          this.detailPagination.totalItems = allDetails.length;
          this.detailPagination.totalPages = Math.ceil(allDetails.length / this.detailPagination.pageSize);
          this.detailPagination.currentPage = pageIndex;
        } else {
          this.currentTemplateDetailsData = [];
          this.detailPagination.totalItems = 0;
          this.detailPagination.totalPages = 0;
          this.detailPagination.currentPage = 1;
        }
      },
      error: () => {
        this.currentTemplateDetailsData = [];
        this.detailPagination.totalItems = 0;
        this.detailPagination.totalPages = 0;
        this.detailPagination.currentPage = 1;
      }
    });
  }



  // 搜尋模板詳情 (明細專用搜尋)
  searchTemplateDetails(keyword: string) {
    this.detailSearchKeyword = keyword;
    if (this.selectedTemplate && this.selectedTemplate.TemplateID) {
      this.loadTemplateDetails(this.selectedTemplate.TemplateID, 1, keyword);
    }
  }

  // 清除明細搜尋
  clearDetailSearch() {
    this.detailSearchKeyword = '';
    if (this.selectedTemplate && this.selectedTemplate.TemplateID) {
      this.loadTemplateDetails(this.selectedTemplate.TemplateID, 1);
    }
  }

  // 明細搜尋輸入事件處理
  onDetailSearchInput() {
    // 可以在這裡添加即時搜尋邏輯，目前保持原有的 Enter 鍵搜尋方式
  }

  // 明細搜尋事件處理（即時搜尋）
  onDetailSearch() {
    if (this.selectedTemplate && this.selectedTemplate.TemplateID) {
      this.loadTemplateDetails(this.selectedTemplate.TemplateID, 1, this.detailSearchKeyword);
    }
  }

  // 更新詳情分頁 (保留向後相容)
  updateDetailPagination() {
    // 如果使用新的詳情資料，直接返回
    if (this.currentTemplateDetailsData.length > 0) {
      return;
    }

    // 向後相容：使用舊的詳情資料
    const details = this.currentTemplateDetails;
    this.detailPagination.totalItems = details.length;
    this.detailPagination.totalPages = Math.ceil(this.detailPagination.totalItems / this.detailPagination.pageSize);
    this.detailPagination.currentPage = 1; // 重置到第一頁
    this.updatePaginatedDetails();
  }

  // 更新分頁後的詳情列表
  updatePaginatedDetails() {
    const details = this.currentTemplateDetails;
    const startIndex = (this.detailPagination.currentPage - 1) * this.detailPagination.pageSize;
    const endIndex = startIndex + this.detailPagination.pageSize;
    this.paginatedDetails = details.slice(startIndex, endIndex);
  }

  // 詳情分頁導航
  goToDetailPage(page: number) {
    if (page >= 1 && page <= this.detailPagination.totalPages) {
      // 如果使用新的詳情資料，重新載入
      if (this.selectedTemplate && this.selectedTemplate.TemplateID && this.currentTemplateDetailsData.length > 0) {
        this.loadTemplateDetails(this.selectedTemplate.TemplateID, page, this.detailSearchKeyword);
      } else {
        // 向後相容：使用舊的分頁邏輯
        this.detailPagination.currentPage = page;
        this.updatePaginatedDetails();
      }
    }
  }

  // 獲取詳情分頁頁碼數組
  getDetailPageNumbers(): number[] {
    const pages: number[] = [];
    const totalPages = this.detailPagination.totalPages;
    const currentPage = this.detailPagination.currentPage;

    // 顯示當前頁面前後各2頁
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    return pages;
  }

  // 關閉模板查看器
  onClose() {
    this.close.emit();
  }

  // 刪除模板
  onDeleteTemplate(templateID: number) {
    if (confirm('確定刪除此模板？')) {
      // TODO: 替換為實際的API調用
      this.deleteTemplateById(templateID);
    }
  }

  // 刪除模板 - 使用真實的 API 調用
  deleteTemplateById(templateID: number) {
    // 準備 API 請求參數
    const deleteTemplateArgs: GetTemplateByIdArgs = {
      CTemplateId: templateID
    };

    // 調用 DeleteTemplate API
    this.templateService.apiTemplateDeleteTemplatePost$Json({
      body: deleteTemplateArgs
    }).subscribe({
      next: (response) => {
        if (response.StatusCode === 0) {
          // API 調用成功
          // 重新載入模板列表
          this.loadTemplates();

          // 如果當前查看的模板被刪除，關閉詳情
          if (this.selectedTemplate?.TemplateID === templateID) {
            this.selectedTemplate = null;
          }
        } else {
          // API 返回錯誤
        }
      },
      error: () => {
        // HTTP 請求錯誤
      }
    });
  }

  /*
   * API 設計說明：
   *
   * 1. 載入模板列表 API:
   *    GET /api/Template/GetTemplateList
   *    請求體: { CTemplateType: number, PageIndex: number, PageSize: number }
   *    返回: { templates: Template[], templateDetails: TemplateDetail[] }
   *
   * 2. 創建模板 API:
   *    POST /api/Template/SaveTemplate
   *    請求體: {
   *      CTemplateName: string,
   *      CTemplateType: number,  // 1=客變需求
   *      CStatus: number,
   *      Details: [{
   *        CReleateId: number,        // 關聯主檔ID
   *        CPart: string       // 關聯名稱
   *      }]
   *    }
   *
   * 3. 刪除模板 API:
   *    POST /api/Template/DeleteTemplate
   *    請求體: { CTemplateId: number }
   *
   * 資料庫設計重點：
   * - template 表：存放模板基本資訊 (CTemplateId, CTemplateName, CTemplateType, CStatus)
   * - templatedetail 表：存放模板詳情 (CTemplateDetailId, CTemplateId, CReleateId, CPart)
   * - CTemplateType 欄位用於區分不同模板類型 (1=客變需求)
   * - CReleateId 存放關聯主檔的ID，CReleateName 存放關聯名稱
   */

  // 關閉模板詳情
  closeTemplateDetail() {
    this.selectedTemplate = null;
  }

  // 取得當前選中模板的詳情
  get currentTemplateDetails(): TemplateDetail[] {
    if (!this.selectedTemplate) {
      return [];
    }
    return this.templateDetails.filter(d => d.TemplateID === this.selectedTemplate!.TemplateID);
  }

  // TrackBy函數用於優化ngFor性能
  trackByTemplateId(index: number, template: Template): number {
    return template.TemplateID || index;
  }
}

// DB 對應型別
export interface Template {
  TemplateID?: number;
  TemplateName: string;
  Description?: string;
  CreateTime?: string;
  UpdateTime?: string;
  Creator?: string;
  Updator?: string;
}
export interface TemplateDetail {
  TemplateDetailID?: number;
  TemplateID: number;
  RefID: number; // 關聯主檔ID
  CTemplateType: number; // 模板類型，1=客變需求
  FieldName: string;
  FieldValue: string;
}

// 擴展的詳情項目結構 (基於 API 的 TemplateDetailItem 擴展)
export interface TemplateDetailItem {
  CTemplateDetailId: number;
  CTemplateId: number;
  CReleateId: number;            // 關聯主檔ID
  CPart: string;          // 關聯名稱
  CLocation?: string;           // 群組名稱 (API 新增欄位)
  CSort?: number;                // 排序順序
  CRemark?: string;              // 備註
  CCreateDt: string;             // 建立時間
  CCreator: string;              // 建立者
  CUpdateDt?: string;            // 更新時間
  CUpdator?: string;             // 更新者

  // 業務相關欄位 (依需求添加，部分由 API 提供)
  CUnitPrice?: number;           // 單價
  CQuantity?: number;            // 數量
  CUnit?: string;                // 單位
  CCategory?: string;            // 分類
}

// 注意：GetTemplateDetailById API 使用的是 GetTemplateDetailByIdArgs 和 TemplateDetailItemListResponseBase
// 這些 interface 已經在 API models 中定義，不需要重複定義
