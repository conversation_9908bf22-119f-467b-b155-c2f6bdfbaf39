import { Component, EventEmitter, Input, Output, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { NbCardModule, NbButtonModule } from '@nebular/theme';
import { TemplateService } from 'src/services/api/services/template.service';
import { SaveTemplateArgs, SaveTemplateDetailArgs } from 'src/services/api/models';

// 選中項目的介面定義
interface SelectedItem {
  CLocation: string | null;
  CReleateName: string | null;
  CReleateId: number;
}

@Component({
  selector: 'app-template-creator',
  templateUrl: './template-creator.component.html',
  styleUrls: ['./template-creator.component.scss'],
  standalone: true,
  imports: [CommonModule, FormsModule, NbCardModule, NbButtonModule],
})
export class TemplateCreatorComponent implements OnInit {
  @Input() availableData: any[] = []; // 父元件傳入的可選資料
  @Input() templateType: number = 1; // 模板類型，1=客變需求
  @Output() close = new EventEmitter<void>(); // 關閉事件
  @Output() templateCreated = new EventEmitter<void>(); // 模板創建成功事件

  // 新增模板表單
  newTemplate = {
    name: ''
  };

  // 表單驗證狀態
  isSubmitting = false;
  validationErrors: { [key: string]: string } = {};

  constructor(private templateService: TemplateService) { }

  ngOnInit() {
    // 初始化時重置所有選擇狀態
    this.resetForm();
  }

  // 重置表單
  resetForm() {
    this.newTemplate = {
      name: ''
    };
    this.validationErrors = {};
    this.isSubmitting = false;

    // 重置選擇狀態
    if (this.availableData) {
      this.availableData.forEach(item => item.selected = false);
    }
  }

  // 驗證表單
  validateForm(): boolean {
    this.validationErrors = {};
    let isValid = true;

    // 驗證模板名稱
    if (!this.newTemplate.name.trim()) {
      this.validationErrors['name'] = '請輸入模板名稱';
      isValid = false;
    } else if (this.newTemplate.name.trim().length > 50) {
      this.validationErrors['name'] = '模板名稱不能超過50個字符';
      isValid = false;
    }



    // 驗證是否選擇了項目
    const selectedItems = this.getSelectedItems();
    if (selectedItems.length === 0) {
      this.validationErrors['items'] = '請至少選擇一個項目';
      isValid = false;
    }

    return isValid;
  }

  // 獲取選中的項目
  getSelectedItems(): SelectedItem[] {
    if (!this.availableData) return [];

    return this.availableData
      .filter(item => item.selected)
      .map(item => ({
        CLocation: item.CLocation || null,
        CReleateName: item.CReleateName || item.CRequirement || item.name || null,
        CReleateId: item.CReleateId || item.CRequirementID || item.ID || item.id || 0
      }));
  }

  // 儲存新模板
  saveTemplate() {
    if (!this.validateForm()) {
      return;
    }

    this.isSubmitting = true;
    const selectedItems = this.getSelectedItems();

    // 準備 API 請求資料
    const saveTemplateArgs: SaveTemplateArgs = {
      CTemplateId: null, // 新增時為 null
      CTemplateName: this.newTemplate.name.trim(),
      CTemplateType: this.templateType, // 1=客變需求
      CStatus: 1, // 啟用狀態
      Details: selectedItems.map(item => ({
        CTemplateDetailId: null, // 新增時為 null
        CReleateId: item.CReleateId, // 關聯主檔ID
        CReleateName: item.CReleateName, // 關聯名稱
        CLocation: item.CLocation // 群組名稱
      } as SaveTemplateDetailArgs))
    };

    // 調用 SaveTemplate API
    this.templateService.apiTemplateSaveTemplatePost$Json({
      body: saveTemplateArgs
    }).subscribe({
      next: (response) => {
        this.isSubmitting = false;
        if (response.StatusCode === 0) {
          // API 調用成功
          this.templateCreated.emit(); // 通知父組件模板創建成功
          this.close.emit(); // 關閉對話框
        } else {
          // API 返回錯誤
          this.validationErrors['api'] = response.Message || '保存失敗，請稍後重試';
        }
      },
      error: (error) => {
        this.isSubmitting = false;
        this.validationErrors['api'] = '網絡錯誤，請檢查網絡連接後重試';
        console.error('保存模板失敗:', error);
      }
    });
  }

  // 取消操作
  cancel() {
    this.close.emit();
  }

  // 獲取選中項目數量
  getSelectedCount(): number {
    return this.availableData ? this.availableData.filter(item => item.selected).length : 0;
  }

  // 切換項目選擇狀態
  toggleItemSelection(item: any) {
    item.selected = !item.selected;
    // 清除項目選擇相關的驗證錯誤
    if (this.validationErrors['items'] && this.getSelectedCount() > 0) {
      delete this.validationErrors['items'];
    }
  }

  // 全選/取消全選
  toggleSelectAll() {
    const hasUnselected = this.availableData.some(item => !item.selected);
    this.availableData.forEach(item => item.selected = hasUnselected);

    // 清除項目選擇相關的驗證錯誤
    if (this.validationErrors['items'] && this.getSelectedCount() > 0) {
      delete this.validationErrors['items'];
    }
  }

  // 檢查是否全選
  isAllSelected(): boolean {
    return this.availableData && this.availableData.length > 0 &&
      this.availableData.every(item => item.selected);
  }

  // 檢查是否部分選中
  isIndeterminate(): boolean {
    const selectedCount = this.getSelectedCount();
    return selectedCount > 0 && selectedCount < this.availableData.length;
  }
}
