/**
 * @license
 * Copyright Akveo. All Rights Reserved.
 * Licensed under the MIT License. See License.txt in the project root for license information.
 */

@import '_colors';

@mixin ngx-pace-theme() {

  .pace .pace-progress {
    background: $gradient-primary;
    height: 3px;
  }

  .pace .pace-progress-inner {
    box-shadow: 0 0 10px $primary-gold-light, 0 0 5px $primary-gold-light;
  }

  .pace .pace-activity {
    display: none;
  }

  // 增強載入進度條樣式
  .pace {
    pointer-events: none;
    user-select: none;
    z-index: 2000;
    position: fixed;
    margin: auto;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    border-radius: 0 0 2px 2px;
    overflow: hidden;
    background: rgba(184, 166, 118, 0.1);
  }

  .pace .pace-progress {
    transform: translate3d(0, 0, 0);
    max-width: 200px;
    position: fixed;
    z-index: 2000;
    display: block;
    position: absolute;
    top: 0;
    right: 100%;
    width: 100%;
    height: 100%;
    background: $gradient-primary;
    animation: pace-progress-animation 1s ease-in-out infinite;
  }

  @keyframes pace-progress-animation {
    0% {
      transform: translateX(0) scaleX(0);
    }

    40% {
      transform: translateX(0) scaleX(0.4);
    }

    100% {
      transform: translateX(100%) scaleX(0.5);
    }
  }
}