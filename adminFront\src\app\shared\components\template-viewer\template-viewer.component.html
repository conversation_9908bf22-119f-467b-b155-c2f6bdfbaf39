<nb-card class="template-viewer-card">
  <nb-card-header class="template-viewer-header">
    <div class="d-flex justify-content-between align-items-center">
      <div class="header-title">
        <h5 class="mb-0">
          <i class="fas fa-layer-group mr-2 text-primary"></i>模板管理
        </h5>
        <small class="text-muted">管理和查看客變需求模板</small>
      </div>
      <div class="header-actions">
        <span class="badge badge-info">{{ templatePagination.totalItems }} 個模板</span>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body class="template-viewer-body">
    <!-- 增強的搜尋功能 (僅在模板列表時顯示) -->
    <div class="enhanced-search-container mb-4" *ngIf="!selectedTemplate">
      <div class="search-wrapper">
        <div class="search-input-group">
          <div class="search-icon">
            <i class="fas fa-search"></i>
          </div>
          <input type="text" class="search-input" placeholder="搜尋模板名稱、描述或關鍵字..." [(ngModel)]="searchKeyword"
            (input)="onSearch()" (keyup.enter)="onSearch()">
          <div class="search-actions" *ngIf="searchKeyword">
            <button class="clear-search-btn" type="button" (click)="clearSearch()" title="清除搜尋">
              <i class="fas fa-times"></i>
            </button>
          </div>
        </div>
        <div class="search-suggestions" *ngIf="searchKeyword && filteredTemplates.length > 0">
          <small class="text-success">
            <i class="fas fa-check-circle mr-1"></i>找到 {{ filteredTemplates.length }} 個相關模板
          </small>
        </div>
        <div class="search-no-results" *ngIf="searchKeyword && filteredTemplates.length === 0">
          <small class="text-warning">
            <i class="fas fa-exclamation-triangle mr-1"></i>未找到符合條件的模板
          </small>
        </div>
      </div>
    </div>



    <!-- 優化的模板列表（僅在未選擇模板細節時顯示） -->
    <div class="template-list-container" *ngIf="!selectedTemplate">
      <!-- 列表控制欄 -->
      <div class="list-controls mb-3" *ngIf="templatePagination.totalItems > 0">
        <div class="d-flex justify-content-between align-items-center">
          <div class="list-info">
            <span class="info-text">
              顯示第 {{ (templatePagination.currentPage - 1) * templatePagination.pageSize + 1 }} -
              {{ Math.min(templatePagination.currentPage * templatePagination.pageSize, templatePagination.totalItems)
              }} 項，
              共 {{ templatePagination.totalItems }} 項模板
            </span>
          </div>
          <div class="view-options">
            <small class="text-muted">第 {{ templatePagination.currentPage }} / {{ templatePagination.totalPages }}
              頁</small>
          </div>
        </div>
      </div>

      <!-- 簡潔模板列表 -->
      <div class="template-list-container">
        <div class="template-item" *ngFor="let tpl of paginatedTemplates; trackBy: trackByTemplateId">
          <div class="template-main-info">
            <div class="template-header">
              <span class="template-label">模板名稱</span>
              <h6 class="template-name">
                <i class="fas fa-file-alt mr-2"></i>
                {{ tpl.TemplateName }}
              </h6>
            </div>
            <div class="template-meta">
              <div class="meta-row" *ngIf="tpl.CreateTime">
                <span class="meta-label">
                  <i class="fas fa-calendar-plus mr-1"></i>建立日期：
                </span>
                <span class="meta-value">{{ tpl.CreateTime | date:'yyyy/MM/dd HH:mm' }}</span>
              </div>
              <div class="meta-row" *ngIf="tpl.UpdateTime">
                <span class="meta-label">
                  <i class="fas fa-clock mr-1"></i>更新時間：
                </span>
                <span class="meta-value">{{ tpl.UpdateTime | date:'yyyy/MM/dd HH:mm' }}</span>
              </div>
            </div>
          </div>
          <div class="template-actions">
            <button class="action-btn view-btn" (click)="onSelectTemplate(tpl)" title="查看詳情">
              <i class="fas fa-eye"></i>
              <span>查看</span>
            </button>
            <button class="action-btn delete-btn" (click)="tpl.TemplateID && onDeleteTemplate(tpl.TemplateID)"
              *ngIf="tpl.TemplateID" title="刪除模板">
              <i class="fas fa-trash"></i>
              <span>刪除</span>
            </button>
          </div>
        </div>

        <!-- 空狀態 -->
        <div class="empty-state-card" *ngIf="!paginatedTemplates || paginatedTemplates.length === 0">
          <div class="empty-content">
            <div class="empty-icon">
              <i class="fas fa-search" *ngIf="searchKeyword"></i>
              <i class="fas fa-folder-open" *ngIf="!searchKeyword"></i>
            </div>
            <h6 class="empty-title">
              {{ searchKeyword ? '找不到符合條件的模板' : '暫無模板' }}
            </h6>
            <p class="empty-description" *ngIf="searchKeyword">
              請嘗試其他關鍵字或
              <a href="javascript:void(0)" (click)="clearSearch()" class="clear-link">清除搜尋條件</a>
            </p>
            <p class="empty-description" *ngIf="!searchKeyword">
              目前還沒有建立任何模板，請先建立模板
            </p>
          </div>
        </div>
      </div>

      <!-- 優化的分頁控制器 -->
      <div class="enhanced-pagination-container mt-4" *ngIf="templatePagination.totalPages > 1">
        <div class="pagination-wrapper">
          <div class="pagination-info">
            <span class="page-info">
              第 {{ templatePagination.currentPage }} 頁，共 {{ templatePagination.totalPages }} 頁
            </span>
          </div>
          <nav aria-label="模板列表分頁" class="pagination-nav">
            <ul class="enhanced-pagination">
              <!-- 第一頁 -->
              <li class="page-item" [class.disabled]="templatePagination.currentPage === 1">
                <button class="page-btn first-page" (click)="goToTemplatePage(1)"
                  [disabled]="templatePagination.currentPage === 1" title="第一頁">
                  <i class="fas fa-angle-double-left"></i>
                </button>
              </li>

              <!-- 上一頁 -->
              <li class="page-item" [class.disabled]="templatePagination.currentPage === 1">
                <button class="page-btn prev-page" (click)="goToTemplatePage(templatePagination.currentPage - 1)"
                  [disabled]="templatePagination.currentPage === 1" title="上一頁">
                  <i class="fas fa-chevron-left"></i>
                </button>
              </li>

              <!-- 頁碼 -->
              <li class="page-item" *ngFor="let page of getTemplatePageNumbers()"
                [class.active]="page === templatePagination.currentPage">
                <button class="page-btn page-number" (click)="goToTemplatePage(page)">{{ page }}</button>
              </li>

              <!-- 下一頁 -->
              <li class="page-item" [class.disabled]="templatePagination.currentPage === templatePagination.totalPages">
                <button class="page-btn next-page" (click)="goToTemplatePage(templatePagination.currentPage + 1)"
                  [disabled]="templatePagination.currentPage === templatePagination.totalPages" title="下一頁">
                  <i class="fas fa-chevron-right"></i>
                </button>
              </li>

              <!-- 最後一頁 -->
              <li class="page-item" [class.disabled]="templatePagination.currentPage === templatePagination.totalPages">
                <button class="page-btn last-page" (click)="goToTemplatePage(templatePagination.totalPages)"
                  [disabled]="templatePagination.currentPage === templatePagination.totalPages" title="最後一頁">
                  <i class="fas fa-angle-double-right"></i>
                </button>
              </li>
            </ul>
          </nav>
        </div>
      </div>
    </div>

    <!-- 優化的模板詳情視圖 -->
    <div *ngIf="selectedTemplate" class="template-detail-view">
      <!-- 詳情標題欄 -->
      <div class="detail-header">
        <div class="detail-title-section">
          <div class="back-button">
            <button class="back-btn" (click)="closeTemplateDetail()" title="返回模板列表">
              <i class="fas fa-arrow-left"></i>
            </button>
          </div>
          <div class="detail-title-info">
            <h5 class="detail-title">
              <i class="fas fa-file-alt mr-2 text-primary"></i>
              {{ selectedTemplate!.TemplateName }}
            </h5>
            <p class="detail-subtitle" *ngIf="selectedTemplate.Description">
              {{ selectedTemplate.Description }}
            </p>
          </div>
        </div>
        <div class="detail-stats">
          <div class="stat-item">
            <span class="stat-label">項目數量</span>
            <span class="stat-value">{{ currentTemplateDetailsData.length > 0 ? detailPagination.totalItems :
              currentTemplateDetails.length }}</span>
          </div>
          <div class="stat-item" *ngIf="detailPagination.totalPages > 1">
            <span class="stat-label">頁數</span>
            <span class="stat-value">{{ detailPagination.currentPage }} / {{ detailPagination.totalPages }}</span>
          </div>
        </div>
      </div>

      <!-- 明細專用搜尋區域 -->
      <div class="enhanced-search-container mb-4">
        <div class="search-wrapper">
          <div class="search-input-group">
            <div class="search-icon">
              <i class="fas fa-search"></i>
            </div>
            <input type="text" class="search-input" placeholder="搜尋明細項目名稱、群組..." [(ngModel)]="detailSearchKeyword"
              (input)="onDetailSearch()" (keyup.enter)="onDetailSearch()">
            <div class="search-actions" *ngIf="detailSearchKeyword">
              <button class="clear-search-btn" type="button" (click)="clearDetailSearch()" title="清除搜尋">
                <i class="fas fa-times"></i>
              </button>
            </div>
          </div>
          <div class="search-suggestions" *ngIf="detailSearchKeyword && detailPagination.totalItems > 0">
            <small class="text-success">
              <i class="fas fa-check-circle mr-1"></i>找到 {{ detailPagination.totalItems }} 個相關項目
            </small>
          </div>
          <div class="search-no-results" *ngIf="detailSearchKeyword && detailPagination.totalItems === 0">
            <small class="text-warning">
              <i class="fas fa-exclamation-triangle mr-1"></i>未找到符合條件的項目
            </small>
          </div>
        </div>
      </div>

      <!-- 詳情內容區域 -->
      <div class="detail-content">


        <!-- 優化的詳情項目顯示 -->
        <div *ngIf="currentTemplateDetailsData.length > 0; else checkOldDetails" class="enhanced-detail-list">
          <div *ngFor="let detail of currentTemplateDetailsData; let i = index" class="enhanced-detail-item">
            <div class="detail-item-card">
              <div class="detail-item-header">
                <div class="item-index">
                  <span class="index-badge">{{ (detailPagination.currentPage - 1) * detailPagination.pageSize + i + 1
                    }}</span>
                </div>
                <div class="item-main-info">
                  <h6 class="item-name">
                    <i class="fas fa-cog mr-2 text-secondary"></i>
                    {{ detail.CPart }}
                  </h6>
                  <div class="item-meta">
                    <span class="meta-item id-meta">
                      <i class="fas fa-hashtag"></i>
                      <span>{{ detail.CReleateId }}</span>
                    </span>
                    <span class="meta-item group-meta" *ngIf="detail.CLocation">
                      <i class="fas fa-layer-group"></i>
                      <span>{{ detail.CLocation }}</span>
                    </span>
                    <span class="meta-item category-meta" *ngIf="detail.CCategory">
                      <i class="fas fa-tag"></i>
                      <span>{{ detail.CCategory }}</span>
                    </span>
                  </div>
                </div>
                <div class="item-actions">
                  <span class="create-date">{{ detail.CCreateDt | date:'MM/dd' }}</span>
                </div>
              </div>

              <div class="detail-item-body"
                *ngIf="detail.CUnitPrice || detail.CQuantity || detail.CUnit || detail.CRemark">
                <div class="item-details-grid">
                  <div class="detail-group price-group" *ngIf="detail.CUnitPrice">
                    <span class="detail-label">單價</span>
                    <span class="detail-value price-value">NT$ {{ detail.CUnitPrice | number }}</span>
                  </div>
                  <div class="detail-group quantity-group" *ngIf="detail.CQuantity && detail.CUnit">
                    <span class="detail-label">數量</span>
                    <span class="detail-value quantity-value">{{ detail.CQuantity }} {{ detail.CUnit }}</span>
                  </div>
                </div>
                <div class="item-remark" *ngIf="detail.CRemark">
                  <div class="remark-content">
                    <i class="fas fa-comment-alt mr-2 text-muted"></i>
                    <span class="remark-text">{{ detail.CRemark }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 向後相容：舊的詳情資料顯示 -->
        <ng-template #checkOldDetails>
          <div *ngIf="currentTemplateDetails.length > 0; else noDetails" class="detail-list">
            <div *ngFor="let detail of paginatedDetails; let i = index"
              class="detail-item d-flex align-items-center py-2 border-bottom">
              <div class="detail-index">
                <span class="badge badge-light">{{ (detailPagination.currentPage - 1) * detailPagination.pageSize + i
                  + 1 }}</span>
              </div>
              <div class="detail-content flex-grow-1 ml-2">
                <div class="detail-field">
                  <strong>{{ detail.FieldName }}:</strong>
                </div>
                <div class="detail-value text-muted">
                  {{ detail.FieldValue }}
                </div>
              </div>
            </div>
          </div>
        </ng-template>

        <!-- 詳情分頁控制器 -->
        <div class="detail-pagination mt-3" *ngIf="detailPagination.totalPages > 1">
          <nav aria-label="模板詳情分頁">
            <ul class="pagination pagination-sm justify-content-center mb-0">
              <!-- 上一頁 -->
              <li class="page-item" [class.disabled]="detailPagination.currentPage === 1">
                <button class="page-link" (click)="goToDetailPage(detailPagination.currentPage - 1)"
                  [disabled]="detailPagination.currentPage === 1">
                  <i class="fas fa-chevron-left"></i>
                </button>
              </li>

              <!-- 頁碼 -->
              <li class="page-item" *ngFor="let page of getDetailPageNumbers()"
                [class.active]="page === detailPagination.currentPage">
                <button class="page-link" (click)="goToDetailPage(page)">{{ page }}</button>
              </li>

              <!-- 下一頁 -->
              <li class="page-item" [class.disabled]="detailPagination.currentPage === detailPagination.totalPages">
                <button class="page-link" (click)="goToDetailPage(detailPagination.currentPage + 1)"
                  [disabled]="detailPagination.currentPage === detailPagination.totalPages">
                  <i class="fas fa-chevron-right"></i>
                </button>
              </li>
            </ul>
          </nav>
        </div>

        <ng-template #noDetails>
          <div class="text-center py-3">
            <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
            <p class="text-muted mb-0">此模板暫無內容</p>
          </div>
        </ng-template>
      </div>
    </div>
  </nb-card-body>
  <nb-card-footer class="template-viewer-footer">
    <div class="footer-actions">
      <button class="close-btn" (click)="onClose()">
        <i class="fas fa-times mr-2"></i>關閉
      </button>
    </div>
  </nb-card-footer>
</nb-card>