<nb-card style="width: 90vw; max-width: 800px; height: 80vh;">
  <nb-card-header>
    <div class="d-flex justify-content-between align-items-center">
      <h5 class="mb-0">
        <i class="fas fa-plus mr-2"></i>新增模板
      </h5>
      <button class="btn btn-outline-secondary btn-sm" (click)="cancel()">
        <i class="fas fa-times"></i>
      </button>
    </div>
  </nb-card-header>

  <nb-card-body style="overflow: auto;">
    <!-- API 錯誤提示 -->
    <div class="alert alert-danger" *ngIf="validationErrors['api']">
      <i class="fas fa-exclamation-triangle mr-2"></i>
      {{ validationErrors['api'] }}
    </div>

    <form (ngSubmit)="saveTemplate()" class="template-form">
      <!-- 模板基本資訊 -->
      <div class="form-section mb-4">
        <h6 class="section-title">
          <i class="fas fa-info-circle mr-2"></i>模板資訊
        </h6>

        <div class="input-group">
          <label class="input-label">
            模板名稱 <span class="required">*</span>
          </label>
          <input type="text" class="input-field" [class.is-invalid]="validationErrors['name']"
            [(ngModel)]="newTemplate.name" name="templateName" placeholder="請輸入模板名稱" maxlength="50"
            [disabled]="isSubmitting">
          <div class="invalid-feedback" *ngIf="validationErrors['name']">
            {{ validationErrors['name'] }}
          </div>
        </div>
      </div>

      <!-- 項目選擇 -->
      <div class="form-section">
        <div class="d-flex justify-content-between align-items-center mb-3">
          <h6 class="section-title mb-0">
            <i class="fas fa-list mr-2"></i>選擇要加入模板的項目 <span class="required">*</span>
          </h6>
          <div class="selection-summary">
            <span class="badge badge-primary">已選擇 {{ getSelectedCount() }} 項</span>
          </div>
        </div>

        <!-- 全選控制 -->
        <div class="select-all-control mb-3" *ngIf="availableData.length > 0">
          <label class="select-all-label">
            <input type="checkbox" class="select-all-checkbox" [checked]="isAllSelected()"
              [indeterminate]="isIndeterminate()" (change)="toggleSelectAll()" [disabled]="isSubmitting">
            <span class="checkmark"></span>
            <span class="select-all-text">
              {{ isAllSelected() ? '取消全選' : '全選' }}
            </span>
          </label>
        </div>

        <!-- 項目列表 -->
        <div class="items-selector" [class.has-error]="validationErrors['items']">
          <div *ngIf="availableData.length === 0" class="empty-items">
            <i class="fas fa-info-circle"></i>
            <span>暫無可選項目</span>
          </div>

          <div *ngFor="let item of availableData; let i = index" class="item-option">
            <label class="item-label" (click)="toggleItemSelection(item)">
              <input type="checkbox" class="item-checkbox" [(ngModel)]="item.selected" name="item_{{i}}"
                [disabled]="isSubmitting" (click)="$event.stopPropagation()">
              <div class="item-content">
                <div class="item-title">{{ item.CRequirement || item.name }}</div>
                <div class="item-desc">{{ item.CLocation || item.description }}</div>
              </div>
            </label>
          </div>
        </div>

        <div class="invalid-feedback d-block" *ngIf="validationErrors['items']">
          <i class="fas fa-exclamation-triangle mr-1"></i>
          {{ validationErrors['items'] }}
        </div>
      </div>
    </form>
  </nb-card-body>

  <nb-card-footer>
    <div class="form-actions d-flex justify-content-end">
      <button type="button" class="btn btn-outline-secondary mr-2" (click)="cancel()" [disabled]="isSubmitting">
        <i class="fas fa-times mr-1"></i>取消
      </button>
      <button type="button" class="btn btn-success" (click)="saveTemplate()" [disabled]="isSubmitting">
        <i class="fas fa-spinner fa-spin mr-1" *ngIf="isSubmitting"></i>
        <i class="fas fa-save mr-1" *ngIf="!isSubmitting"></i>
        {{ isSubmitting ? '保存中...' : '儲存模板' }}
      </button>
    </div>
  </nb-card-footer>
</nb-card>